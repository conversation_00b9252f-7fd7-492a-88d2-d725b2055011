"""
Application configuration settings
"""

class AppConfig:
    APP_NAME = "Gema"
    APP_VERSION = "1.0.0"
    APP_DESCRIPTION = "Metadata Editor for Media Files"
    
    # Window settings
    WINDOW_WIDTH = 900
    WINDOW_HEIGHT = 700
    WINDOW_MIN_WIDTH = 800
    WINDOW_MIN_HEIGHT = 600
    
    # Supported file formats
    SUPPORTED_IMAGE_FORMATS = ['.jpg', '.jpeg', '.png', '.tiff', '.tif', '.bmp', '.gif']
    SUPPORTED_VIDEO_FORMATS = ['.mp4', '.avi', '.mov', '.mkv', '.wmv', '.flv', '.webm']
    SUPPORTED_AUDIO_FORMATS = ['.mp3', '.flac', '.ogg', '.m4a', '.wav']
    
    @classmethod
    def get_all_supported_formats(cls):
        return cls.SUPPORTED_IMAGE_FORMATS + cls.SUPPORTED_VIDEO_FORMATS + cls.SUPPORTED_AUDIO_FORMATS
    
    @classmethod
    def is_supported_format(cls, file_extension):
        return file_extension.lower() in cls.get_all_supported_formats()
    
    @classmethod
    def get_file_type(cls, file_extension):
        ext = file_extension.lower()
        if ext in cls.SUPPORTED_IMAGE_FORMATS:
            return 'image'
        elif ext in cls.SUPPORTED_VIDEO_FORMATS:
            return 'video'
        elif ext in cls.SUPPORTED_AUDIO_FORMATS:
            return 'audio'
        return 'unknown'
