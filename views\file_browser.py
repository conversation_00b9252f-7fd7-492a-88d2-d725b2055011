"""
File browser component for selecting media files
"""

import tkinter as tk
from tkinter import ttk, filedialog
import os
from services.settings_service import SettingsService

class FileBrowser(tk.Frame):
    def __init__(self, parent, theme_service, on_file_selected_callback):
        super().__init__(parent)
        self.theme_service = theme_service
        self.on_file_selected = on_file_selected_callback
        self.settings_service = SettingsService()
        self.current_path = self.settings_service.get_last_directory()
        
        self.supported_extensions = {
            '.jpg', '.jpeg', '.png', '.tiff', '.tif', '.bmp', '.gif',
            '.mp4', '.avi', '.mov', '.mkv', '.wmv', '.flv', '.webm',
            '.mp3', '.flac', '.ogg', '.m4a', '.wav'
        }
        
        self.create_widgets()
        self.load_directory(self.current_path)
    
    def create_widgets(self):
        # Top frame for path and buttons
        self.top_frame = tk.Frame(self)
        self.top_frame.pack(fill=tk.X, pady=(0, 10))
        
        # Path frame
        self.path_frame = tk.Frame(self.top_frame)
        self.path_frame.pack(fill=tk.X, pady=(0, 5))
        
        tk.Label(self.path_frame, text="Current Path:", font=("Arial", 10, "bold")).pack(side=tk.LEFT)
        
        self.path_var = tk.StringVar(value=self.current_path)
        self.path_entry = tk.Entry(self.path_frame, textvariable=self.path_var, font=("Arial", 9))
        self.path_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(10, 5))
        self.path_entry.bind('<Return>', self.on_path_enter)
        
        self.browse_button = tk.Button(
            self.path_frame,
            text="Browse",
            command=self.browse_folder,
            font=("Arial", 9)
        )
        self.browse_button.pack(side=tk.RIGHT)
        
        # Navigation buttons
        self.nav_frame = tk.Frame(self.top_frame)
        self.nav_frame.pack(fill=tk.X)
        
        self.up_button = tk.Button(
            self.nav_frame,
            text="⬆️ Up",
            command=self.go_up,
            font=("Arial", 9)
        )
        self.up_button.pack(side=tk.LEFT, padx=(0, 5))
        
        self.home_button = tk.Button(
            self.nav_frame,
            text="🏠 Home",
            command=self.go_home,
            font=("Arial", 9)
        )
        self.home_button.pack(side=tk.LEFT, padx=(0, 5))
        
        self.refresh_button = tk.Button(
            self.nav_frame,
            text="🔄 Refresh",
            command=self.refresh,
            font=("Arial", 9)
        )
        self.refresh_button.pack(side=tk.LEFT)
        
        # File list frame
        self.list_frame = tk.Frame(self)
        self.list_frame.pack(fill=tk.BOTH, expand=True)
        
        # Create treeview for file listing
        self.tree = ttk.Treeview(self.list_frame, columns=('size', 'type'), show='tree headings')
        self.tree.heading('#0', text='Name')
        self.tree.heading('size', text='Size')
        self.tree.heading('type', text='Type')
        
        self.tree.column('#0', width=400)
        self.tree.column('size', width=100)
        self.tree.column('type', width=100)
        
        # Scrollbars
        v_scrollbar = ttk.Scrollbar(self.list_frame, orient=tk.VERTICAL, command=self.tree.yview)
        h_scrollbar = ttk.Scrollbar(self.list_frame, orient=tk.HORIZONTAL, command=self.tree.xview)
        self.tree.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)
        
        # Pack treeview and scrollbars
        self.tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        v_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        h_scrollbar.pack(side=tk.BOTTOM, fill=tk.X)
        
        # Bind events
        self.tree.bind('<Double-1>', self.on_double_click)
        self.tree.bind('<Return>', self.on_enter_key)
        self.tree.bind('<<TreeviewSelect>>', self.on_selection_changed)
        
        # Preview and status frame
        self.bottom_frame = tk.Frame(self)
        self.bottom_frame.pack(fill=tk.X, pady=(10, 0))

        # Preview frame (left side)
        self.preview_frame = tk.LabelFrame(self.bottom_frame, text="Preview", font=("Arial", 9, "bold"))
        self.preview_frame.pack(side=tk.LEFT, fill=tk.Y, padx=(0, 10))

        # Thumbnail label
        self.thumbnail_label = tk.Label(self.preview_frame, text="No preview",
                                       width=15, height=8, relief='sunken')
        self.thumbnail_label.pack(padx=5, pady=5)

        # File info in preview
        self.preview_info_label = tk.Label(self.preview_frame, text="",
                                          font=("Arial", 8), justify=tk.LEFT)
        self.preview_info_label.pack(padx=5, pady=(0, 5))

        # Status frame (right side)
        self.status_frame = tk.Frame(self.bottom_frame)
        self.status_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)

        self.status_label = tk.Label(
            self.status_frame,
            text="Select a media file to edit its metadata",
            font=("Arial", 9)
        )
        self.status_label.pack(anchor=tk.W)
    
    def load_directory(self, path):
        try:
            if not os.path.exists(path):
                return

            self.current_path = path
            self.path_var.set(path)

            # Save current directory to settings
            self.settings_service.set_last_directory(path)
            
            # Clear existing items
            for item in self.tree.get_children():
                self.tree.delete(item)
            
            # Add parent directory entry if not at root
            if os.path.dirname(path) != path:
                self.tree.insert('', 'end', text='..', values=('', 'Directory'), tags=('directory',))
            
            # List directories and files
            items = []
            try:
                for item in os.listdir(path):
                    item_path = os.path.join(path, item)
                    if os.path.isdir(item_path):
                        items.append((item, 'directory', ''))
                    elif os.path.isfile(item_path):
                        ext = os.path.splitext(item)[1].lower()
                        if ext in self.supported_extensions:
                            size = self.format_file_size(os.path.getsize(item_path))
                            file_type = self.get_file_type(ext)
                            items.append((item, 'file', size, file_type))
            except PermissionError:
                self.status_label.configure(text="Permission denied to access this directory")
                return
            
            # Sort items (directories first, then files)
            items.sort(key=lambda x: (x[1] != 'directory', x[0].lower()))
            
            # Add items to tree
            for item in items:
                if item[1] == 'directory':
                    self.tree.insert('', 'end', text=item[0], values=('', 'Directory'), tags=('directory',))
                else:
                    self.tree.insert('', 'end', text=item[0], values=(item[2], item[3]), tags=('file',))
            
            # Update status
            file_count = sum(1 for item in items if item[1] == 'file')
            dir_count = sum(1 for item in items if item[1] == 'directory')
            self.status_label.configure(text=f"{file_count} supported files, {dir_count} directories")
            
        except Exception as e:
            self.status_label.configure(text=f"Error loading directory: {str(e)}")
    
    def format_file_size(self, size):
        for unit in ['B', 'KB', 'MB', 'GB']:
            if size < 1024.0:
                return f"{size:.1f} {unit}"
            size /= 1024.0
        return f"{size:.1f} TB"
    
    def get_file_type(self, extension):
        image_exts = {'.jpg', '.jpeg', '.png', '.tiff', '.tif', '.bmp', '.gif'}
        video_exts = {'.mp4', '.avi', '.mov', '.mkv', '.wmv', '.flv', '.webm'}
        audio_exts = {'.mp3', '.flac', '.ogg', '.m4a', '.wav'}
        
        if extension in image_exts:
            return 'Image'
        elif extension in video_exts:
            return 'Video'
        elif extension in audio_exts:
            return 'Audio'
        return 'Unknown'

    def on_selection_changed(self, event):
        selection = self.tree.selection()
        if not selection:
            self.clear_preview()
            return

        item = self.tree.item(selection[0])
        item_name = item['text']

        if item_name == '..' or 'directory' in item['tags']:
            self.clear_preview()
            return

        if 'file' in item['tags']:
            file_path = os.path.join(self.current_path, item_name)
            self.show_preview(file_path)

    def clear_preview(self):
        self.thumbnail_label.configure(image='', text="No preview")
        self.thumbnail_label.image = None
        self.preview_info_label.configure(text="")

    def show_preview(self, file_path):
        try:
            # Get file info
            file_size = os.path.getsize(file_path)
            size_str = self.format_file_size(file_size)
            ext = os.path.splitext(file_path)[1].lower()
            file_type = self.get_file_type(ext)

            info_text = f"Type: {file_type}\nSize: {size_str}"

            # Try to create thumbnail
            thumbnail = self.create_thumbnail(file_path, ext)

            if thumbnail:
                self.thumbnail_label.configure(image=thumbnail, text="")
                self.thumbnail_label.image = thumbnail  # Keep reference
            else:
                # Show file type icon as text
                type_icons = {
                    'Image': '🖼️',
                    'Video': '🎬',
                    'Audio': '🎵'
                }
                icon = type_icons.get(file_type, '📄')
                self.thumbnail_label.configure(image='', text=f"{icon}\n{file_type}")
                self.thumbnail_label.image = None

            self.preview_info_label.configure(text=info_text)

        except Exception as e:
            self.clear_preview()
            print(f"Error showing preview: {e}")

    def create_thumbnail(self, file_path, extension):
        try:
            if extension in {'.jpg', '.jpeg', '.png', '.tiff', '.tif', '.bmp', '.gif'}:
                # Create image thumbnail
                try:
                    from PIL import Image, ImageTk

                    img = Image.open(file_path)

                    # Calculate thumbnail size maintaining aspect ratio
                    # Use LANCZOS if available, otherwise use ANTIALIAS
                    try:
                        img.thumbnail((120, 120), Image.Resampling.LANCZOS)
                    except AttributeError:
                        # Fallback for older PIL versions
                        img.thumbnail((120, 120), Image.ANTIALIAS)

                    # Convert to PhotoImage for tkinter
                    return ImageTk.PhotoImage(img)

                except ImportError:
                    print("PIL/Pillow not available for thumbnail generation")
                    return None
                except Exception as e:
                    print(f"Error creating image thumbnail: {e}")
                    return None

            elif extension in {'.mp4', '.avi', '.mov', '.mkv', '.wmv'}:
                # For video files, try to extract first frame
                # This is optional and requires additional libraries
                return None

            else:
                return None

        except Exception as e:
            print(f"Error creating thumbnail: {e}")
            return None

    def on_double_click(self, event):
        selection = self.tree.selection()
        if not selection:
            return

        item = self.tree.item(selection[0])
        item_name = item['text']

        if item_name == '..':
            self.go_up()
        elif 'directory' in item['tags']:
            new_path = os.path.join(self.current_path, item_name)
            self.load_directory(new_path)
        elif 'file' in item['tags']:
            file_path = os.path.join(self.current_path, item_name)
            # Add to recent files
            self.settings_service.add_recent_file(file_path)
            self.on_file_selected(file_path)

    def on_enter_key(self, event):
        self.on_double_click(event)

    def on_path_enter(self, event):
        path = self.path_var.get()
        if os.path.exists(path) and os.path.isdir(path):
            self.load_directory(path)
        else:
            self.path_var.set(self.current_path)
            self.status_label.configure(text="Invalid path")

    def browse_folder(self):
        folder = filedialog.askdirectory(initialdir=self.current_path)
        if folder:
            self.load_directory(folder)

    def go_up(self):
        parent = os.path.dirname(self.current_path)
        if parent != self.current_path:
            self.load_directory(parent)

    def go_home(self):
        self.load_directory(os.path.expanduser("~"))

    def refresh(self):
        self.load_directory(self.current_path)

    def apply_theme(self):
        # Apply theme to all widgets
        self.theme_service.apply_theme_to_widget(self, 'frame')
        self.theme_service.apply_theme_to_widget(self.top_frame, 'frame')
        self.theme_service.apply_theme_to_widget(self.path_frame, 'frame')
        self.theme_service.apply_theme_to_widget(self.nav_frame, 'frame')
        self.theme_service.apply_theme_to_widget(self.list_frame, 'frame')
        self.theme_service.apply_theme_to_widget(self.bottom_frame, 'frame')
        self.theme_service.apply_theme_to_widget(self.preview_frame, 'frame')
        self.theme_service.apply_theme_to_widget(self.status_frame, 'frame')

        # Apply theme to entry
        self.theme_service.apply_theme_to_widget(self.path_entry, 'entry')

        # Apply theme to buttons
        for button in [self.browse_button, self.up_button, self.home_button, self.refresh_button]:
            self.theme_service.apply_theme_to_widget(button, 'button_secondary')

        # Apply theme to labels
        self.theme_service.apply_theme_to_widget(self.status_label, 'label_secondary')
        self.theme_service.apply_theme_to_widget(self.thumbnail_label, 'label_secondary')
        self.theme_service.apply_theme_to_widget(self.preview_info_label, 'label_secondary')

        # Apply theme to treeview
        self.theme_service.apply_theme_to_widget(self.tree, 'treeview')
