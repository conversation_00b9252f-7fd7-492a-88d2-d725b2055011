# Panduan Penulisan Metadata - Pendekatan Baru

## Cara Kerja <PERSON>adata Writing di Gema

Gema sekarang menggunakan pendekatan yang lebih sederhana dan efektif:

### 1. ExifTool (Recommended)
- **Primary Method**: ExifTool command-line tool
- **Support**: Semua format (images, audio, video)
- **Reliability**: Sangat tinggi, industry standard
- **Installation**: Otomatis via `install_exiftool.bat`

### 2. Mutagen (Fallback untuk Audio)
- **MP3**: ID3v2 tags dengan encoding UTF-8
- **FLAC**: Vorbis comments
- **M4A**: iTunes-style tags

### 3. PIL (Fallback untuk Images)
- **Basic metadata**: Disimpan dalam image info
- **Limited**: Tidak semua format support EXIF

## Quick Setup

### 1. Install ExifTool (Recommended)
```bash
# Run as Administrator
install_exiftool.bat
```

### 2. Test Metadata Writing
```bash
python debug_metadata.py
```

## Troubleshooting

### 1. Install ExifTool
ExifTool adalah tool paling reliable untuk metadata writing:

**Windows:**
1. Run `install_exiftool.bat` sebagai Administrator
2. Atau download manual dari https://exiftool.org/
3. Extract dan add ke PATH

**Verify Installation:**
```bash
exiftool -ver
```

### 2. Test dengan Script Debug
```bash
python debug_metadata.py
```

Script akan test:
- ExifTool availability
- Mutagen MP3 writing
- File permissions
- Verification

### 3. Format-Specific Issues

#### MP3 Files:
- Pastikan file bukan MP3 yang corrupt
- Cek apakah sudah ada ID3 tags
- Beberapa MP3 encoder menghasilkan file yang sulit diedit

#### JPEG Files:
- Install piexif untuk full support: `pip install piexif`
- Tanpa piexif, hanya basic metadata yang bisa ditulis
- Progressive JPEG mungkin bermasalah

#### FLAC Files:
- Biasanya paling reliable untuk metadata writing
- Support custom fields dengan baik

### 4. Common Error Messages

#### "File is not writable"
- File sedang dibuka di aplikasi lain
- File di network drive dengan permission terbatas
- File system read-only

#### "Failed to save metadata"
- Format file tidak didukung
- File corrupt atau tidak valid
- Library dependencies missing

#### "Permission denied"
- Insufficient user permissions
- File locked oleh system
- Antivirus blocking file modification

### 5. Debug Steps

1. **Enable Console Output**:
   Run dari command line untuk melihat debug messages:
   ```bash
   python main.py
   ```

2. **Check Dependencies**:
   ```python
   import mutagen
   import piexif  # Optional
   print("Dependencies OK")
   ```

3. **Test dengan File Sample**:
   - Buat copy file untuk testing
   - Test dengan file kecil dulu
   - Coba berbagai format

4. **Manual Verification**:
   Setelah save, buka file di aplikasi lain untuk verify:
   - MP3: Windows Properties, VLC, foobar2000
   - JPEG: Windows Properties, ExifTool
   - FLAC: foobar2000, MediaInfo

## Best Practices

### 1. File Backup
Aplikasi otomatis membuat backup sebelum menulis:
- `filename.ext.backup` dibuat sementara
- Dihapus jika penulisan sukses
- Restored jika penulisan gagal

### 2. Field Naming
Gunakan field names yang standard:
- **Audio**: Title, Artist, Album, Genre, Year, Track
- **Image**: Title, Description, Creator, Copyright, Make, Model
- **Custom**: Gunakan nama yang deskriptif

### 3. Value Format
- **Year**: 4 digit (2023)
- **Track**: Angka (1, 2, 3)
- **Date**: ISO format (2023-12-25)
- **Text**: UTF-8 encoding

### 4. Testing Workflow
1. Test dengan copy file dulu
2. Gunakan file format yang paling didukung (MP3, JPEG)
3. Start dengan field standard (Title, Artist)
4. Verify hasil dengan aplikasi lain

## Advanced Debugging

### 1. Manual Mutagen Test
```python
import mutagen
file = mutagen.File("test.mp3")
print(f"File type: {type(file)}")
print(f"Has tags: {file.tags is not None}")

# Add test tag
if file.tags is None:
    file.add_tags()
file.tags['TIT2'] = mutagen.id3.TIT2(encoding=3, text='Test Title')
file.save()
```

### 2. Manual Piexif Test
```python
import piexif
from PIL import Image

# Read EXIF
exif_dict = piexif.load("test.jpg")
print(f"EXIF data: {exif_dict}")

# Add metadata
exif_dict["0th"][piexif.ImageIFD.Artist] = "Test Artist"
exif_bytes = piexif.dump(exif_dict)

# Save
img = Image.open("test.jpg")
img.save("test_output.jpg", exif=exif_bytes)
```

### 3. Check File Integrity
```python
import mutagen
try:
    file = mutagen.File("test.mp3")
    if file is None:
        print("File format not recognized")
    else:
        print(f"File OK: {type(file)}")
except Exception as e:
    print(f"File error: {e}")
```

## Known Limitations

### 1. Format Limitations
- **PNG**: Metadata support terbatas
- **GIF**: Tidak support metadata
- **AVI**: Read-only metadata
- **WMV**: Terbatas support

### 2. Library Limitations
- Tanpa piexif: JPEG metadata writing terbatas
- Mutagen: Tidak semua format video didukung
- PIL: Basic metadata only

### 3. System Limitations
- Network drives: Permission issues
- Cloud storage: Sync conflicts
- Mobile devices: MTP transfer issues

## Success Indicators

### 1. Console Messages
```
Writing metadata to: file.mp3
File type detected: <class 'mutagen.mp3.MP3'>
Writing MP3 tag: Title = My Song
Saving file...
Audio metadata saved successfully
Write result: True
Verified: Title = My Song
```

### 2. UI Feedback
- "Metadata saved successfully!" dialog
- Status: "Saved X metadata fields"
- Refresh shows updated values

### 3. External Verification
- Windows Properties shows new metadata
- Media players display updated info
- ExifTool shows EXIF changes

## Getting Help

Jika masih bermasalah:
1. Run `python debug_metadata.py` dengan file test
2. Copy console output
3. Test dengan format file berbeda
4. Cek dengan aplikasi metadata lain (ExifTool, MediaInfo)
5. Verify file permissions dan dependencies
