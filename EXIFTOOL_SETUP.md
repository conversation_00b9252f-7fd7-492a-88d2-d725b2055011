# ExifTool Setup untuk Gema

## ExifTool Sudah Tersedia

ExifTool sudah tersedia di direktori `exiftool-13.32_64/`. Aplikasi Gema akan otomatis mendeteksi dan menggunakan ExifTool ini.

## Struktur Direktori

```
e:\PROJECT\Metadata/
├── exiftool-13.32_64/
│   ├── exiftool.exe          # ExifTool executable
│   ├── exiftool(-k).exe      # Alternative executable
│   └── ...                   # Other ExifTool files
├── main.py
├── services/
└── ...
```

## Cara Test ExifTool

### 1. Test Langsung
```bash
python test_exiftool_direct.py
```

Script ini akan:
- Detect ExifTool di direktori lokal
- Test read/write metadata
- Backup dan restore file
- Verification hasil

### 2. Test via Debug Script
```bash
python debug_metadata.py
```

### 3. Test Manual
```bash
# Dari command prompt di direktori Gema
exiftool-13.32_64\exiftool.exe -ver
exiftool-13.32_64\exiftool.exe -Title="Test" your_file.mp3
```

## Cara Kerja Auto-Detection

Aplikasi Gema akan mencari ExifTool dalam urutan:

1. **Local Directory**: `exiftool-13.32_64/exiftool.exe`
2. **Local Directory**: `exiftool-13.32_64/exiftool(-k).exe`
3. **PATH Environment**: `exiftool` command
4. **Common Paths**: Program Files, dll

## Supported Metadata Writing

Dengan ExifTool, Gema dapat menulis metadata ke:

### Audio Files:
- **MP3**: ID3v1, ID3v2 tags
- **FLAC**: Vorbis comments
- **M4A**: iTunes tags
- **OGG**: Vorbis comments
- **WAV**: INFO chunks

### Image Files:
- **JPEG**: EXIF, IPTC, XMP
- **TIFF**: EXIF, IPTC, XMP
- **PNG**: XMP, text chunks
- **RAW**: Camera-specific metadata

### Video Files:
- **MP4**: QuickTime tags
- **MOV**: QuickTime tags
- **AVI**: INFO chunks
- **MKV**: Matroska tags

## Field Mapping

ExifTool menggunakan tag mapping yang standard:

### Common Fields:
- `Title` → Title tag
- `Artist` → Artist tag
- `Album` → Album tag
- `Genre` → Genre tag
- `Year` → Year/Date tag
- `Comment` → Comment tag
- `Description` → Description tag
- `Creator` → Creator/Artist tag
- `Copyright` → Copyright tag

### Custom Fields:
- Custom fields disimpan sebagai XMP tags
- Format: `XMP:CustomFieldName`

## Troubleshooting

### ExifTool Not Found
```
ExifTool not found
```

**Solusi:**
1. Pastikan folder `exiftool-13.32_64` ada
2. Pastikan file `exiftool.exe` atau `exiftool(-k).exe` ada
3. Check permissions file

### Permission Denied
```
Error: Permission denied
```

**Solusi:**
1. Tutup aplikasi lain yang menggunakan file
2. Run sebagai Administrator
3. Check file read-only attribute

### Write Failed
```
ExifTool error: ...
```

**Solusi:**
1. Check file format support
2. Verify file tidak corrupt
3. Test dengan file sample kecil

## Performance Tips

### Untuk File Besar:
- ExifTool sangat efisien untuk file besar
- Metadata writing tidak mempengaruhi file content
- Backup otomatis dengan `-overwrite_original`

### Untuk Batch Operations:
- ExifTool support multiple files
- Dapat dioptimasi untuk batch processing
- Memory usage minimal

## Advanced Usage

### Manual ExifTool Commands:
```bash
# Read all metadata
exiftool-13.32_64\exiftool.exe -j file.mp3

# Write specific tags
exiftool-13.32_64\exiftool.exe -Title="My Song" -Artist="My Artist" file.mp3

# Copy metadata between files
exiftool-13.32_64\exiftool.exe -TagsFromFile source.mp3 target.mp3

# Remove all metadata
exiftool-13.32_64\exiftool.exe -all= file.mp3
```

### Custom Tag Writing:
```bash
# XMP custom fields
exiftool-13.32_64\exiftool.exe -XMP:CustomField="Custom Value" file.mp3

# EXIF custom fields (images)
exiftool-13.32_64\exiftool.exe -EXIF:UserComment="Custom Comment" image.jpg
```

## Verification

Setelah menulis metadata, verify dengan:

### Windows Properties:
- Right-click file → Properties → Details

### Media Players:
- VLC: Tools → Media Information
- Windows Media Player: Library view
- foobar2000: Properties

### Other Tools:
- MediaInfo
- Mp3tag
- Adobe Bridge (untuk images)

## Backup & Recovery

### Automatic Backup:
- Gema menggunakan `-overwrite_original` flag
- ExifTool membuat backup sementara
- Backup dihapus jika write sukses

### Manual Backup:
```bash
# Create backup before editing
copy file.mp3 file.mp3.backup

# Restore if needed
copy file.mp3.backup file.mp3
```

## Status Messages

### Success:
```
Found ExifTool at: e:\PROJECT\Metadata\exiftool-13.32_64\exiftool.exe
ExifTool write successful
✓ Verified: Title = My Song
```

### Failure:
```
ExifTool not found
ExifTool error: File format not supported
```

Dengan ExifTool yang sudah tersedia, Gema sekarang memiliki kemampuan metadata writing yang sangat powerful dan reliable!
