"""
Metadata editor component for editing file metadata
"""

import tkinter as tk
from tkinter import ttk, messagebox
import os
from views.metadata_field_selector import MetadataFieldSelector
from services.metadata_template_service import MetadataTemplateService
from services.settings_service import SettingsService
from utils.tooltip import create_tooltip

class MetadataEditor(tk.Frame):
    def __init__(self, parent, theme_service, metadata_service, metadata_model):
        super().__init__(parent)
        self.theme_service = theme_service
        self.metadata_service = metadata_service
        self.metadata_model = metadata_model
        self.template_service = MetadataTemplateService()
        self.settings_service = SettingsService()
        self.current_file = None
        self.metadata_entries = {}
        
        self.create_widgets()
    
    def create_widgets(self):
        # File info frame
        self.file_info_frame = tk.Frame(self)
        self.file_info_frame.pack(fill=tk.X, pady=(0, 10))
        
        # File path
        self.file_label = tk.Label(
            self.file_info_frame,
            text="No file selected",
            font=("Arial", 12, "bold"),
            anchor='w'
        )
        self.file_label.pack(fill=tk.X)
        
        # File details
        self.details_frame = tk.Frame(self.file_info_frame)
        self.details_frame.pack(fill=tk.X, pady=(5, 0))
        
        self.type_label = tk.Label(self.details_frame, text="", font=("Arial", 9))
        self.type_label.pack(side=tk.LEFT)
        
        self.size_label = tk.Label(self.details_frame, text="", font=("Arial", 9))
        self.size_label.pack(side=tk.RIGHT)
        
        # Action buttons frame
        self.action_frame = tk.Frame(self)
        self.action_frame.pack(fill=tk.X, pady=(0, 10))
        
        self.load_button = tk.Button(
            self.action_frame,
            text="📂 Load File",
            command=self.load_file_dialog,
            font=("Arial", 10)
        )
        self.load_button.pack(side=tk.LEFT, padx=(0, 5))
        
        self.read_button = tk.Button(
            self.action_frame,
            text="📖 Read Metadata",
            command=self.read_metadata,
            font=("Arial", 10),
            state='disabled'
        )
        self.read_button.pack(side=tk.LEFT, padx=(0, 5))
        
        self.save_button = tk.Button(
            self.action_frame,
            text="💾 Save Metadata",
            command=self.save_metadata,
            font=("Arial", 10),
            state='disabled'
        )
        self.save_button.pack(side=tk.LEFT, padx=(0, 5))
        
        self.clear_button = tk.Button(
            self.action_frame,
            text="🗑️ Clear All",
            command=self.clear_metadata,
            font=("Arial", 10)
        )
        self.clear_button.pack(side=tk.LEFT)
        
        # Add new field button
        self.add_button = tk.Button(
            self.action_frame,
            text="➕ Add Field",
            command=self.add_metadata_field,
            font=("Arial", 10)
        )
        self.add_button.pack(side=tk.RIGHT, padx=(5, 0))

        # Quick add popular fields button
        self.quick_add_button = tk.Button(
            self.action_frame,
            text="⚡ Quick Add",
            command=self.quick_add_popular_fields,
            font=("Arial", 10)
        )
        self.quick_add_button.pack(side=tk.RIGHT)
        
        # Metadata editor frame
        self.editor_frame = tk.Frame(self)
        self.editor_frame.pack(fill=tk.BOTH, expand=True)
        
        # Create scrollable frame for metadata fields
        self.canvas = tk.Canvas(self.editor_frame)
        self.scrollbar = ttk.Scrollbar(self.editor_frame, orient="vertical", command=self.canvas.yview)
        self.scrollable_frame = tk.Frame(self.canvas)
        
        self.scrollable_frame.bind(
            "<Configure>",
            lambda e: self.canvas.configure(scrollregion=self.canvas.bbox("all"))
        )
        
        self.canvas.create_window((0, 0), window=self.scrollable_frame, anchor="nw")
        self.canvas.configure(yscrollcommand=self.scrollbar.set)
        
        self.canvas.pack(side="left", fill="both", expand=True)
        self.scrollbar.pack(side="right", fill="y")
        
        # Bind mousewheel to canvas
        self.canvas.bind("<MouseWheel>", self._on_mousewheel)
        
        # Status frame
        self.status_frame = tk.Frame(self)
        self.status_frame.pack(fill=tk.X, pady=(10, 0))
        
        self.status_label = tk.Label(
            self.status_frame,
            text="Load a file to start editing metadata",
            font=("Arial", 9)
        )
        self.status_label.pack(side=tk.LEFT)
    
    def _on_mousewheel(self, event):
        self.canvas.yview_scroll(int(-1*(event.delta/120)), "units")
    
    def load_file_dialog(self):
        from tkinter import filedialog
        file_path = filedialog.askopenfilename(
            title="Select Media File",
            filetypes=[
                ("All Supported", "*.jpg;*.jpeg;*.png;*.tiff;*.tif;*.bmp;*.gif;*.mp4;*.avi;*.mov;*.mkv;*.wmv;*.flv;*.webm;*.mp3;*.flac;*.ogg;*.m4a;*.wav"),
                ("Image Files", "*.jpg;*.jpeg;*.png;*.tiff;*.tif;*.bmp;*.gif"),
                ("Video Files", "*.mp4;*.avi;*.mov;*.mkv;*.wmv;*.flv;*.webm"),
                ("Audio Files", "*.mp3;*.flac;*.ogg;*.m4a;*.wav"),
                ("All Files", "*.*")
            ]
        )
        if file_path:
            self.load_file(file_path)
    
    def load_file(self, file_path):
        if not os.path.exists(file_path):
            messagebox.showerror("Error", "File not found")
            return
        
        self.current_file = file_path
        self.metadata_model.set_file_path(file_path)

        # Add to recent files
        self.settings_service.add_recent_file(file_path)
        
        # Update file info
        filename = os.path.basename(file_path)
        self.file_label.configure(text=filename)
        
        # Get file details
        file_size = os.path.getsize(file_path)
        size_str = self.format_file_size(file_size)
        file_type = self.metadata_model.file_type.title()
        
        self.type_label.configure(text=f"Type: {file_type}")
        self.size_label.configure(text=f"Size: {size_str}")
        
        # Enable buttons
        self.read_button.configure(state='normal')
        self.save_button.configure(state='normal')
        
        # Clear existing metadata
        self.clear_metadata_display()
        
        # Auto-read metadata
        self.read_metadata()
        
        self.status_label.configure(text=f"Loaded: {filename}")
    
    def format_file_size(self, size):
        for unit in ['B', 'KB', 'MB', 'GB']:
            if size < 1024.0:
                return f"{size:.1f} {unit}"
            size /= 1024.0
        return f"{size:.1f} TB"
    
    def read_metadata(self):
        if not self.current_file:
            return
        
        try:
            metadata = self.metadata_service.read_metadata(self.current_file)
            self.metadata_model.set_metadata(metadata)
            self.display_metadata(metadata)
            self.status_label.configure(text=f"Read {len(metadata)} metadata fields")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to read metadata: {str(e)}")
            self.status_label.configure(text="Error reading metadata")
    
    def display_metadata(self, metadata):
        self.clear_metadata_display()
        
        for key, value in metadata.items():
            self.add_metadata_row(key, str(value))
    
    def clear_metadata_display(self):
        for widget in self.scrollable_frame.winfo_children():
            widget.destroy()
        self.metadata_entries.clear()
    
    def add_metadata_row(self, key="", value=""):
        row_frame = tk.Frame(self.scrollable_frame)
        row_frame.pack(fill=tk.X, pady=2)
        
        # Key entry
        key_entry = tk.Entry(row_frame, width=20, font=("Arial", 9))
        key_entry.pack(side=tk.LEFT, padx=(0, 5))
        key_entry.insert(0, key)
        
        # Value entry
        value_entry = tk.Entry(row_frame, font=("Arial", 9))
        value_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 5))
        value_entry.insert(0, value)

        # Bind validation events
        value_entry.bind('<KeyRelease>', lambda e: self.validate_field_value(key_entry, value_entry))
        value_entry.bind('<FocusOut>', lambda e: self.validate_field_value(key_entry, value_entry))
        
        # Delete button
        delete_button = tk.Button(
            row_frame,
            text="❌",
            width=3,
            command=lambda: self.delete_metadata_row(row_frame, key_entry)
        )
        delete_button.pack(side=tk.RIGHT)
        
        # Store references
        self.metadata_entries[key_entry] = (value_entry, delete_button, row_frame)

        # Add tooltip for field description
        if key:
            file_type = getattr(self.metadata_model, 'file_type', 'unknown')
            description = self.template_service.get_field_description(key)
            create_tooltip(key_entry, description)

        # Apply theme
        self.theme_service.apply_theme_to_widget(row_frame, 'frame')
        self.theme_service.apply_theme_to_widget(key_entry, 'entry')
        self.theme_service.apply_theme_to_widget(value_entry, 'entry')
        self.theme_service.apply_theme_to_widget(delete_button, 'button_secondary')
    
    def delete_metadata_row(self, row_frame, key_entry):
        if key_entry in self.metadata_entries:
            del self.metadata_entries[key_entry]
        row_frame.destroy()
    
    def add_metadata_field(self):
        # Get current file type for template selection
        file_type = getattr(self.metadata_model, 'file_type', 'unknown')

        # Show field selector dialog
        selector = MetadataFieldSelector(
            self.winfo_toplevel(),
            self.theme_service,
            file_type
        )
        selected_fields = selector.show()

        if selected_fields:
            # Add selected fields as new rows
            for field_name in selected_fields:
                # Check if field already exists
                field_exists = False
                for key_entry, (_, _, _) in self.metadata_entries.items():
                    if key_entry.get() == field_name:
                        field_exists = True
                        break

                # Only add if field doesn't exist
                if not field_exists:
                    self.add_metadata_row(field_name, "")

            self.status_label.configure(text=f"Added {len(selected_fields)} metadata fields")

    def quick_add_popular_fields(self):
        """Add popular metadata fields for current file type"""
        file_type = getattr(self.metadata_model, 'file_type', 'unknown')
        popular_fields = self.template_service.get_popular_fields_for_file_type(file_type)

        added_count = 0
        for field_name in popular_fields:
            # Check if field already exists
            field_exists = False
            for key_entry, (_, _, _) in self.metadata_entries.items():
                if key_entry.get() == field_name:
                    field_exists = True
                    break

            # Only add if field doesn't exist
            if not field_exists:
                self.add_metadata_row(field_name, "")
                added_count += 1

        if added_count > 0:
            self.status_label.configure(text=f"Added {added_count} popular fields for {file_type} files")
        else:
            self.status_label.configure(text="All popular fields already present")

    def validate_field_value(self, key_entry, value_entry):
        """Validate field value and provide visual feedback"""
        field_name = key_entry.get().strip()
        field_value = value_entry.get().strip()

        if not field_name or not field_value:
            return

        file_type = getattr(self.metadata_model, 'file_type', 'unknown')
        is_valid = self.template_service.validate_field_value(field_name, field_value, file_type)

        # Apply visual feedback
        theme = self.theme_service.get_theme_data()
        if is_valid:
            value_entry.configure(bg=theme['surface'])
        else:
            # Light red background for invalid values
            if theme['name'] == 'dark':
                value_entry.configure(bg='#4a2c2c')
            else:
                value_entry.configure(bg='#ffe6e6')
    
    def clear_metadata(self):
        if messagebox.askyesno("Confirm", "Clear all metadata fields?"):
            self.clear_metadata_display()
            self.status_label.configure(text="Metadata fields cleared")

    def save_metadata(self):
        if not self.current_file:
            messagebox.showwarning("Warning", "No file selected")
            return

        try:
            # Collect metadata from entries
            metadata = {}
            for key_entry, (value_entry, _, _) in self.metadata_entries.items():
                key = key_entry.get().strip()
                value = value_entry.get().strip()
                if key and value:
                    metadata[key] = value

            if not metadata:
                messagebox.showwarning("Warning", "No metadata to save")
                return

            print(f"Saving metadata to: {self.current_file}")
            print(f"Metadata to save: {metadata}")

            # Check file permissions
            if not os.access(self.current_file, os.W_OK):
                messagebox.showerror("Error", "File is read-only or not writable")
                return

            # Save metadata
            success = self.metadata_service.write_metadata(self.current_file, metadata)

            if success:
                self.metadata_model.set_metadata(metadata)
                messagebox.showinfo("Success", f"Metadata saved successfully!\n\nFile: {os.path.basename(self.current_file)}\nFields: {len(metadata)}")
                self.status_label.configure(text=f"Saved {len(metadata)} metadata fields")

                # Refresh metadata display to show what was actually saved
                self.read_metadata()
            else:
                messagebox.showerror("Error", "Failed to save metadata to file")
                self.status_label.configure(text="Error saving metadata")

        except PermissionError as e:
            messagebox.showerror("Permission Error", f"Cannot write to file:\n{str(e)}")
            self.status_label.configure(text="Permission denied")
        except Exception as e:
            error_msg = f"Failed to save metadata:\n{str(e)}"
            messagebox.showerror("Error", error_msg)
            self.status_label.configure(text="Error saving metadata")
            print(f"Save metadata error: {e}")
            import traceback
            traceback.print_exc()

    def apply_theme(self):
        # Apply theme to all widgets
        self.theme_service.apply_theme_to_widget(self, 'frame')
        self.theme_service.apply_theme_to_widget(self.file_info_frame, 'frame')
        self.theme_service.apply_theme_to_widget(self.details_frame, 'frame')
        self.theme_service.apply_theme_to_widget(self.action_frame, 'frame')
        self.theme_service.apply_theme_to_widget(self.editor_frame, 'frame')
        self.theme_service.apply_theme_to_widget(self.status_frame, 'frame')
        self.theme_service.apply_theme_to_widget(self.scrollable_frame, 'frame')

        # Apply theme to canvas
        theme = self.theme_service.get_theme_data()
        self.canvas.configure(bg=theme['background'])

        # Apply theme to labels
        self.theme_service.apply_theme_to_widget(self.file_label, 'label')
        self.theme_service.apply_theme_to_widget(self.type_label, 'label_secondary')
        self.theme_service.apply_theme_to_widget(self.size_label, 'label_secondary')
        self.theme_service.apply_theme_to_widget(self.status_label, 'label_secondary')

        # Apply theme to buttons
        for button in [self.load_button, self.read_button, self.save_button,
                      self.clear_button, self.add_button, self.quick_add_button]:
            self.theme_service.apply_theme_to_widget(button, 'button_secondary')

        # Apply theme to metadata entries
        for key_entry, (value_entry, delete_button, row_frame) in self.metadata_entries.items():
            self.theme_service.apply_theme_to_widget(row_frame, 'frame')
            self.theme_service.apply_theme_to_widget(key_entry, 'entry')
            self.theme_service.apply_theme_to_widget(value_entry, 'entry')
            self.theme_service.apply_theme_to_widget(delete_button, 'button_secondary')
