---
type: "manual"
---

1. <PERSON><PERSON><PERSON> b<PERSON>, tanpa komentar. <PERSON><PERSON><PERSON>an diskusi hanya dalam Bahasa Indonesia. <PERSON>i adalah aplikasi modular menggunakan gaya "views, models dan services".
2. <PERSON><PERSON> ubah fitur yang ada. <PERSON><PERSON> per<PERSON>n atau tambahkan yang perlu.
3. <PERSON><PERSON><PERSON> struktur sebelum ubah. Hindari konflik dan duplikasi.
4. Fungsi baru harus modular terintegrasi, kompatibel.
5. Jika file terlalu besar lebih dari 1200 baris maka buatkan di-file baru tetapi terintegrasi dengan akurat.
6. <PERSON><PERSON> pernah buat file test.