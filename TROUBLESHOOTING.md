# Troubleshooting Gema

## Common Issues and Solutions

### 1. Import Errors

#### Error: `ImportError: cannot import name 'TAGS_V2' from 'PIL.ExifTags'`
**Cause**: Older version of Pillow/PIL
**Solution**: 
```bash
pip install --upgrade Pillow
```
Or use minimal mode:
```bash
run_gema_minimal.bat
```

#### Error: `ImportError: No module named 'piexif'`
**Cause**: piexif not installed
**Solution**: 
```bash
pip install piexif
```
**Note**: Application will work without piexif but with limited image metadata writing capability.

#### Error: `ImportError: No module named 'mutagen'`
**Cause**: mutagen not installed
**Solution**: 
```bash
pip install mutagen
```

### 2. Metadata Writing Issues

#### Problem: "Metadata saved successfully" but changes not visible
**Possible Causes**:
1. File is read-only
2. File is being used by another application
3. Insufficient permissions

**Solutions**:
1. Check file properties and remove read-only attribute
2. Close other applications using the file
3. Run as administrator
4. Try with a copy of the file

#### Problem: Image metadata not saving
**Cause**: piexif not available or file format not supported
**Solution**: 
1. Install piexif: `pip install piexif`
2. Use supported formats: JPG, JPEG, TIFF
3. Check console for error messages

### 3. Performance Issues

#### Problem: Application slow when browsing large directories
**Solutions**:
1. Navigate to smaller directories
2. Use search functionality
3. Disable thumbnail preview temporarily

#### Problem: Thumbnail generation slow
**Solutions**:
1. Ensure Pillow is properly installed
2. Use smaller image files for testing
3. Check available memory

### 4. UI Issues

#### Problem: Interface appears broken or misaligned
**Solutions**:
1. Restart application
2. Reset window geometry by deleting `~/.gema_settings.json`
3. Try different theme (light/dark toggle)

#### Problem: Theme not saving
**Cause**: Settings file not writable
**Solution**: 
1. Check permissions in home directory
2. Manually delete `~/.gema_settings.json`

### 5. File Browser Issues

#### Problem: Directory not loading
**Causes**:
1. Permission denied
2. Network drive issues
3. Path doesn't exist

**Solutions**:
1. Navigate to accessible directory
2. Use "Browse" button instead of typing path
3. Check network connectivity for network drives

#### Problem: Files not showing
**Cause**: Unsupported file format
**Solution**: Check supported formats list in documentation

### 6. Installation Issues

#### Problem: Dependencies fail to install
**Solutions**:
1. Update pip: `python -m pip install --upgrade pip`
2. Use virtual environment
3. Install dependencies manually one by one
4. Try minimal installation: `run_gema_minimal.bat`

#### Problem: Python not found
**Solutions**:
1. Install Python from python.org
2. Add Python to PATH during installation
3. Restart command prompt after installation

### 7. Settings and Configuration

#### Problem: Settings not persisting
**Cause**: Settings file corruption or permission issues
**Solution**: 
1. Delete `~/.gema_settings.json`
2. Restart application
3. Check home directory permissions

#### Problem: Recent files not working
**Cause**: Files moved or deleted
**Solution**: 
1. Recent files are automatically cleaned
2. Manually clear recent files from settings

## Compatibility Information

### Supported Python Versions
- Python 3.7+
- Tested on Python 3.8, 3.9, 3.10, 3.11, 3.13

### Supported Operating Systems
- Windows 10/11
- Windows 7 (with limitations)
- Linux (Ubuntu, Debian, etc.)
- macOS (limited testing)

### Supported File Formats

#### Full Support (Read + Write):
- **Audio**: MP3, FLAC, M4A
- **Video**: MP4, MOV (limited)

#### Read-Only:
- **Images**: PNG, BMP, GIF (basic info only)
- **Video**: AVI, MKV, WMV (basic info only)

#### Best Support:
- **Images**: JPG, JPEG, TIFF (with piexif)
- **Audio**: MP3 (with ID3 tags)

## Getting Help

### Debug Information
When reporting issues, include:
1. Python version: `python --version`
2. Installed packages: `pip list`
3. Operating system
4. Error messages (full traceback)
5. File format being used

### Console Output
Run application from command line to see detailed error messages:
```bash
python main.py
```

### Log Files
Check console output for warnings and errors. Common messages:
- `Warning: piexif not available` - Install piexif for full image support
- `Error reading/writing metadata` - Check file permissions and format

### Reset Application
To completely reset application:
1. Delete `~/.gema_settings.json`
2. Restart application
3. Reconfigure preferences

## Performance Tips

### For Large Collections:
1. Use specific directories instead of root drives
2. Enable thumbnail preview only when needed
3. Use search functionality to filter files

### For Slow Systems:
1. Use minimal installation
2. Disable thumbnail preview
3. Work with smaller batches of files

### Memory Usage:
- Application uses minimal memory
- Thumbnails are generated on-demand
- Settings file is small (< 1KB)

## Advanced Troubleshooting

### Manual Dependency Installation:
```bash
# Core dependencies (required)
pip install Pillow>=8.0.0
pip install mutagen>=1.45.0

# Optional dependencies
pip install piexif>=1.1.3
pip install exifread>=2.3.0
```

### Virtual Environment Setup:
```bash
python -m venv gema_env
gema_env\Scripts\activate  # Windows
source gema_env/bin/activate  # Linux/Mac
pip install -r requirements.txt
python main.py
```

### Development Mode:
For developers or advanced users:
```bash
git clone <repository>
cd gema
pip install -e .
python main.py
```
