# Gema - Metadata Editor

Aplikasi Python untuk mengedit metadata file media (gambar, video, dan audio) dengan interface yang menarik dan mudah digunakan.

## Fitur Utama

- **Multi-format Support**: Mendukung berbagai format file media
  - Gambar: JPG, JPEG, PNG, TIFF, TIF, BMP, GIF
  - Video: MP4, AVI, MOV, MKV, WMV, FLV, WEBM
  - Audio: MP3, FLAC, OGG, M4A, WAV

- **Interface Menarik**: 
  - Tema terang dan gelap dengan warna ungu yang elegan
  - Navigasi tab yang intuitif
  - File browser terintegrasi

- **Metadata Editing**:
  - Baca metadata existing dari file
  - Template field system dengan 100+ predefined fields
  - Quick Add untuk field populer berdasarkan tipe file
  - Field selector dengan kategorisasi dan search
  - Real-time validation dengan visual feedback
  - Tooltips dengan deskripsi field
  - Simpan metadata langsung ke file
  - Batch editing (coming soon)

## Tema Warna

- **<PERSON><PERSON>**: Tone warna #d1c2ed (ungu muda)
- **<PERSON><PERSON>**: Tone warna #8a6dc1 (ungu gelap)

## Instalasi

1. Pastikan Python 3.7+ terinstall
2. Install dependensi:
```bash
pip install -r requirements.txt
```

3. Jalankan aplikasi:
```bash
python main.py
```

## Dependensi

- `Pillow>=10.0.0` - Untuk handling gambar dan EXIF data
- `mutagen>=1.47.0` - Untuk metadata audio dan video
- `exifread>=3.0.0` - Untuk membaca EXIF data gambar
- `tkinter` - GUI framework (biasanya sudah include di Python)

## Struktur Proyek

```
Gema/
├── main.py                 # Entry point aplikasi
├── requirements.txt        # Dependensi Python
├── config/
│   ├── __init__.py
│   └── themes.py          # Konfigurasi tema
├── models/
│   ├── __init__.py
│   └── metadata_model.py  # Model data metadata
├── services/
│   ├── __init__.py
│   ├── metadata_service.py # Service untuk baca/tulis metadata
│   └── theme_service.py   # Service untuk manajemen tema
└── views/
    ├── __init__.py
    ├── main_window.py     # Window utama
    ├── file_browser.py    # Komponen file browser
    └── metadata_editor.py # Komponen editor metadata
```

## Cara Penggunaan

1. **File Browser Tab**: 
   - Browse dan pilih file media
   - Double-click file untuk membuka di editor

2. **Metadata Editor Tab**:
   - Load file menggunakan tombol "Load File"
   - Klik "Read Metadata" untuk membaca metadata existing
   - Edit atau tambah field metadata
   - Klik "Save Metadata" untuk menyimpan perubahan

3. **Theme Toggle**:
   - Klik tombol 🌙/☀️ di header untuk toggle tema

## Arsitektur

Aplikasi menggunakan pola arsitektur modular dengan pemisahan:
- **Models**: Struktur data dan business logic
- **Views**: Komponen UI dan presentasi
- **Services**: Logic untuk operasi file dan tema

## Kontribusi

Aplikasi ini dikembangkan dengan fokus pada:
- Modularitas dan maintainability
- User experience yang intuitif
- Support multi-format yang luas
- Performa yang optimal

## Lisensi

MIT License
