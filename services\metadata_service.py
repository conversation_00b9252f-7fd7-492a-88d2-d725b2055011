"""
Metadata service for reading and writing metadata to various file formats
"""

import os
import subprocess
import json
from PIL import Image
from PIL.ExifTags import TAGS
import mutagen
from mutagen.id3 import ID3, TIT2, TPE1, TALB, TDRC, TCON, COMM, TXXX
from mutagen.mp4 import MP4
from mutagen.flac import FLAC

# Check if ExifTool executable is available
def _check_exiftool_available():
    """Check if ExifTool executable is available"""
    script_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    local_paths = [
        os.path.join(script_dir, 'exiftool-13.32_64', 'exiftool.exe'),
        os.path.join(script_dir, 'exiftool-13.32_64', 'exiftool(-k).exe'),
        os.path.join(script_dir, 'exiftool', 'exiftool.exe'),
        os.path.join(script_dir, 'exiftool', 'exiftool(-k).exe')
    ]

    # Check local paths first
    for path in local_paths:
        if os.path.exists(path):
            return True

    # Check PATH
    try:
        result = subprocess.run(['exiftool', '-ver'], capture_output=True, text=True, timeout=5)
        if result.returncode == 0:
            return True
    except:
        pass

    return False

EXIFTOOL_AVAILABLE = _check_exiftool_available()
print(f"ExifTool available: {EXIFTOOL_AVAILABLE}")

class MetadataService:
    def __init__(self):
        self.supported_image_formats = ['.jpg', '.jpeg', '.tiff', '.tif']
        self.supported_video_formats = ['.mp4', '.mov', '.m4v']
        self.supported_audio_formats = ['.mp3', '.flac', '.m4a']
        self._exiftool_alt_cmd = None  # Alternative command for ExifTool if needed
    
    def read_metadata(self, file_path):
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"File not found: {file_path}")
        
        extension = os.path.splitext(file_path)[1].lower()
        
        if extension in self.supported_image_formats:
            return self._read_image_metadata(file_path)
        elif extension in self.supported_video_formats:
            return self._read_video_metadata(file_path)
        elif extension in self.supported_audio_formats:
            return self._read_audio_metadata(file_path)
        else:
            return {}
    
    def write_metadata(self, file_path, metadata_dict):
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"File not found: {file_path}")

        # Check if file is writable
        if not os.access(file_path, os.W_OK):
            raise PermissionError(f"File is not writable: {file_path}")

        extension = os.path.splitext(file_path)[1].lower()

        print(f"Writing metadata to {file_path}")
        print(f"Metadata fields: {list(metadata_dict.keys())}")

        # Try ExifTool first (most reliable)
        if self._write_with_exiftool(file_path, metadata_dict):
            print("Successfully wrote metadata with ExifTool")
            return True

        # Fallback to library-specific methods
        if extension in self.supported_image_formats:
            result = self._write_image_metadata_simple(file_path, metadata_dict)
        elif extension in self.supported_video_formats:
            result = self._write_video_metadata(file_path, metadata_dict)
        elif extension in self.supported_audio_formats:
            result = self._write_audio_metadata_simple(file_path, metadata_dict)
        else:
            raise ValueError(f"Unsupported file format: {extension}")

        print(f"Write result: {result}")
        return result

    def _write_with_exiftool(self, file_path, metadata_dict):
        """Write metadata using ExifTool (most reliable method)"""
        try:
            # Get ExifTool path
            exiftool_path = self._get_exiftool_path()
            if not exiftool_path:
                return False

            # Build command - check if we have alternative command
            if hasattr(self, '_exiftool_alt_cmd') and self._exiftool_alt_cmd:
                cmd = self._exiftool_alt_cmd + ['-overwrite_original']
            else:
                cmd = [exiftool_path, '-overwrite_original']

            # Map common fields to ExifTool tags
            tag_mapping = {
                'Title': '-Title',
                'Artist': '-Artist',
                'Album': '-Album',
                'Genre': '-Genre',
                'Year': '-Year',
                'Track': '-Track',
                'Comment': '-Comment',
                'Description': '-Description',
                'Creator': '-Creator',
                'Copyright': '-Copyright',
                'Make': '-Make',
                'Model': '-Model',
                'DateTime': '-DateTime'
            }

            for key, value in metadata_dict.items():
                if key in tag_mapping:
                    cmd.extend([f'{tag_mapping[key]}={value}'])
                else:
                    # Custom field
                    cmd.extend([f'-XMP:{key}={value}'])

            cmd.append(file_path)

            print(f"ExifTool command: {' '.join(cmd)}")
            # Use CREATE_NO_WINDOW flag on Windows to prevent console window
            creation_flags = subprocess.CREATE_NO_WINDOW if os.name == 'nt' else 0
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=30, creationflags=creation_flags)

            if result.returncode == 0:
                print("ExifTool write successful")
                return True
            else:
                print(f"ExifTool error: {result.stderr}")
                return False

        except FileNotFoundError:
            print("ExifTool not found in PATH")
            return False
        except subprocess.TimeoutExpired:
            print("ExifTool timeout")
            return False
        except Exception as e:
            print(f"ExifTool error: {e}")
            return False

    def _get_exiftool_path(self):
        """Find ExifTool executable"""
        # Check local directory first
        script_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        local_paths = [
            os.path.join(script_dir, 'exiftool-13.32_64', 'exiftool.exe'),
            os.path.join(script_dir, 'exiftool-13.32_64', 'exiftool(-k).exe'),
            os.path.join(script_dir, 'exiftool', 'exiftool.exe'),
            os.path.join(script_dir, 'exiftool', 'exiftool(-k).exe')
        ]

        for path in local_paths:
            if os.path.exists(path):
                # Validate that it's actually an executable, not a script
                if self._validate_exiftool_executable(path):
                    print(f"Found valid ExifTool at: {path}")
                    return path
                else:
                    print(f"Found ExifTool but validation failed: {path}")

        # Check PATH
        try:
            result = subprocess.run(['exiftool', '-ver'], capture_output=True, text=True, timeout=5)
            if result.returncode == 0:
                print("Found ExifTool in PATH")
                return 'exiftool'
        except:
            pass

        # Check common installation paths
        common_paths = [
            r'C:\Program Files\ExifTool\exiftool.exe',
            r'C:\Program Files (x86)\ExifTool\exiftool.exe',
            r'C:\ExifTool\exiftool.exe'
        ]

        for path in common_paths:
            if os.path.exists(path):
                if self._validate_exiftool_executable(path):
                    print(f"Found valid ExifTool at: {path}")
                    return path

        print("ExifTool not found")
        return None

    def _validate_exiftool_executable(self, exe_path):
        """Validate that ExifTool executable works properly"""
        try:
            # For Windows, try different approaches if the main exe fails
            if os.name == 'nt' and exe_path.endswith('.exe'):
                # First try the direct executable
                result = subprocess.run([exe_path, '-ver'],
                                      capture_output=True,
                                      text=True,
                                      timeout=10,
                                      creationflags=subprocess.CREATE_NO_WINDOW)

                if result.returncode == 0 and result.stdout.strip():
                    print(f"ExifTool version: {result.stdout.strip()}")
                    return True

                # If direct exe fails, try using perl directly with the .pl script
                exe_dir = os.path.dirname(exe_path)
                perl_exe = os.path.join(exe_dir, 'exiftool_files', 'perl.exe')
                pl_script = os.path.join(exe_dir, 'exiftool_files', 'exiftool.pl')

                if os.path.exists(perl_exe) and os.path.exists(pl_script):
                    print(f"Trying alternative: {perl_exe} {pl_script}")
                    result = subprocess.run([perl_exe, pl_script, '-ver'],
                                          capture_output=True,
                                          text=True,
                                          timeout=10,
                                          creationflags=subprocess.CREATE_NO_WINDOW)

                    if result.returncode == 0 and result.stdout.strip():
                        print(f"ExifTool version (via perl): {result.stdout.strip()}")
                        # Store the alternative command for later use
                        self._exiftool_alt_cmd = [perl_exe, pl_script]
                        return True

                print(f"ExifTool validation failed: {result.stderr}")
                return False
            else:
                # Non-Windows or non-exe path
                result = subprocess.run([exe_path, '-ver'],
                                      capture_output=True,
                                      text=True,
                                      timeout=10)

                if result.returncode == 0 and result.stdout.strip():
                    print(f"ExifTool version: {result.stdout.strip()}")
                    return True
                else:
                    print(f"ExifTool validation failed: {result.stderr}")
                    return False

        except subprocess.TimeoutExpired:
            print("ExifTool validation timeout")
            return False
        except Exception as e:
            print(f"ExifTool validation error: {e}")
            return False

    def _write_audio_metadata_simple(self, file_path, metadata_dict):
        """Simplified audio metadata writing"""
        try:
            print(f"Writing audio metadata (simple method): {file_path}")

            # Use mutagen directly
            audiofile = mutagen.File(file_path)
            if audiofile is None:
                print("Could not open audio file")
                return False

            # Delete existing tags and create new ones
            audiofile.delete()
            audiofile.add_tags()

            print(f"Audio file type: {type(audiofile)}")

            # Write metadata based on file type
            if isinstance(audiofile, mutagen.mp3.MP3):
                # MP3 with ID3v2
                for key, value in metadata_dict.items():
                    if key.lower() == 'title':
                        audiofile.tags.add(TIT2(encoding=3, text=str(value)))
                    elif key.lower() == 'artist':
                        audiofile.tags.add(TPE1(encoding=3, text=str(value)))
                    elif key.lower() == 'album':
                        audiofile.tags.add(TALB(encoding=3, text=str(value)))
                    elif key.lower() == 'genre':
                        audiofile.tags.add(TCON(encoding=3, text=str(value)))
                    elif key.lower() == 'year':
                        audiofile.tags.add(TDRC(encoding=3, text=str(value)))
                    elif key.lower() == 'comment':
                        audiofile.tags.add(COMM(encoding=3, lang='eng', desc='', text=str(value)))
                    else:
                        # Custom field
                        audiofile.tags.add(TXXX(encoding=3, desc=key, text=str(value)))
                    print(f"Added MP3 tag: {key} = {value}")

            else:
                # Other formats (FLAC, OGG, etc.) - use simple key=value
                for key, value in metadata_dict.items():
                    audiofile.tags[key.upper()] = [str(value)]
                    print(f"Added tag: {key} = {value}")

            # Save the file
            audiofile.save()
            print("Audio metadata saved successfully")
            return True

        except Exception as e:
            print(f"Error writing audio metadata: {e}")
            import traceback
            traceback.print_exc()
            return False

    def _write_image_metadata_simple(self, file_path, metadata_dict):
        """Simplified image metadata writing using PIL only"""
        try:
            print(f"Writing image metadata (simple method): {file_path}")

            # Open image
            img = Image.open(file_path)

            # Get existing metadata
            metadata = img.info.copy() if hasattr(img, 'info') else {}

            # Add new metadata
            for key, value in metadata_dict.items():
                metadata[key] = str(value)
                print(f"Added image metadata: {key} = {value}")

            # Save with metadata
            if file_path.lower().endswith(('.jpg', '.jpeg')):
                img.save(file_path, 'JPEG', quality=95, **metadata)
            elif file_path.lower().endswith('.png'):
                img.save(file_path, 'PNG', **metadata)
            elif file_path.lower().endswith(('.tiff', '.tif')):
                img.save(file_path, 'TIFF', **metadata)
            else:
                img.save(file_path, **metadata)

            print("Image metadata saved successfully")
            return True

        except Exception as e:
            print(f"Error writing image metadata: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def _read_image_metadata(self, file_path):
        try:
            with Image.open(file_path) as img:
                exifdata = img.getexif()
                metadata = {}
                
                for tag_id in exifdata:
                    tag = TAGS.get(tag_id, tag_id)
                    data = exifdata.get(tag_id)
                    if isinstance(data, bytes):
                        data = data.decode('utf-8', errors='ignore')
                    metadata[tag] = data
                
                return metadata
        except Exception as e:
            print(f"Error reading image metadata: {e}")
            return {}
    


    def _read_video_metadata(self, file_path):
        try:
            file = mutagen.File(file_path)
            if file is None:
                return {}

            metadata = {}
            for key, value in file.tags.items() if file.tags else []:
                if isinstance(value, list) and len(value) > 0:
                    metadata[key] = str(value[0])
                else:
                    metadata[key] = str(value)

            return metadata
        except Exception as e:
            print(f"Error reading video metadata: {e}")
            return {}

    def _write_video_metadata(self, file_path, metadata_dict):
        try:
            file = mutagen.File(file_path)
            if file is None:
                return False

            if file.tags is None:
                file.add_tags()

            for key, value in metadata_dict.items():
                file.tags[key] = [str(value)]

            file.save()
            return True
        except Exception as e:
            print(f"Error writing video metadata: {e}")
            return False

    def _read_audio_metadata(self, file_path):
        try:
            file = mutagen.File(file_path)
            if file is None:
                return {}

            metadata = {}
            for key, value in file.tags.items() if file.tags else []:
                if isinstance(value, list) and len(value) > 0:
                    metadata[key] = str(value[0])
                else:
                    metadata[key] = str(value)

            return metadata
        except Exception as e:
            print(f"Error reading audio metadata: {e}")
            return {}


