#------------------------------------------------------------------------------
# File:         Qualcomm.pm
#
# Description:  Read Qualcomm APP7 meta information
#
# Revisions:    2012/02/14 - P. <PERSON> Created
#------------------------------------------------------------------------------

package Image::ExifTool::Qualcomm;

use strict;
use vars qw($VERSION);
use Image::ExifTool qw(:DataAccess :Utils);

$VERSION = '1.02';

sub ProcessQualcomm($$$);
sub ProcessDualCamera($$$);
sub MakeNameAndDesc($$);

# Qualcomm format codes (ref PH (NC))
my @qualcommFormat = (
    'int8u',    'int8s',    'int16u',   'int16s',
    'int32u',   'int32s',   'float',    'double',
);

# information found in JPEG APP7 Qualcomm Camera Attributes segment
%Image::ExifTool::Qualcomm::Main = (
    PROCESS_PROC => \&ProcessQualcomm,
    GROUPS => { 0 => 'MakerNotes', 2 => 'Camera' },
    VARS => { NO_ID => 1, NO_LOOKUP => 1 }, # too long, too many, and too obscure
    NOTES => q{
        The tags below have been observed in the JPEG APP7 "Qualcomm Camera
        Attributes" segment written by some cameras such as the HP iPAQ Voice
        Messenger.  ExifTool will extract any information found from this segment,
        even if it is not listed in this table.
    },
    'aec_current_sensor_luma' => { },
    'af_position' => { },
    'aec_current_exp_index' => { },
    'awb_sample_decision' => { },
    'asf5_enable' => { },
    'asf5_filter_mode' => { },
    'asf5_exposure_index_1' => { },
    'asf5_exposure_index_2' => { },
    'asf5_max_exposure_index' => { },
    'asf5_luma_filter[0]' => { },
    'asf5_luma_filter[1]' => { },
    'asf5_luma_filter[2]' => { },
    'asf5_luma_filter[3]' => { },
    'asf5_luma_filter[4]' => { },
    'asf5_luma_filter[5]' => { },
    'asf5_luma_filter[6]' => { },
    'asf5_luma_filter[7]' => { },
    'asf5_luma_filter[8]' => { },
    'asf5_filter1_a11' => { },
    'asf5_filter1_a12' => { },
    'asf5_filter1_a13' => { },
    'asf5_filter1_a14' => { },
    'asf5_filter1_a15' => { },
    'asf5_filter1_a21' => { },
    'asf5_filter1_a22' => { },
    'asf5_filter1_a23' => { },
    'asf5_filter1_a24' => { },
    'asf5_filter1_a25' => { },
    'asf5_filter1_a31' => { },
    'asf5_filter1_a32' => { },
    'asf5_filter1_a33' => { },
    'asf5_filter1_a34' => { },
    'asf5_filter1_a35' => { },
    'asf5_filter1_a41' => { },
    'asf5_filter1_a42' => { },
    'asf5_filter1_a43' => { },
    'asf5_filter1_a44' => { },
    'asf5_filter1_a45' => { },
    'asf5_filter1_a51' => { },
    'asf5_filter1_a52' => { },
    'asf5_filter1_a53' => { },
    'asf5_filter1_a54' => { },
    'asf5_filter1_a55' => { },
    'asf5_filter2_a11' => { },
    'asf5_filter2_a12' => { },
    'asf5_filter2_a13' => { },
    'asf5_filter2_a14' => { },
    'asf5_filter2_a15' => { },
    'asf5_filter2_a21' => { },
    'asf5_filter2_a22' => { },
    'asf5_filter2_a23' => { },
    'asf5_filter2_a24' => { },
    'asf5_filter2_a25' => { },
    'asf5_filter2_a31' => { },
    'asf5_filter2_a32' => { },
    'asf5_filter2_a33' => { },
    'asf5_filter2_a34' => { },
    'asf5_filter2_a35' => { },
    'asf5_filter2_a41' => { },
    'asf5_filter2_a42' => { },
    'asf5_filter2_a43' => { },
    'asf5_filter2_a44' => { },
    'asf5_filter2_a45' => { },
    'asf5_filter2_a51' => { },
    'asf5_filter2_a52' => { },
    'asf5_filter2_a53' => { },
    'asf5_filter2_a54' => { },
    'asf5_filter2_a55' => { },
    'asf5_nrmize_factor1' => { },
    'asf5_nrmize_factor2' => { },
    'asf5_low_lo_thres' => { },
    'asf5_low_up_thres' => { },
    'asf5_low_shrp_deg_f1' => { },
    'asf5_low_shrp_deg_f2' => { },
    'asf5_low_smth_prcnt' => { },
    'asf5_nrm_lo_thres' => { },
    'asf5_nrm_up_thres' => { },
    'asf5_nrm_shrp_deg_f1' => { },
    'asf5_nrm_shrp_deg_f2' => { },
    'asf5_nrm_smth_prcnt' => { },
    'asf5_brt_lo_thres' => { },
    'asf5_brt_up_thres' => { },
    'asf5_brt_shrp_deg_f1' => { },
    'asf5_brt_shrp_deg_f2' => { },
    'asf5_brt_smth_percent' => { },
    'asf3_enable' => { },
    'asf3_edge_filter_a11' => { },
    'asf3_edge_filter_a12' => { },
    'asf3_edge_filter_a13' => { },
    'asf3_edge_filter_a21' => { },
    'asf3_edge_filter_a22' => { },
    'asf3_edge_filter_a23' => { },
    'asf3_edge_filter_a31' => { },
    'asf3_edge_filter_a32' => { },
    'asf3_edge_filter_a33' => { },
    'asf3_noise_filter_a11' => { },
    'asf3_noise_filter_a12' => { },
    'asf3_noise_filter_a13' => { },
    'asf3_noise_filter_a21' => { },
    'asf3_noise_filter_a22' => { },
    'asf3_noise_filter_a23' => { },
    'asf3_noise_filter_a31' => { },
    'asf3_noise_filter_a32' => { },
    'asf3_noise_filter_a33' => { },
    'asf3_lower_threshold' => { },
    'asf3_upper_threshold' => { },
    'asf3_edge_detect' => { },
    'aec_enable' => { },
    'aec_mode' => { },
    'aec_aggressiveness' => { },
    'aec_luma_target' => { },
    'aec_luma_tolerance' => { },
    'aec_indoor_idx' => { },
    'aec_odoor_idx' => { },
    'aec_exposure_index_adj_step' => { },
    'aec_outdoor_gamma_index' => { },
    'aec_vfe_luma' => { },
    'aec_high_luma_region_threshold' => { },
    'aec_snapshot_sensor_gain' => { },
    'aec_snapshot_digital_gain' => { },
    'aec_snapshot_line_count' => { },
    'aec_snapshot_exposure_time_ms' => { },
    'aec_outdoor_bright_enable' => { },
    'aec_outdoor_bright_reduction' => { },
    'aec_outdoor_bright_threshold_LO' => { },
    'aec_outdoor_bright_threshold_HI' => { },
    'aec_outdoor_bright_discarded' => { },
    'aec_high_luma_region_count' => { },
    'antibanding_enable' => { },
    'anti_bading_pixel_clk' => { },
    'anti_bading_pixel_clk_per_line' => { },
    'afr_enable' => { },
    'afr_faster_0_trigger' => { },
    'afr_slower_0_trigger' => { },
    'afr_faster_0_exp_mod' => { },
    'afr_slower_0_exp_mod' => { },
    'afr_faster_1_trigger' => { },
    'afr_slower_1_trigger' => { },
    'afr_faster_1_exp_mod' => { },
    'afr_slower_1_exp_mod' => { },
    'afr_faster_2_trigger' => { },
    'afr_slower_2_trigger' => { },
    'afr_faster_2_exp_mod' => { },
    'afr_slower_2_exp_mod' => { },
    'afr_faster_3_trigger' => { },
    'afr_slower_3_trigger' => { },
    'afr_faster_3_exp_mod' => { },
    'afr_slower_3_exp_mod' => { },
    'afr_faster_4_trigger' => { },
    'afr_slower_4_trigger' => { },
    'afr_faster_4_exp_mod' => { },
    'afr_slower_4_exp_mod' => { },
    'afr_possible_frame_cnt' => { },
    'af_enable' => { },
    'af_steps_near_far' => { },
    'af_steps_near_infinity' => { },
    'af_gross_step' => { },
    'af_fine_step' => { },
    'af_fine_srch_points' => { },
    'af_process' => { },
    'af_mode' => { },
    'af_near_end' => { },
    'af_boundary' => { },
    'af_far_end' => { },
    'af_collect_end_stat' => { },
    'af_test_mode' => { },
    'af_undershoot_protect' => { },
    'af_reset_lens_after_snap' => { },
    'clip_to_af_rato' => { },
    'af_pos_def_macro' => { },
    'af_pos_def_norm' => { },
    'af_vfe_vert_offset' => { },
    'af_vfe_horz_offset' => { },
    'af_vfe_vert_height' => { },
    'af_vfe_horz_width' => { },
    'af_vfe_metric_max' => { },
    'af_trace_positions[0]' => { },
    'af_trace_positions[1]' => { },
    'af_trace_positions[2]' => { },
    'af_trace_positions[3]' => { },
    'af_trace_positions[4]' => { },
    'af_trace_positions[5]' => { },
    'af_trace_positions[6]' => { },
    'af_trace_positions[7]' => { },
    'af_trace_positions[8]' => { },
    'af_trace_positions[9]' => { },
    'af_trace_positions[10]' => { },
    'af_trace_positions[11]' => { },
    'af_trace_positions[12]' => { },
    'af_trace_positions[13]' => { },
    'af_trace_positions[14]' => { },
    'af_trace_positions[15]' => { },
    'af_trace_positions[16]' => { },
    'af_trace_positions[17]' => { },
    'af_trace_positions[18]' => { },
    'af_trace_positions[19]' => { },
    'af_trace_positions[20]' => { },
    'af_trace_positions[21]' => { },
    'af_trace_positions[22]' => { },
    'af_trace_positions[23]' => { },
    'af_trace_positions[24]' => { },
    'af_trace_positions[25]' => { },
    'af_trace_positions[26]' => { },
    'af_trace_positions[27]' => { },
    'af_trace_positions[28]' => { },
    'af_trace_positions[29]' => { },
    'af_trace_positions[30]' => { },
    'af_trace_positions[31]' => { },
    'af_trace_positions[32]' => { },
    'af_trace_positions[33]' => { },
    'af_trace_positions[34]' => { },
    'af_trace_positions[35]' => { },
    'af_trace_positions[36]' => { },
    'af_trace_positions[37]' => { },
    'af_trace_positions[38]' => { },
    'af_trace_positions[39]' => { },
    'af_trace_positions[40]' => { },
    'af_trace_positions[41]' => { },
    'af_trace_positions[42]' => { },
    'af_trace_positions[43]' => { },
    'af_trace_positions[44]' => { },
    'af_trace_positions[45]' => { },
    'af_trace_positions[46]' => { },
    'af_trace_positions[47]' => { },
    'af_trace_positions[48]' => { },
    'af_trace_positions[49]' => { },
    'af_trace_stats[0]' => { },
    'af_trace_stats[1]' => { },
    'af_trace_stats[2]' => { },
    'af_trace_stats[3]' => { },
    'af_trace_stats[4]' => { },
    'af_trace_stats[5]' => { },
    'af_trace_stats[6]' => { },
    'af_trace_stats[7]' => { },
    'af_trace_stats[8]' => { },
    'af_trace_stats[9]' => { },
    'af_trace_stats[10]' => { },
    'af_trace_stats[11]' => { },
    'af_trace_stats[12]' => { },
    'af_trace_stats[13]' => { },
    'af_trace_stats[14]' => { },
    'af_trace_stats[15]' => { },
    'af_trace_stats[16]' => { },
    'af_trace_stats[17]' => { },
    'af_trace_stats[18]' => { },
    'af_trace_stats[19]' => { },
    'af_trace_stats[20]' => { },
    'af_trace_stats[21]' => { },
    'af_trace_stats[22]' => { },
    'af_trace_stats[23]' => { },
    'af_trace_stats[24]' => { },
    'af_trace_stats[25]' => { },
    'af_trace_stats[26]' => { },
    'af_trace_stats[27]' => { },
    'af_trace_stats[28]' => { },
    'af_trace_stats[29]' => { },
    'af_trace_stats[30]' => { },
    'af_trace_stats[31]' => { },
    'af_trace_stats[32]' => { },
    'af_trace_stats[33]' => { },
    'af_trace_stats[34]' => { },
    'af_trace_stats[35]' => { },
    'af_trace_stats[36]' => { },
    'af_trace_stats[37]' => { },
    'af_trace_stats[38]' => { },
    'af_trace_stats[39]' => { },
    'af_trace_stats[40]' => { },
    'af_trace_stats[41]' => { },
    'af_trace_stats[42]' => { },
    'af_trace_stats[43]' => { },
    'af_trace_stats[44]' => { },
    'af_trace_stats[45]' => { },
    'af_trace_stats[46]' => { },
    'af_trace_stats[47]' => { },
    'af_trace_stats[48]' => { },
    'af_trace_stats[49]' => { },
    'af_focus_time' => { },
    'awb_enable' => { },
    'awb_algorithm' => { },
    'awb_aggressiveness' => { },
    'awb_red_gain_ref1' => { },
    'awb_blue_gain_ref1' => { },
    'awb_red_gain_adj_ref1' => { },
    'awb_blue_gain_adj_ref1' => { },
    'awb_red_gain_ref2' => { },
    'awb_blue_gain_ref2' => { },
    'awb_red_gain_adj_ref2' => { },
    'awb_blue_gain_adj_ref2' => { },
    'awb_red_gain_ref3' => { },
    'awb_blue_gain_ref3' => { },
    'awb_red_gain_adj_ref3' => { },
    'awb_blue_gain_adj_ref3' => { },
    'awb_red_gain_ref4' => { },
    'awb_blue_gain_ref4' => { },
    'awb_red_gain_adj_ref4' => { },
    'awb_blue_gain_adj_ref4' => { },
    'awb_red_gain_ref5' => { },
    'awb_blue_gain_ref5' => { },
    'awb_red_gain_adj_ref5' => { },
    'awb_blue_gain_adj_ref5' => { },
    'awb_red_gain_ref6' => { },
    'awb_blue_gain_ref6' => { },
    'awb_red_gain_adj_ref6' => { },
    'awb_blue_gain_adj_ref6' => { },
    'awb_red_gain_ref7' => { },
    'awb_blue_gain_ref7' => { },
    'awb_red_gain_adj_ref7' => { },
    'awb_blue_gain_adj_ref7' => { },
    'awb_red_gain_ref8' => { },
    'awb_blue_gain_ref8' => { },
    'awb_red_gain_adj_ref8' => { },
    'awb_blue_gain_adj_ref8' => { },
    'awb_lo_vfe_max_y' => { },
    'awb_lo_vfe_min_y' => { },
    'awb_lo_vfe_m1' => { },
    'awb_lo_vfe_m2' => { },
    'awb_lo_vfe_m3' => { },
    'awb_lo_vfe_m4' => { },
    'awb_lo_vfe_c1' => { },
    'awb_lo_vfe_c2' => { },
    'awb_lo_vfe_c3' => { },
    'awb_lo_vfe_c4' => { },
    'awb_norm_vfe_max_y' => { },
    'awb_norm_vfe_min_y' => { },
    'awb_norm_vfe_m1' => { },
    'awb_norm_vfe_m2' => { },
    'awb_norm_vfe_m3' => { },
    'awb_norm_vfe_m4' => { },
    'awb_norm_vfe_c1' => { },
    'awb_norm_vfe_c2' => { },
    'awb_norm_vfe_c3' => { },
    'awb_norm_vfe_c4' => { },
    'awb_oudor_vfe_max_y' => { },
    'awb_oudor_vfe_min_y' => { },
    'awb_oudor_vfe_m1' => { },
    'awb_oudor_vfe_m2' => { },
    'awb_oudor_vfe_m3' => { },
    'awb_oudor_vfe_m4' => { },
    'awb_oudor_vfe_c1' => { },
    'awb_oudor_vfe_c2' => { },
    'awb_oudor_vfe_c3' => { },
    'awb_oudor_vfe_c4' => { },
    'awb_cc_bias' => { },
    'awb_min_r_gain' => { },
    'awb_min_g_gain' => { },
    'awb_min_b_gain' => { },
    'awb_max_r_gain' => { },
    'awb_max_g_gain' => { },
    'awb_max_b_gain' => { },
    'awb_outdoor_sample_influence' => { },
    'awb_indoor_sample_influence' => { },
    'awb_low_lig_col_cor_ena' => { },
    'awb_agw_grid_dist_2_thresh' => { },
    'awb_ave_rg_ratio' => { },
    'awb_ave_bg_ratio' => { },
    'awb_compact_cluster_R2' => { },
    'outlier_distance' => { },
    'awb_green_offset_rg' => { },
    'awb_green_offset_bg' => { },
    'awb_prev_wb_rgain' => { },
    'awb_prev_wb_ggain' => { },
    'awb_prev_wb_bgain' => { },
    'awb_snapshot_r_gain' => { },
    'awb_snapshot_b_gain' => { },
    'rolloff_enable' => { },
    'r2_tl84_cx' => { },
    'r2_tl84_cy' => { },
    'r2_tl84_width' => { },
    'r2_tl84_height' => { },
    'r2_tl84_intervals' => { },
    'r2_tl84_tbl[0]' => { },
    'r2_tl84_tbl[1]' => { },
    'r2_tl84_tbl[2]' => { },
    'r2_tl84_tbl[3]' => { },
    'r2_tl84_tbl[4]' => { },
    'r2_tl84_tbl[5]' => { },
    'r2_tl84_tbl[6]' => { },
    'r2_tl84_tbl[7]' => { },
    'r2_tl84_tbl[8]' => { },
    'r2_tl84_tbl[9]' => { },
    'r2_tl84_tbl[10]' => { },
    'r2_tl84_tbl[11]' => { },
    'r2_tl84_tbl[12]' => { },
    'r2_tl84_tbl[13]' => { },
    'r2_tl84_tbl[14]' => { },
    'r2_tl84_tbl[15]' => { },
    'r2_tl84_tbl[16]' => { },
    'r2_tl84_tbl[17]' => { },
    'r2_tl84_tbl[18]' => { },
    'r2_tl84_tbl[19]' => { },
    'r2_tl84_tbl[20]' => { },
    'r2_tl84_tbl[21]' => { },
    'r2_tl84_tbl[22]' => { },
    'r2_tl84_tbl[23]' => { },
    'r2_tl84_tbl[24]' => { },
    'r2_tl84_tbl[25]' => { },
    'r2_tl84_tbl[26]' => { },
    'r2_tl84_tbl[27]' => { },
    'r2_tl84_tbl[28]' => { },
    'r2_tl84_tbl[29]' => { },
    'r2_tl84_tbl[30]' => { },
    'r2_tl84_tbl[31]' => { },
    'r2_tl84_red_ctbl[0]' => { },
    'r2_tl84_red_ctbl[1]' => { },
    'r2_tl84_red_ctbl[2]' => { },
    'r2_tl84_red_ctbl[3]' => { },
    'r2_tl84_red_ctbl[4]' => { },
    'r2_tl84_red_ctbl[5]' => { },
    'r2_tl84_red_ctbl[6]' => { },
    'r2_tl84_red_ctbl[7]' => { },
    'r2_tl84_red_ctbl[8]' => { },
    'r2_tl84_red_ctbl[9]' => { },
    'r2_tl84_red_ctbl[10]' => { },
    'r2_tl84_red_ctbl[11]' => { },
    'r2_tl84_red_ctbl[12]' => { },
    'r2_tl84_red_ctbl[13]' => { },
    'r2_tl84_red_ctbl[14]' => { },
    'r2_tl84_red_ctbl[15]' => { },
    'r2_tl84_red_ctbl[16]' => { },
    'r2_tl84_red_ctbl[17]' => { },
    'r2_tl84_red_ctbl[18]' => { },
    'r2_tl84_red_ctbl[19]' => { },
    'r2_tl84_red_ctbl[20]' => { },
    'r2_tl84_red_ctbl[21]' => { },
    'r2_tl84_red_ctbl[22]' => { },
    'r2_tl84_red_ctbl[23]' => { },
    'r2_tl84_red_ctbl[24]' => { },
    'r2_tl84_red_ctbl[25]' => { },
    'r2_tl84_red_ctbl[26]' => { },
    'r2_tl84_red_ctbl[27]' => { },
    'r2_tl84_red_ctbl[28]' => { },
    'r2_tl84_red_ctbl[29]' => { },
    'r2_tl84_red_ctbl[30]' => { },
    'r2_tl84_red_ctbl[31]' => { },
    'r2_tl84_green_ctbl[0]' => { },
    'r2_tl84_green_ctbl[1]' => { },
    'r2_tl84_green_ctbl[2]' => { },
    'r2_tl84_green_ctbl[3]' => { },
    'r2_tl84_green_ctbl[4]' => { },
    'r2_tl84_green_ctbl[5]' => { },
    'r2_tl84_green_ctbl[6]' => { },
    'r2_tl84_green_ctbl[7]' => { },
    'r2_tl84_green_ctbl[8]' => { },
    'r2_tl84_green_ctbl[9]' => { },
    'r2_tl84_green_ctbl[10]' => { },
    'r2_tl84_green_ctbl[11]' => { },
    'r2_tl84_green_ctbl[12]' => { },
    'r2_tl84_green_ctbl[13]' => { },
    'r2_tl84_green_ctbl[14]' => { },
    'r2_tl84_green_ctbl[15]' => { },
    'r2_tl84_green_ctbl[16]' => { },
    'r2_tl84_green_ctbl[17]' => { },
    'r2_tl84_green_ctbl[18]' => { },
    'r2_tl84_green_ctbl[19]' => { },
    'r2_tl84_green_ctbl[20]' => { },
    'r2_tl84_green_ctbl[21]' => { },
    'r2_tl84_green_ctbl[22]' => { },
    'r2_tl84_green_ctbl[23]' => { },
    'r2_tl84_green_ctbl[24]' => { },
    'r2_tl84_green_ctbl[25]' => { },
    'r2_tl84_green_ctbl[26]' => { },
    'r2_tl84_green_ctbl[27]' => { },
    'r2_tl84_green_ctbl[28]' => { },
    'r2_tl84_green_ctbl[29]' => { },
    'r2_tl84_green_ctbl[30]' => { },
    'r2_tl84_green_ctbl[31]' => { },
    'r2_tl84_blue_ctbl[0]' => { },
    'r2_tl84_blue_ctbl[1]' => { },
    'r2_tl84_blue_ctbl[2]' => { },
    'r2_tl84_blue_ctbl[3]' => { },
    'r2_tl84_blue_ctbl[4]' => { },
    'r2_tl84_blue_ctbl[5]' => { },
    'r2_tl84_blue_ctbl[6]' => { },
    'r2_tl84_blue_ctbl[7]' => { },
    'r2_tl84_blue_ctbl[8]' => { },
    'r2_tl84_blue_ctbl[9]' => { },
    'r2_tl84_blue_ctbl[10]' => { },
    'r2_tl84_blue_ctbl[11]' => { },
    'r2_tl84_blue_ctbl[12]' => { },
    'r2_tl84_blue_ctbl[13]' => { },
    'r2_tl84_blue_ctbl[14]' => { },
    'r2_tl84_blue_ctbl[15]' => { },
    'r2_tl84_blue_ctbl[16]' => { },
    'r2_tl84_blue_ctbl[17]' => { },
    'r2_tl84_blue_ctbl[18]' => { },
    'r2_tl84_blue_ctbl[19]' => { },
    'r2_tl84_blue_ctbl[20]' => { },
    'r2_tl84_blue_ctbl[21]' => { },
    'r2_tl84_blue_ctbl[22]' => { },
    'r2_tl84_blue_ctbl[23]' => { },
    'r2_tl84_blue_ctbl[24]' => { },
    'r2_tl84_blue_ctbl[25]' => { },
    'r2_tl84_blue_ctbl[26]' => { },
    'r2_tl84_blue_ctbl[27]' => { },
    'r2_tl84_blue_ctbl[28]' => { },
    'r2_tl84_blue_ctbl[29]' => { },
    'r2_tl84_blue_ctbl[30]' => { },
    'r2_tl84_blue_ctbl[31]' => { },
    'r2_tl84_red_stbl[0]' => { },
    'r2_tl84_red_stbl[1]' => { },
    'r2_tl84_red_stbl[2]' => { },
    'r2_tl84_red_stbl[3]' => { },
    'r2_tl84_red_stbl[4]' => { },
    'r2_tl84_red_stbl[5]' => { },
    'r2_tl84_red_stbl[6]' => { },
    'r2_tl84_red_stbl[7]' => { },
    'r2_tl84_red_stbl[8]' => { },
    'r2_tl84_red_stbl[9]' => { },
    'r2_tl84_red_stbl[10]' => { },
    'r2_tl84_red_stbl[11]' => { },
    'r2_tl84_red_stbl[12]' => { },
    'r2_tl84_red_stbl[13]' => { },
    'r2_tl84_red_stbl[14]' => { },
    'r2_tl84_red_stbl[15]' => { },
    'r2_tl84_red_stbl[16]' => { },
    'r2_tl84_red_stbl[17]' => { },
    'r2_tl84_red_stbl[18]' => { },
    'r2_tl84_red_stbl[19]' => { },
    'r2_tl84_red_stbl[20]' => { },
    'r2_tl84_red_stbl[21]' => { },
    'r2_tl84_red_stbl[22]' => { },
    'r2_tl84_red_stbl[23]' => { },
    'r2_tl84_red_stbl[24]' => { },
    'r2_tl84_red_stbl[25]' => { },
    'r2_tl84_red_stbl[26]' => { },
    'r2_tl84_red_stbl[27]' => { },
    'r2_tl84_red_stbl[28]' => { },
    'r2_tl84_red_stbl[29]' => { },
    'r2_tl84_red_stbl[30]' => { },
    'r2_tl84_red_stbl[31]' => { },
    'r2_tl84_blue_stbl[0]' => { },
    'r2_tl84_blue_stbl[1]' => { },
    'r2_tl84_blue_stbl[2]' => { },
    'r2_tl84_blue_stbl[3]' => { },
    'r2_tl84_blue_stbl[4]' => { },
    'r2_tl84_blue_stbl[5]' => { },
    'r2_tl84_blue_stbl[6]' => { },
    'r2_tl84_blue_stbl[7]' => { },
    'r2_tl84_blue_stbl[8]' => { },
    'r2_tl84_blue_stbl[9]' => { },
    'r2_tl84_blue_stbl[10]' => { },
    'r2_tl84_blue_stbl[11]' => { },
    'r2_tl84_blue_stbl[12]' => { },
    'r2_tl84_blue_stbl[13]' => { },
    'r2_tl84_blue_stbl[14]' => { },
    'r2_tl84_blue_stbl[15]' => { },
    'r2_tl84_blue_stbl[16]' => { },
    'r2_tl84_blue_stbl[17]' => { },
    'r2_tl84_blue_stbl[18]' => { },
    'r2_tl84_blue_stbl[19]' => { },
    'r2_tl84_blue_stbl[20]' => { },
    'r2_tl84_blue_stbl[21]' => { },
    'r2_tl84_blue_stbl[22]' => { },
    'r2_tl84_blue_stbl[23]' => { },
    'r2_tl84_blue_stbl[24]' => { },
    'r2_tl84_blue_stbl[25]' => { },
    'r2_tl84_blue_stbl[26]' => { },
    'r2_tl84_blue_stbl[27]' => { },
    'r2_tl84_blue_stbl[28]' => { },
    'r2_tl84_blue_stbl[29]' => { },
    'r2_tl84_blue_stbl[30]' => { },
    'r2_tl84_blue_stbl[31]' => { },
    'r2_tl84_green_stbl[0]' => { },
    'r2_tl84_green_stbl[1]' => { },
    'r2_tl84_green_stbl[2]' => { },
    'r2_tl84_green_stbl[3]' => { },
    'r2_tl84_green_stbl[4]' => { },
    'r2_tl84_green_stbl[5]' => { },
    'r2_tl84_green_stbl[6]' => { },
    'r2_tl84_green_stbl[7]' => { },
    'r2_tl84_green_stbl[8]' => { },
    'r2_tl84_green_stbl[9]' => { },
    'r2_tl84_green_stbl[10]' => { },
    'r2_tl84_green_stbl[11]' => { },
    'r2_tl84_green_stbl[12]' => { },
    'r2_tl84_green_stbl[13]' => { },
    'r2_tl84_green_stbl[14]' => { },
    'r2_tl84_green_stbl[15]' => { },
    'r2_tl84_green_stbl[16]' => { },
    'r2_tl84_green_stbl[17]' => { },
    'r2_tl84_green_stbl[18]' => { },
    'r2_tl84_green_stbl[19]' => { },
    'r2_tl84_green_stbl[20]' => { },
    'r2_tl84_green_stbl[21]' => { },
    'r2_tl84_green_stbl[22]' => { },
    'r2_tl84_green_stbl[23]' => { },
    'r2_tl84_green_stbl[24]' => { },
    'r2_tl84_green_stbl[25]' => { },
    'r2_tl84_green_stbl[26]' => { },
    'r2_tl84_green_stbl[27]' => { },
    'r2_tl84_green_stbl[28]' => { },
    'r2_tl84_green_stbl[29]' => { },
    'r2_tl84_green_stbl[30]' => { },
    'r2_tl84_green_stbl[31]' => { },
    'r2_d65_cx' => { },
    'r2_d65_cy' => { },
    'r2_d65_width' => { },
    'r2_d65_height' => { },
    'r2_d65_intervals' => { },
    'r2_d65_tbl[0]' => { },
    'r2_d65_tbl[1]' => { },
    'r2_d65_tbl[2]' => { },
    'r2_d65_tbl[3]' => { },
    'r2_d65_tbl[4]' => { },
    'r2_d65_tbl[5]' => { },
    'r2_d65_tbl[6]' => { },
    'r2_d65_tbl[7]' => { },
    'r2_d65_tbl[8]' => { },
    'r2_d65_tbl[9]' => { },
    'r2_d65_tbl[10]' => { },
    'r2_d65_tbl[11]' => { },
    'r2_d65_tbl[12]' => { },
    'r2_d65_tbl[13]' => { },
    'r2_d65_tbl[14]' => { },
    'r2_d65_tbl[15]' => { },
    'r2_d65_tbl[16]' => { },
    'r2_d65_tbl[17]' => { },
    'r2_d65_tbl[18]' => { },
    'r2_d65_tbl[19]' => { },
    'r2_d65_tbl[20]' => { },
    'r2_d65_tbl[21]' => { },
    'r2_d65_tbl[22]' => { },
    'r2_d65_tbl[23]' => { },
    'r2_d65_tbl[24]' => { },
    'r2_d65_tbl[25]' => { },
    'r2_d65_tbl[26]' => { },
    'r2_d65_tbl[27]' => { },
    'r2_d65_tbl[28]' => { },
    'r2_d65_tbl[29]' => { },
    'r2_d65_tbl[30]' => { },
    'r2_d65_tbl[31]' => { },
    'r2_d65_red_ctbl[0]' => { },
    'r2_d65_red_ctbl[1]' => { },
    'r2_d65_red_ctbl[2]' => { },
    'r2_d65_red_ctbl[3]' => { },
    'r2_d65_red_ctbl[4]' => { },
    'r2_d65_red_ctbl[5]' => { },
    'r2_d65_red_ctbl[6]' => { },
    'r2_d65_red_ctbl[7]' => { },
    'r2_d65_red_ctbl[8]' => { },
    'r2_d65_red_ctbl[9]' => { },
    'r2_d65_red_ctbl[10]' => { },
    'r2_d65_red_ctbl[11]' => { },
    'r2_d65_red_ctbl[12]' => { },
    'r2_d65_red_ctbl[13]' => { },
    'r2_d65_red_ctbl[14]' => { },
    'r2_d65_red_ctbl[15]' => { },
    'r2_d65_red_ctbl[16]' => { },
    'r2_d65_red_ctbl[17]' => { },
    'r2_d65_red_ctbl[18]' => { },
    'r2_d65_red_ctbl[19]' => { },
    'r2_d65_red_ctbl[20]' => { },
    'r2_d65_red_ctbl[21]' => { },
    'r2_d65_red_ctbl[22]' => { },
    'r2_d65_red_ctbl[23]' => { },
    'r2_d65_red_ctbl[24]' => { },
    'r2_d65_red_ctbl[25]' => { },
    'r2_d65_red_ctbl[26]' => { },
    'r2_d65_red_ctbl[27]' => { },
    'r2_d65_red_ctbl[28]' => { },
    'r2_d65_red_ctbl[29]' => { },
    'r2_d65_red_ctbl[30]' => { },
    'r2_d65_red_ctbl[31]' => { },
    'r2_d65_green_ctbl[0]' => { },
    'r2_d65_green_ctbl[1]' => { },
    'r2_d65_green_ctbl[2]' => { },
    'r2_d65_green_ctbl[3]' => { },
    'r2_d65_green_ctbl[4]' => { },
    'r2_d65_green_ctbl[5]' => { },
    'r2_d65_green_ctbl[6]' => { },
    'r2_d65_green_ctbl[7]' => { },
    'r2_d65_green_ctbl[8]' => { },
    'r2_d65_green_ctbl[9]' => { },
    'r2_d65_green_ctbl[10]' => { },
    'r2_d65_green_ctbl[11]' => { },
    'r2_d65_green_ctbl[12]' => { },
    'r2_d65_green_ctbl[13]' => { },
    'r2_d65_green_ctbl[14]' => { },
    'r2_d65_green_ctbl[15]' => { },
    'r2_d65_green_ctbl[16]' => { },
    'r2_d65_green_ctbl[17]' => { },
    'r2_d65_green_ctbl[18]' => { },
    'r2_d65_green_ctbl[19]' => { },
    'r2_d65_green_ctbl[20]' => { },
    'r2_d65_green_ctbl[21]' => { },
    'r2_d65_green_ctbl[22]' => { },
    'r2_d65_green_ctbl[23]' => { },
    'r2_d65_green_ctbl[24]' => { },
    'r2_d65_green_ctbl[25]' => { },
    'r2_d65_green_ctbl[26]' => { },
    'r2_d65_green_ctbl[27]' => { },
    'r2_d65_green_ctbl[28]' => { },
    'r2_d65_green_ctbl[29]' => { },
    'r2_d65_green_ctbl[30]' => { },
    'r2_d65_green_ctbl[31]' => { },
    'r2_d65_blue_ctbl[0]' => { },
    'r2_d65_blue_ctbl[1]' => { },
    'r2_d65_blue_ctbl[2]' => { },
    'r2_d65_blue_ctbl[3]' => { },
    'r2_d65_blue_ctbl[4]' => { },
    'r2_d65_blue_ctbl[5]' => { },
    'r2_d65_blue_ctbl[6]' => { },
    'r2_d65_blue_ctbl[7]' => { },
    'r2_d65_blue_ctbl[8]' => { },
    'r2_d65_blue_ctbl[9]' => { },
    'r2_d65_blue_ctbl[10]' => { },
    'r2_d65_blue_ctbl[11]' => { },
    'r2_d65_blue_ctbl[12]' => { },
    'r2_d65_blue_ctbl[13]' => { },
    'r2_d65_blue_ctbl[14]' => { },
    'r2_d65_blue_ctbl[15]' => { },
    'r2_d65_blue_ctbl[16]' => { },
    'r2_d65_blue_ctbl[17]' => { },
    'r2_d65_blue_ctbl[18]' => { },
    'r2_d65_blue_ctbl[19]' => { },
    'r2_d65_blue_ctbl[20]' => { },
    'r2_d65_blue_ctbl[21]' => { },
    'r2_d65_blue_ctbl[22]' => { },
    'r2_d65_blue_ctbl[23]' => { },
    'r2_d65_blue_ctbl[24]' => { },
    'r2_d65_blue_ctbl[25]' => { },
    'r2_d65_blue_ctbl[26]' => { },
    'r2_d65_blue_ctbl[27]' => { },
    'r2_d65_blue_ctbl[28]' => { },
    'r2_d65_blue_ctbl[29]' => { },
    'r2_d65_blue_ctbl[30]' => { },
    'r2_d65_blue_ctbl[31]' => { },
    'r2_d65_red_stbl[0]' => { },
    'r2_d65_red_stbl[1]' => { },
    'r2_d65_red_stbl[2]' => { },
    'r2_d65_red_stbl[3]' => { },
    'r2_d65_red_stbl[4]' => { },
    'r2_d65_red_stbl[5]' => { },
    'r2_d65_red_stbl[6]' => { },
    'r2_d65_red_stbl[7]' => { },
    'r2_d65_red_stbl[8]' => { },
    'r2_d65_red_stbl[9]' => { },
    'r2_d65_red_stbl[10]' => { },
    'r2_d65_red_stbl[11]' => { },
    'r2_d65_red_stbl[12]' => { },
    'r2_d65_red_stbl[13]' => { },
    'r2_d65_red_stbl[14]' => { },
    'r2_d65_red_stbl[15]' => { },
    'r2_d65_red_stbl[16]' => { },
    'r2_d65_red_stbl[17]' => { },
    'r2_d65_red_stbl[18]' => { },
    'r2_d65_red_stbl[19]' => { },
    'r2_d65_red_stbl[20]' => { },
    'r2_d65_red_stbl[21]' => { },
    'r2_d65_red_stbl[22]' => { },
    'r2_d65_red_stbl[23]' => { },
    'r2_d65_red_stbl[24]' => { },
    'r2_d65_red_stbl[25]' => { },
    'r2_d65_red_stbl[26]' => { },
    'r2_d65_red_stbl[27]' => { },
    'r2_d65_red_stbl[28]' => { },
    'r2_d65_red_stbl[29]' => { },
    'r2_d65_red_stbl[30]' => { },
    'r2_d65_red_stbl[31]' => { },
    'r2_d65_blue_stbl[0]' => { },
    'r2_d65_blue_stbl[1]' => { },
    'r2_d65_blue_stbl[2]' => { },
    'r2_d65_blue_stbl[3]' => { },
    'r2_d65_blue_stbl[4]' => { },
    'r2_d65_blue_stbl[5]' => { },
    'r2_d65_blue_stbl[6]' => { },
    'r2_d65_blue_stbl[7]' => { },
    'r2_d65_blue_stbl[8]' => { },
    'r2_d65_blue_stbl[9]' => { },
    'r2_d65_blue_stbl[10]' => { },
    'r2_d65_blue_stbl[11]' => { },
    'r2_d65_blue_stbl[12]' => { },
    'r2_d65_blue_stbl[13]' => { },
    'r2_d65_blue_stbl[14]' => { },
    'r2_d65_blue_stbl[15]' => { },
    'r2_d65_blue_stbl[16]' => { },
    'r2_d65_blue_stbl[17]' => { },
    'r2_d65_blue_stbl[18]' => { },
    'r2_d65_blue_stbl[19]' => { },
    'r2_d65_blue_stbl[20]' => { },
    'r2_d65_blue_stbl[21]' => { },
    'r2_d65_blue_stbl[22]' => { },
    'r2_d65_blue_stbl[23]' => { },
    'r2_d65_blue_stbl[24]' => { },
    'r2_d65_blue_stbl[25]' => { },
    'r2_d65_blue_stbl[26]' => { },
    'r2_d65_blue_stbl[27]' => { },
    'r2_d65_blue_stbl[28]' => { },
    'r2_d65_blue_stbl[29]' => { },
    'r2_d65_blue_stbl[30]' => { },
    'r2_d65_blue_stbl[31]' => { },
    'r2_d65_green_stbl[0]' => { },
    'r2_d65_green_stbl[1]' => { },
    'r2_d65_green_stbl[2]' => { },
    'r2_d65_green_stbl[3]' => { },
    'r2_d65_green_stbl[4]' => { },
    'r2_d65_green_stbl[5]' => { },
    'r2_d65_green_stbl[6]' => { },
    'r2_d65_green_stbl[7]' => { },
    'r2_d65_green_stbl[8]' => { },
    'r2_d65_green_stbl[9]' => { },
    'r2_d65_green_stbl[10]' => { },
    'r2_d65_green_stbl[11]' => { },
    'r2_d65_green_stbl[12]' => { },
    'r2_d65_green_stbl[13]' => { },
    'r2_d65_green_stbl[14]' => { },
    'r2_d65_green_stbl[15]' => { },
    'r2_d65_green_stbl[16]' => { },
    'r2_d65_green_stbl[17]' => { },
    'r2_d65_green_stbl[18]' => { },
    'r2_d65_green_stbl[19]' => { },
    'r2_d65_green_stbl[20]' => { },
    'r2_d65_green_stbl[21]' => { },
    'r2_d65_green_stbl[22]' => { },
    'r2_d65_green_stbl[23]' => { },
    'r2_d65_green_stbl[24]' => { },
    'r2_d65_green_stbl[25]' => { },
    'r2_d65_green_stbl[26]' => { },
    'r2_d65_green_stbl[27]' => { },
    'r2_d65_green_stbl[28]' => { },
    'r2_d65_green_stbl[29]' => { },
    'r2_d65_green_stbl[30]' => { },
    'r2_d65_green_stbl[31]' => { },
    'r2_a_cx' => { },
    'r2_a_cy' => { },
    'r2_a_width' => { },
    'r2_a_height' => { },
    'r2_a_intervals' => { },
    'r2_a_tbl[0]' => { },
    'r2_a_tbl[1]' => { },
    'r2_a_tbl[2]' => { },
    'r2_a_tbl[3]' => { },
    'r2_a_tbl[4]' => { },
    'r2_a_tbl[5]' => { },
    'r2_a_tbl[6]' => { },
    'r2_a_tbl[7]' => { },
    'r2_a_tbl[8]' => { },
    'r2_a_tbl[9]' => { },
    'r2_a_tbl[10]' => { },
    'r2_a_tbl[11]' => { },
    'r2_a_tbl[12]' => { },
    'r2_a_tbl[13]' => { },
    'r2_a_tbl[14]' => { },
    'r2_a_tbl[15]' => { },
    'r2_a_tbl[16]' => { },
    'r2_a_tbl[17]' => { },
    'r2_a_tbl[18]' => { },
    'r2_a_tbl[19]' => { },
    'r2_a_tbl[20]' => { },
    'r2_a_tbl[21]' => { },
    'r2_a_tbl[22]' => { },
    'r2_a_tbl[23]' => { },
    'r2_a_tbl[24]' => { },
    'r2_a_tbl[25]' => { },
    'r2_a_tbl[26]' => { },
    'r2_a_tbl[27]' => { },
    'r2_a_tbl[28]' => { },
    'r2_a_tbl[29]' => { },
    'r2_a_tbl[30]' => { },
    'r2_a_tbl[31]' => { },
    'r2_a_red_ctbl[0]' => { },
    'r2_a_red_ctbl[1]' => { },
    'r2_a_red_ctbl[2]' => { },
    'r2_a_red_ctbl[3]' => { },
    'r2_a_red_ctbl[4]' => { },
    'r2_a_red_ctbl[5]' => { },
    'r2_a_red_ctbl[6]' => { },
    'r2_a_red_ctbl[7]' => { },
    'r2_a_red_ctbl[8]' => { },
    'r2_a_red_ctbl[9]' => { },
    'r2_a_red_ctbl[10]' => { },
    'r2_a_red_ctbl[11]' => { },
    'r2_a_red_ctbl[12]' => { },
    'r2_a_red_ctbl[13]' => { },
    'r2_a_red_ctbl[14]' => { },
    'r2_a_red_ctbl[15]' => { },
    'r2_a_red_ctbl[16]' => { },
    'r2_a_red_ctbl[17]' => { },
    'r2_a_red_ctbl[18]' => { },
    'r2_a_red_ctbl[19]' => { },
    'r2_a_red_ctbl[20]' => { },
    'r2_a_red_ctbl[21]' => { },
    'r2_a_red_ctbl[22]' => { },
    'r2_a_red_ctbl[23]' => { },
    'r2_a_red_ctbl[24]' => { },
    'r2_a_red_ctbl[25]' => { },
    'r2_a_red_ctbl[26]' => { },
    'r2_a_red_ctbl[27]' => { },
    'r2_a_red_ctbl[28]' => { },
    'r2_a_red_ctbl[29]' => { },
    'r2_a_red_ctbl[30]' => { },
    'r2_a_red_ctbl[31]' => { },
    'r2_a_green_ctbl[0]' => { },
    'r2_a_green_ctbl[1]' => { },
    'r2_a_green_ctbl[2]' => { },
    'r2_a_green_ctbl[3]' => { },
    'r2_a_green_ctbl[4]' => { },
    'r2_a_green_ctbl[5]' => { },
    'r2_a_green_ctbl[6]' => { },
    'r2_a_green_ctbl[7]' => { },
    'r2_a_green_ctbl[8]' => { },
    'r2_a_green_ctbl[9]' => { },
    'r2_a_green_ctbl[10]' => { },
    'r2_a_green_ctbl[11]' => { },
    'r2_a_green_ctbl[12]' => { },
    'r2_a_green_ctbl[13]' => { },
    'r2_a_green_ctbl[14]' => { },
    'r2_a_green_ctbl[15]' => { },
    'r2_a_green_ctbl[16]' => { },
    'r2_a_green_ctbl[17]' => { },
    'r2_a_green_ctbl[18]' => { },
    'r2_a_green_ctbl[19]' => { },
    'r2_a_green_ctbl[20]' => { },
    'r2_a_green_ctbl[21]' => { },
    'r2_a_green_ctbl[22]' => { },
    'r2_a_green_ctbl[23]' => { },
    'r2_a_green_ctbl[24]' => { },
    'r2_a_green_ctbl[25]' => { },
    'r2_a_green_ctbl[26]' => { },
    'r2_a_green_ctbl[27]' => { },
    'r2_a_green_ctbl[28]' => { },
    'r2_a_green_ctbl[29]' => { },
    'r2_a_green_ctbl[30]' => { },
    'r2_a_green_ctbl[31]' => { },
    'r2_a_blue_ctbl[0]' => { },
    'r2_a_blue_ctbl[1]' => { },
    'r2_a_blue_ctbl[2]' => { },
    'r2_a_blue_ctbl[3]' => { },
    'r2_a_blue_ctbl[4]' => { },
    'r2_a_blue_ctbl[5]' => { },
    'r2_a_blue_ctbl[6]' => { },
    'r2_a_blue_ctbl[7]' => { },
    'r2_a_blue_ctbl[8]' => { },
    'r2_a_blue_ctbl[9]' => { },
    'r2_a_blue_ctbl[10]' => { },
    'r2_a_blue_ctbl[11]' => { },
    'r2_a_blue_ctbl[12]' => { },
    'r2_a_blue_ctbl[13]' => { },
    'r2_a_blue_ctbl[14]' => { },
    'r2_a_blue_ctbl[15]' => { },
    'r2_a_blue_ctbl[16]' => { },
    'r2_a_blue_ctbl[17]' => { },
    'r2_a_blue_ctbl[18]' => { },
    'r2_a_blue_ctbl[19]' => { },
    'r2_a_blue_ctbl[20]' => { },
    'r2_a_blue_ctbl[21]' => { },
    'r2_a_blue_ctbl[22]' => { },
    'r2_a_blue_ctbl[23]' => { },
    'r2_a_blue_ctbl[24]' => { },
    'r2_a_blue_ctbl[25]' => { },
    'r2_a_blue_ctbl[26]' => { },
    'r2_a_blue_ctbl[27]' => { },
    'r2_a_blue_ctbl[28]' => { },
    'r2_a_blue_ctbl[29]' => { },
    'r2_a_blue_ctbl[30]' => { },
    'r2_a_blue_ctbl[31]' => { },
    'r2_a_red_stbl[0]' => { },
    'r2_a_red_stbl[1]' => { },
    'r2_a_red_stbl[2]' => { },
    'r2_a_red_stbl[3]' => { },
    'r2_a_red_stbl[4]' => { },
    'r2_a_red_stbl[5]' => { },
    'r2_a_red_stbl[6]' => { },
    'r2_a_red_stbl[7]' => { },
    'r2_a_red_stbl[8]' => { },
    'r2_a_red_stbl[9]' => { },
    'r2_a_red_stbl[10]' => { },
    'r2_a_red_stbl[11]' => { },
    'r2_a_red_stbl[12]' => { },
    'r2_a_red_stbl[13]' => { },
    'r2_a_red_stbl[14]' => { },
    'r2_a_red_stbl[15]' => { },
    'r2_a_red_stbl[16]' => { },
    'r2_a_red_stbl[17]' => { },
    'r2_a_red_stbl[18]' => { },
    'r2_a_red_stbl[19]' => { },
    'r2_a_red_stbl[20]' => { },
    'r2_a_red_stbl[21]' => { },
    'r2_a_red_stbl[22]' => { },
    'r2_a_red_stbl[23]' => { },
    'r2_a_red_stbl[24]' => { },
    'r2_a_red_stbl[25]' => { },
    'r2_a_red_stbl[26]' => { },
    'r2_a_red_stbl[27]' => { },
    'r2_a_red_stbl[28]' => { },
    'r2_a_red_stbl[29]' => { },
    'r2_a_red_stbl[30]' => { },
    'r2_a_red_stbl[31]' => { },
    'r2_a_blue_stbl[0]' => { },
    'r2_a_blue_stbl[1]' => { },
    'r2_a_blue_stbl[2]' => { },
    'r2_a_blue_stbl[3]' => { },
    'r2_a_blue_stbl[4]' => { },
    'r2_a_blue_stbl[5]' => { },
    'r2_a_blue_stbl[6]' => { },
    'r2_a_blue_stbl[7]' => { },
    'r2_a_blue_stbl[8]' => { },
    'r2_a_blue_stbl[9]' => { },
    'r2_a_blue_stbl[10]' => { },
    'r2_a_blue_stbl[11]' => { },
    'r2_a_blue_stbl[12]' => { },
    'r2_a_blue_stbl[13]' => { },
    'r2_a_blue_stbl[14]' => { },
    'r2_a_blue_stbl[15]' => { },
    'r2_a_blue_stbl[16]' => { },
    'r2_a_blue_stbl[17]' => { },
    'r2_a_blue_stbl[18]' => { },
    'r2_a_blue_stbl[19]' => { },
    'r2_a_blue_stbl[20]' => { },
    'r2_a_blue_stbl[21]' => { },
    'r2_a_blue_stbl[22]' => { },
    'r2_a_blue_stbl[23]' => { },
    'r2_a_blue_stbl[24]' => { },
    'r2_a_blue_stbl[25]' => { },
    'r2_a_blue_stbl[26]' => { },
    'r2_a_blue_stbl[27]' => { },
    'r2_a_blue_stbl[28]' => { },
    'r2_a_blue_stbl[29]' => { },
    'r2_a_blue_stbl[30]' => { },
    'r2_a_blue_stbl[31]' => { },
    'r2_a_green_stbl[0]' => { },
    'r2_a_green_stbl[1]' => { },
    'r2_a_green_stbl[2]' => { },
    'r2_a_green_stbl[3]' => { },
    'r2_a_green_stbl[4]' => { },
    'r2_a_green_stbl[5]' => { },
    'r2_a_green_stbl[6]' => { },
    'r2_a_green_stbl[7]' => { },
    'r2_a_green_stbl[8]' => { },
    'r2_a_green_stbl[9]' => { },
    'r2_a_green_stbl[10]' => { },
    'r2_a_green_stbl[11]' => { },
    'r2_a_green_stbl[12]' => { },
    'r2_a_green_stbl[13]' => { },
    'r2_a_green_stbl[14]' => { },
    'r2_a_green_stbl[15]' => { },
    'r2_a_green_stbl[16]' => { },
    'r2_a_green_stbl[17]' => { },
    'r2_a_green_stbl[18]' => { },
    'r2_a_green_stbl[19]' => { },
    'r2_a_green_stbl[20]' => { },
    'r2_a_green_stbl[21]' => { },
    'r2_a_green_stbl[22]' => { },
    'r2_a_green_stbl[23]' => { },
    'r2_a_green_stbl[24]' => { },
    'r2_a_green_stbl[25]' => { },
    'r2_a_green_stbl[26]' => { },
    'r2_a_green_stbl[27]' => { },
    'r2_a_green_stbl[28]' => { },
    'r2_a_green_stbl[29]' => { },
    'r2_a_green_stbl[30]' => { },
    'r2_a_green_stbl[31]' => { },
    'def_cor_c0' => { },
    'def_cor_c1' => { },
    'def_cor_c2' => { },
    'def_cor_c3' => { },
    'def_cor_c4' => { },
    'def_cor_c5' => { },
    'def_cor_c6' => { },
    'def_cor_c7' => { },
    'def_cor_c8' => { },
    'def_cor_k0' => { },
    'def_cor_k1' => { },
    'def_cor_k2' => { },
    'yhi_ylo_cor_c0' => { },
    'yhi_ylo_cor_c1' => { },
    'yhi_ylo_cor_c2' => { },
    'yhi_ylo_cor_c3' => { },
    'yhi_ylo_cor_c4' => { },
    'yhi_ylo_cor_c5' => { },
    'yhi_ylo_cor_c6' => { },
    'yhi_ylo_cor_c7' => { },
    'yhi_ylo_cor_c8' => { },
    'yhi_ylo_cor_k0' => { },
    'yhi_ylo_cor_k1' => { },
    'yhi_ylo_cor_k2' => { },
    'def_conv_chrm_a_m' => { },
    'def_conv_chrm_a_p' => { },
    'def_conv_chrm_b_m' => { },
    'def_conv_chrm_b_p' => { },
    'def_conv_chrm_c_m' => { },
    'def_conv_chrm_c_p' => { },
    'def_conv_chrm_d_m' => { },
    'def_conv_chrm_d_p' => { },
    'def_conv_chrm_k_cb' => { },
    'def_conv_chrm_k_cr' => { },
    'def_conv_luma_v0' => { },
    'def_conv_luma_v1' => { },
    'def_conv_luma_v2' => { },
    'def_conv_luma_k' => { },
    'tl84_conv_chrm_a_m' => { },
    'tl84_conv_chrm_a_p' => { },
    'tl84_conv_chrm_b_m' => { },
    'tl84_conv_chrm_b_p' => { },
    'tl84_conv_chrm_c_m' => { },
    'tl84_conv_chrm_c_p' => { },
    'tl84_conv_chrm_d_m' => { },
    'tl84_conv_chrm_d_p' => { },
    'tl84_conv_chrm_k_cb' => { },
    'tl84_conv_chrm_k_cr' => { },
    'tl84_conv_luma_v0' => { },
    'tl84_conv_luma_v1' => { },
    'tl84_conv_luma_v2' => { },
    'tl84_conv_luma_k' => { },
    'incand_conv_chrm_a_m' => { },
    'incand_conv_chrm_a_p' => { },
    'incand_conv_chrm_b_m' => { },
    'incand_conv_chrm_b_p' => { },
    'incand_conv_chrm_c_m' => { },
    'incand_conv_chrm_c_p' => { },
    'incand_conv_chrm_d_m' => { },
    'incand_conv_chrm_d_p' => { },
    'incand_conv_chrm_k_cb' => { },
    'incand_conv_chrm_k_cr' => { },
    'incand_conv_luma_v0' => { },
    'incand_conv_luma_v1' => { },
    'incand_conv_luma_v2' => { },
    'incand_conv_luma_k' => { },
    'daylt_conv_chrm_a_m' => { },
    'daylt_conv_chrm_a_p' => { },
    'daylt_conv_chrm_b_m' => { },
    'daylt_conv_chrm_b_p' => { },
    'daylt_conv_chrm_c_m' => { },
    'daylt_conv_chrm_c_p' => { },
    'daylt_conv_chrm_d_m' => { },
    'daylt_conv_chrm_d_p' => { },
    'daylt_conv_chrm_k_cb' => { },
    'daylt_conv_chrm_k_cr' => { },
    'daylt_conv_luma_v0' => { },
    'daylt_conv_luma_v1' => { },
    'daylt_conv_luma_v2' => { },
    'daylt_conv_luma_k' => { },
    'yhi_ylo_conv_chrm_a_m' => { },
    'yhi_ylo_conv_chrm_a_p' => { },
    'yhi_ylo_conv_chrm_b_m' => { },
    'yhi_ylo_conv_chrm_b_p' => { },
    'yhi_ylo_conv_chrm_c_m' => { },
    'yhi_ylo_conv_chrm_c_p' => { },
    'yhi_ylo_conv_chrm_d_m' => { },
    'yhi_ylo_conv_chrm_d_p' => { },
    'yhi_ylo_conv_chrm_k_cb' => { },
    'yhi_ylo_conv_chrm_k_cr' => { },
    'yhi_ylo_conv_luma_v0' => { },
    'yhi_ylo_conv_luma_v1' => { },
    'yhi_ylo_conv_luma_v2' => { },
    'yhi_ylo_conv_luma_k' => { },
    'gamma_enable' => { },
    'def_luma_gamma_mode' => { },
    'def_rgb_gamma_mode' => { },
    'blck_lvl_even_cols' => { },
    'blck_lvl_odd_cols' => { },
    'defect_pix_min_thresh' => { },
    'defect_pix_max_thresh' => { },
    'defect_pix_cor_enable' => { },
    'prview_resol' => { },
    'snapshot_resol' => { },
    'curr_resol' => { },
    'sensor_fmt' => { },
    'discard_frst_frm' => { },
    'frm_skip_pttrn' => { },
    'sensor_type' => { },
    'max_video_fps' => { },
    'video_fps' => { },
    'max_prview_fps' => { },
    'prview_fps' => { },
    'nghtsht_fps' => { },
    'sensr_ful_wdth' => { },
    'sensr_ful_hght' => { },
    'sensr_qtr_wdth' => { },
    'sensr_qtr_hght' => { },
    'nightshot_mode' => { },
    'pclk_invert' => { },
    'cam_mclk_hz' => { },
    'chrom_supress' => { },
    'chro_sup_luma_thres_1' => { },
    'chro_sup_luma_thres_2' => { },
    'chro_sup_luma_thres_3' => { },
    'chro_sup_luma_thres_4' => { },
    'chro_sup_chro_thres_1' => { },
    'chro_sup_chro_thres_2' => { },
    'la_detect' => { },
    'la_enable' => { },
    'HJR_enable' => { },
    'HJR_max_num_frames' => { },
    'HJR_one_to_two_offset' => { },
    'HJR_n_reduction_flat' => { },
    'HJR_n_reduction_texture' => { },
    'HJR_texture_threshold' => { },
);

%Image::ExifTool::Qualcomm::DualCamera = (
    PROCESS_PROC => \&ProcessDualCamera,
    GROUPS => { 0 => 'MakerNotes', 2 => 'Camera' },
    VARS => { NO_ID => 1, NO_LOOKUP => 1 }, # too long, too many, and too obscure
    NOTES => q{
        Found in JPEG APP4 "Qualcomm Dual Camera Attributes" written by some Nokia
        phones.
    },
    'Sensor Crop left'      => 'SensorCropLeft',
    'Sensor Crop top'       => 'SensorCropTop',
    'Sensor Crop width'     => 'SensorCropWidth',
    'Sensor Crop height'    => 'SensorCropHeight',
    'Sensor ROI Map left'   => 'SensorROIMapLeft',
    'Sensor ROI Map top'    => 'SensorROIMapTop',
    'Sensor ROI Map width'  => 'SensorROIMapWidth',
    'Sensor ROI Map height' => 'SensorROIMapHeight',
    'CAMIF Crop left'       => 'CAMIFCropLeft',
    'CAMIF Crop top'        => 'CAMIFCropTop',
    'CAMIF Crop width'      => 'CAMIFCropWidth',
    'CAMIF Crop height'     => 'CAMIFCropHeight',
    'CAMIF ROI Map left'    => 'CAMIF_ROIMapLeft',
    'CAMIF ROI Map top'     => 'CAMIF_ROIMapTop',
    'CAMIF ROI Map width'   => 'CAMIF_ROIMapWidth',
    'CAMIF ROI Map height'  => 'CAMIF_ROIMapHeight',
    'ISP Crop left'         => 'ISPCropLeft',
    'ISP Crop top'          => 'ISPCropTop',
    'ISP Crop width'        => 'ISPCropWidth',
    'ISP Crop height'       => 'ISPCropHeight',
    'ISP ROI Map left'      => 'ISP_ROIMapLeft',
    'ISP ROI Map top'       => 'ISP_ROIMapTop',
    'ISP ROI Map width'     => 'ISP_ROIMapWidth',
    'ISP ROI Map height'    => 'ISP_ROIMapHeight',
    'CPP Crop left'         => 'CPPCropLeft',
    'CPP Crop top'          => 'CPPCropTop',
    'CPP Crop width'        => 'CPPCropWidth',
    'CPP Crop height'       => 'CPPCropHeight',
    'CPP ROI Map left'      => 'CPP_ROIMapLeft',
    'CPP ROI Map top'       => 'CPP_ROIMapTop',
    'CPP ROI Map width'     => 'CPP_ROIMapWidth',
    'CPP ROI Map height'    => 'CPP_ROIMapHeight',
    'DDM Crop left'         => 'DDMCropLeft',
    'DDM Crop top'          => 'DDMCropTop',
    'DDM Crop width'        => 'DDMCropWidth',
    'DDM Crop height'       => 'DDMCropHeight',
    'Focal length Ratio'    => 'FocalLengthRatio',
    'Current pipeline mirror flip setting' => 'CurrentPipelineMirrorFlipSetting',
    'Current pipeline rotation setting' => 'CurrentPipelineRotationSetting',
    'AF ROI left'           => 'AF_ROILeft',
    'AF ROI top'            => 'AF_ROITop',
    'AF ROI width'          => 'AF_ROIWidth',
    'AF ROI height'         => 'AF_ROIHeight',
);

# generate tag names and descriptions
{
    local $_;
    my $table = \%Image::ExifTool::Qualcomm::Main;
    MakeNameAndDesc($_, $$table{$_}) foreach TagTableKeys($table);
}

#------------------------------------------------------------------------------
# Generate tag Name and Description from a Qualcomm tag ID
# Inputs: 0) tag ID, 1) tagInfo ref
# Returns: true on success
sub MakeNameAndDesc($$)
{
    local $_ = shift;
    my $tagInfo = shift;
    # capitalize various leading acronyms or just first letter...
    s/^(asf|awb|aec|afr|af_|la_|r2_tl|tl)/\U$1/ or $_ = ucfirst;
    s/_([a-z])/_\u$1/g;     # capitalize first letter of each word
    s/\[(\d+)\]$/sprintf("_%.2d",$1)/e; # use 2-digit subscripts (and remove brackets)
    tr/-_a-zA-Z0-9//dc;     # delete invalid characters
    my $desc = $_;
    # convert underlines to spaces in description
    if ($desc =~ tr/_/ /) {
        # remove unnecessary underlines from tag name...
        s/_([A-Z][a-z])/$1/g;
        s/([a-z0-9])_([A-Z])/$1$2/g;
        s/([A-Za-z])_(\d)/$1$2/g;
    }
    return 0 unless length;
    $$tagInfo{Name} = $_;
    $$tagInfo{Description} = $desc;
    return 1;
}

#------------------------------------------------------------------------------
# Process Qualcomm Dual Camera APP4 metadata (ref PH)
# Inputs: 0) ExifTool object ref, 1) dirInfo ref, 2) tag table ref
# Returns: 1 on success
sub ProcessDualCamera($$$)
{
    my ($et, $dirInfo, $tagTablePtr) = @_;
    my $dataPt = $$dirInfo{DataPt};
    my $dataPos = $$dirInfo{DataPos};
    my $pos = $$dirInfo{DirStart};

    $et->VerboseDir('Qualcomm Dual Camera', undef, $$dirInfo{DirLen});
    pos($$dataPt) = $pos;
    while ($$dataPt =~ /\x0a/g) {
        my $str = substr($$dataPt, $pos, pos($$dataPt) - $pos - 1);
        next unless $str =~ /^(.*?)\s*=\s*(.*)/;
        my ($tag, $val) = ($1, $2);
        $et->HandleTag($tagTablePtr, $tag, $val, MakeTagInfo => 1);
        $pos = pos($$dataPt);
    }
    return 1;
}

#------------------------------------------------------------------------------
# Process Qualcomm APP7 metadata (ref PH)
# Inputs: 0) ExifTool object ref, 1) dirInfo ref, 2) tag table ref
# Returns: 1 on success
sub ProcessQualcomm($$$)
{
    my ($et, $dirInfo, $tagTablePtr) = @_;
    my $dataPt = $$dirInfo{DataPt};
    my $dataPos = $$dirInfo{DataPos};
    my $pos = $$dirInfo{DirStart};
    my $dirEnd = $pos + $$dirInfo{DirLen};

    $et->VerboseDir('Qualcomm', undef, $$dirInfo{DirLen});
    SetByteOrder('II');

    while ($pos + 3 < $dirEnd) {
        my $valLen = Get16u($dataPt, $pos);
        my $tagLen = Get8u($dataPt, $pos + 2);
        last if $pos + 8 + $tagLen + $valLen > $dirEnd;
        my $tag = substr($$dataPt, $pos + 3, $tagLen);
        $pos += 3 + $tagLen;    # point to format byte
        my $fmt = Get8u($dataPt, $pos);
        # (not sure what these counts are for -- both are always 1 in my samples)
        #my $cnt1 = Get16u($dataPt, $pos + 1);
        #my $cnt2 = Get16u($dataPt, $pos + 3);
        $pos += 5;              # point to start of value data
        my ($val, $format);
        if ($fmt <= 7) {
            $format = $qualcommFormat[$fmt];
            $val = ReadValue($dataPt, $pos, $format, undef, $valLen);
        } else {
            $format = "format $fmt";
            my $value = substr($$dataPt, $pos, $valLen);
            $val = \$value;
        }
        unless (defined $$tagTablePtr{$tag} or $Image::ExifTool::specialTags{$tag}) {
            my %tagInfo;
            if (MakeNameAndDesc($tag, \%tagInfo)) {
                $et->VPrint(0, $$et{INDENT}, "[adding Qualcomm:$tagInfo{Name}]\n");
                AddTagToTable($tagTablePtr, $tag, \%tagInfo);
            }
        }
        $et->HandleTag($tagTablePtr, $tag, $val,
            DataPt  => $dataPt,
            DataPos => $dataPos,
            Start   => $pos,
            Size    => $valLen,
            Format  => $format,
        );
        $pos += $valLen;        # point to start of next entry
    }
    return 1;
}

1;  # end

__END__

=head1 NAME

Image::ExifTool::Qualcomm - Read Qualcomm APP7 meta information

=head1 SYNOPSIS

This module is loaded automatically by Image::ExifTool when required.

=head1 DESCRIPTION

This module contains definitions required by Image::ExifTool to read
information from the APP7 Qualcomm segment in JPEG images.

=head1 AUTHOR

Copyright 2003-2025, Phil Harvey (philharvey66 at gmail.com)

This library is free software; you can redistribute it and/or modify it
under the same terms as Perl itself.

=head1 SEE ALSO

L<Image::ExifTool::TagNames/Qualcomm Tags>,
L<Image::ExifTool(3pm)|Image::ExifTool>

=cut
