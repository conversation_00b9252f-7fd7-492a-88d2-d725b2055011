#------------------------------------------------------------------------------
# File:         tr.pm
#
# Description:  ExifTool Turkish language translations
#
# Notes:        This file generated automatically by Image::ExifTool::TagInfoXML
#------------------------------------------------------------------------------

package Image::ExifTool::Lang::tr;

use strict;
use vars qw($VERSION);

$VERSION = '1.05';

%Image::ExifTool::Lang::tr::Translate = (
   'Album' => 'Albüm',
   'Aperture' => 'Açıklık',
   'ApertureValue' => 'Açıklık',
   'Artist' => 'Sanatçı',
   'Author' => 'Yazar',
   'AuthorsPosition' => 'Ya<PERSON><PERSON>n <PERSON>',
   'BitsPerSample' => 'Komponent başına bit sayısı',
   'Brightness' => 'Parlaklık',
   'By-line' => 'Yazar',
   'CFAPattern' => 'CFA deseni',
   'CalibrationIlluminant1' => {
      PrintConv => {
        'Cloudy' => 'Bulutlu Hava',
        'Cool White Fluorescent' => 'Soğuk beyaz floresan (W 3800 - 4500K)',
        'Day White Fluorescent' => 'Gün beyaz floresan (N 4600 - 5500K)',
        'Daylight' => 'Günışığı',
        'Daylight Fluorescent' => 'Günışığı floresan (D 5700 - 7100K)',
        'Fine Weather' => 'İyi hava',
        'Flash' => 'Flaş',
        'Fluorescent' => 'Floresan',
        'ISO Studio Tungsten' => 'ISO stüdyo tungsten',
        'Other' => 'Diğer ışık kaynağı',
        'Shade' => 'Gölge',
        'Standard Light A' => 'Standard Işık A',
        'Standard Light B' => 'Standard Işık B',
        'Standard Light C' => 'Standard Işık C',
        'Tungsten (Incandescent)' => 'Tungsten',
        'Unknown' => 'Bilinmeyen',
        'White Fluorescent' => 'Beyaz floresan (WW 3250 - 3800K)',
      },
    },
   'CalibrationIlluminant2' => {
      PrintConv => {
        'Cloudy' => 'Bulutlu Hava',
        'Cool White Fluorescent' => 'Soğuk beyaz floresan (W 3800 - 4500K)',
        'Day White Fluorescent' => 'Gün beyaz floresan (N 4600 - 5500K)',
        'Daylight' => 'Günışığı',
        'Daylight Fluorescent' => 'Günışığı floresan (D 5700 - 7100K)',
        'Fine Weather' => 'İyi hava',
        'Flash' => 'Flaş',
        'Fluorescent' => 'Floresan',
        'ISO Studio Tungsten' => 'ISO stüdyo tungsten',
        'Other' => 'Diğer ışık kaynağı',
        'Shade' => 'Gölge',
        'Standard Light A' => 'Standard Işık A',
        'Standard Light B' => 'Standard Işık B',
        'Standard Light C' => 'Standard Işık C',
        'Tungsten (Incandescent)' => 'Tungsten',
        'Unknown' => 'Bilinmeyen',
        'White Fluorescent' => 'Beyaz floresan (WW 3250 - 3800K)',
      },
    },
   'Caption-Abstract' => 'Açıklama',
   'CaptionWriter' => 'Açıklama Yazarı',
   'Categories' => 'Kategoriler',
   'Category' => 'Kategoriler',
   'City' => 'Şehir',
   'ColorFilter' => 'Renk Filtresi',
   'ColorSpace' => {
      Description => 'Renk alanı bilgisi',
      PrintConv => {
        'Uncalibrated' => 'Kalibre edilmemiş',
      },
    },
   'ColorTemperature' => 'Renk Sıcaklığı',
   'Comment' => 'Yorum',
   'ComponentsConfiguration' => 'Her komponentin anlamı',
   'CompressedBitsPerPixel' => 'İmaj sıkıştıma modu',
   'Compression' => {
      Description => 'Sıkıştırma planı',
      PrintConv => {
        'JPEG' => 'JPEG Sıkıştırma',
        'Uncompressed' => 'Sıkıştırılmamış',
      },
    },
   'Contrast' => {
      Description => 'Kontrast',
      PrintConv => {
        'High' => 'Sert',
        'Low' => 'Yumuşak',
        'Normal' => 'Standard',
      },
    },
   'Copyright' => 'Telif hakkı sahibi',
   'CopyrightNotice' => 'Telif Hakkı Uyarısı',
   'Country' => 'Ülke',
   'Country-PrimaryLocationName' => 'Ülke',
   'CreateDate' => 'Oluşturma Tarihi',
   'CreationDate' => 'Yaratılış tarihi',
   'Credit' => 'Jenerik',
   'CustomRendered' => {
      Description => 'Özel imaj işleme',
      PrintConv => {
        'Custom' => 'Özel işlem',
        'Normal' => 'Normal işlem',
      },
    },
   'DateCreated' => 'Oluşturma Tarihi',
   'DateTimeOriginal' => 'Orjinal Tarih & Zaman',
   'DeviceSettingDescription' => 'Cihaz ayar tanımları',
   'DigitalZoom' => 'Dijital Zoom',
   'DigitalZoomRatio' => 'Dijital zoom oranı',
   'Directory' => 'Dosya Konumu',
   'DriveMode' => 'Sürüş Modu',
   'ExifImageHeight' => 'Geçerli imaj yüksekliği',
   'ExifImageWidth' => 'Geçerli imaj eni',
   'ExifOffset' => 'Exif IFD İmleci',
   'ExifVersion' => 'Exif sürüm',
   'ExposureCompensation' => 'Pozlama Sapması',
   'ExposureIndex' => 'Pozlama indeksi',
   'ExposureMode' => {
      Description => 'Pozlama modu',
      PrintConv => {
        'Auto' => 'Otomatik',
        'Auto bracket' => 'Otomatik çerçeve',
        'Manual' => 'Manuel pozlama',
      },
    },
   'ExposureProgram' => {
      Description => 'Pozlama program',
      PrintConv => {
        'Action (High speed)' => 'Hareket programı',
        'Aperture-priority AE' => 'Apertür önceliği',
        'Bulb' => 'Ampul',
        'Creative (Slow speed)' => 'Yaratıcı program',
        'Landscape' => 'Manzara',
        'Manual' => 'Manuel Pozlama',
        'Portrait' => 'Portre',
        'Program AE' => 'Normal program',
        'Shutter speed priority AE' => 'Deklanşör önceliği',
      },
    },
   'ExposureTime' => 'Poz süresi',
   'FNumber' => 'Açıklık',
   'FaceOrientation' => {
      PrintConv => {
        'Horizontal (normal)' => 'Pozitif yön',
        'Rotate 90 CW' => 'Saat yönünde 90° döndür',
      },
    },
   'FileFormat' => 'Format',
   'FileModifyDate' => 'Güncellenen Tarih',
   'FileName' => 'Dosya adı',
   'FileSize' => 'Dosya boyutu',
   'FileSource' => {
      Description => 'Dosya kaynağı',
      PrintConv => {
        'Digital Camera' => 'DSC',
        'Film Scanner' => 'Transparan tip tarayıcı',
        'Reflection Print Scanner' => 'Refleks tipi tarayıcı',
      },
    },
   'FileType' => 'Dosya türü',
   'Filename' => 'Dosya adı',
   'Flash' => {
      Description => 'Flaş',
      PrintConv => {
        'Auto, Fired' => 'AÇIK (Otom-flaş)',
        'Auto, Fired, Red-eye reduction' => '"AÇIK (Otom-flaş, Kırmızı-Göz Azaltma)"',
        'Auto, Fired, Red-eye reduction, Return detected' => '"AÇIK (Otom-flaş, Kırmızı-Göz Azaltma, Geri dönen ışık bulundu)"',
        'Auto, Fired, Return detected' => '"AÇIK (Otom-flaş, Geri dönen ışık bulundu)"',
        'Did not fire' => 'Flaş patlamadı',
        'Fired' => 'Flaş patladı',
        'Fired, Red-eye reduction' => 'AÇIK (Kırmızı-Göz Azaltma)',
        'Fired, Red-eye reduction, Return detected' => '"AÇIK (Kırmızı-Göz Azaltma, Geri dönen ışık bulundu)"',
        'Fired, Return detected' => 'AÇIK (Geri dönen ışık bulundu)',
        'No Flash' => 'Flaş fonksiyonu yok',
        'On, Fired' => 'AÇIK (Doldurma)',
        'On, Red-eye reduction' => '"AÇIK (Doldurma, Kırmızı-Göz Önleme)"',
        'On, Red-eye reduction, Return detected' => '"AÇIK (Doldurma, Kırmızı-Göz Önleme, Geri dönen ışık bulundu)"',
        'On, Return detected' => '"AÇIK (Doldurma, Geri dönen ışık bulundu)"',
      },
    },
   'FlashEnergy' => 'Flaş enerjisi',
   'FlashExposureComp' => 'Flaş Telafisi',
   'FlashpixVersion' => 'Desteklenen Flashpix sürümü',
   'FocalLength' => 'Odak uzunluğu',
   'FocalLength35efl' => 'Odak Uzaklığı (35 mm dengi)',
   'FocalLengthIn35mmFormat' => '35mm filmde odak uzaklığı',
   'FocalPlaneResolutionUnit' => {
      Description => 'Odak düzlemi çözünürlük birimi',
      PrintConv => {
        'inches' => 'inç',
      },
    },
   'FocalPlaneXResolution' => 'Odak düzlemi X çözünürlüğü',
   'FocalPlaneYResolution' => 'Odak düzlemi Y çözünürlüğü',
   'FocusMode' => 'Odak modu',
   'FrameRate' => 'Kare Hızı',
   'FrameSize' => 'Kare Boyutu',
   'GPSAltitude' => 'Yükseklik',
   'GPSAltitudeRef' => {
      Description => 'Yükselti referansı',
      PrintConv => {
        'Above Sea Level' => 'Deniz düzeyi',
        'Below Sea Level' => 'Deniz düzeyi referansı (negatif değer)',
      },
    },
   'GPSAreaInformation' => 'GPS Alanının ismi',
   'GPSDOP' => 'Ölçüm hassaslığı',
   'GPSDateStamp' => 'GPS alanı',
   'GPSDestBearing' => 'Varış yönü',
   'GPSDestBearingRef' => 'Varış yönü için referans',
   'GPSDestDistance' => 'Varışa uzaklık',
   'GPSDestDistanceRef' => 'Varışa uzaklık için referans',
   'GPSDestLatitude' => 'Varışın enlemi',
   'GPSDestLatitudeRef' => 'Varışın enlemi için referans',
   'GPSDestLongitude' => 'Varışın boylamı',
   'GPSDestLongitudeRef' => 'Varışın boylamı için referans',
   'GPSDifferential' => {
      Description => 'GPS diferansiyel düzeltme',
      PrintConv => {
        'Differential Corrected' => 'Difrensiyel düzeltme uygulandı',
        'No Correction' => 'Difrensiyel düzeltme olmadan ölçüm',
      },
    },
   'GPSImgDirection' => 'İmajın yönü',
   'GPSImgDirectionRef' => 'İmajın yönü için referans',
   'GPSInfo' => 'GPS Info IDF İmleci',
   'GPSLatitude' => 'Enlem',
   'GPSLatitudeRef' => {
      Description => 'Kuzey veya Güney Enlemi',
      PrintConv => {
        'North' => 'Kuzey enlemi',
        'South' => 'Güney enlemi',
      },
    },
   'GPSLongitude' => 'Boylam',
   'GPSLongitudeRef' => {
      Description => 'Doğu veya Batı Boylamı',
      PrintConv => {
        'East' => 'Doğu boylamı',
        'West' => 'Batı boylamı',
      },
    },
   'GPSMapDatum' => 'Geodetik veri kullanıldı',
   'GPSMeasureMode' => {
      Description => 'GPS ölçüm modu',
      PrintConv => {
        '3-Dimensional Measurement' => '3-boyutlu ölçüm',
      },
    },
   'GPSProcessingMethod' => 'GPS işlem metodunun ismi',
   'GPSSatellites' => 'Ölçüm için kullanılan GPS uyduları',
   'GPSSpeed' => 'GSP alıcının hızı',
   'GPSSpeedRef' => {
      Description => 'Hız Ünitesi',
      PrintConv => {
        'km/h' => 'km / saat',
        'knots' => 'Knot',
        'mph' => 'Mil / saat',
      },
    },
   'GPSStatus' => {
      Description => 'GPS alıcı durumu',
      PrintConv => {
        'Measurement Active' => 'İlerlemeli ölçüm',
        'Measurement Void' => 'Ölçü Birlikte İşlerliği',
      },
    },
   'GPSTimeStamp' => 'GPS saati (atomik saat)',
   'GPSTrack' => 'Hareket yönü',
   'GPSTrackRef' => {
      Description => 'Hareket yönü için referans',
      PrintConv => {
        'Magnetic North' => 'Manyetik yön',
        'True North' => 'Gerçek yön',
      },
    },
   'GPSVersionID' => 'GSP etiket sürümü',
   'GainControl' => {
      Description => 'Kontrol Kazan',
      PrintConv => {
        'High gain down' => 'Yüksek kazanç aşağı',
        'High gain up' => 'Yüksek kazanç yukarı',
        'Low gain down' => 'Düşek kazanç aşağı',
        'Low gain up' => 'Düşük kazanç yukarı',
        'None' => 'Hiçbiri',
      },
    },
   'Gradation' => 'Dereceleme',
   'Headline' => 'Başlık',
   'Hue' => 'Renk',
   'ICCProfile' => 'ICC Profili',
   'ISO' => 'ISO değeri',
   'ImageHeight' => 'İmaj yüksekliği',
   'ImageSize' => 'İmaj Boyutu',
   'ImageUniqueID' => 'Kendine has imaj ID',
   'ImageWidth' => 'İmaj genişliği',
   'Index' => 'İndeks',
   'Instructions' => 'Talimatlar',
   'InteropIndex' => 'Interoperabilite Tanımı',
   'InteropOffset' => 'Interoperabilite etiketi',
   'InteropVersion' => 'Interoperabilite Sürümü',
   'JPEGQuality' => {
      PrintConv => {
        'Standard' => 'Standart Kalite',
      },
    },
   'Keywords' => 'Anahtar sözcükler',
   'Lens' => 'Objektif',
   'LensInfo' => 'Lens Bilgisi',
   'LightSource' => {
      Description => 'Işık kaynağı',
      PrintConv => {
        'Cloudy' => 'Bulutlu Hava',
        'Cool White Fluorescent' => 'Soğuk beyaz floresan (W 3800 - 4500K)',
        'Day White Fluorescent' => 'Gün beyaz floresan (N 4600 - 5500K)',
        'Daylight' => 'Günışığı',
        'Daylight Fluorescent' => 'Günışığı floresan (D 5700 - 7100K)',
        'Fine Weather' => 'İyi hava',
        'Flash' => 'Flaş',
        'Fluorescent' => 'Floresan',
        'ISO Studio Tungsten' => 'ISO stüdyo tungsten',
        'Other' => 'Diğer ışık kaynağı',
        'Shade' => 'Gölge',
        'Standard Light A' => 'Standard Işık A',
        'Standard Light B' => 'Standard Işık B',
        'Standard Light C' => 'Standard Işık C',
        'Tungsten (Incandescent)' => 'Tungsten',
        'Unknown' => 'Bilinmeyen',
        'White Fluorescent' => 'Beyaz floresan (WW 3250 - 3800K)',
      },
    },
   'Lightness' => 'Işık',
   'Location' => 'Mevki',
   'Make' => 'Üretici',
   'MakerNotes' => 'Üretici notları',
   'MaxAperture' => 'Maksimum lens açıklığı',
   'MeteringMode' => {
      Description => 'Ölçü modu',
      PrintConv => {
        'Average' => 'Ortalama',
        'Center-weighted average' => 'CenterWeightedAverage',
        'Multi-segment' => 'Desen',
        'Multi-spot' => 'Multispot',
        'Other' => 'Diğer',
        'Partial' => 'Kısmen',
        'Unknown' => 'Bilinmeyen',
      },
    },
   'Model' => 'Kamera',
   'ModifyDate' => 'Dosya değişim tarih ve zamanı',
   'NoiseReduction' => 'Parazit azaltımı',
   'Opto-ElectricConvFactor' => 'Optoelektrik çevrim faktörü',
   'Orientation' => {
      Description => 'Yönelim',
      PrintConv => {
        'Horizontal (normal)' => 'Pozitif yön',
        'Rotate 90 CW' => 'Saat yönünde 90° döndür',
      },
    },
   'PhotometricInterpretation' => 'Piksel kompozisyonu',
   'PlanarConfiguration' => {
      Description => 'İmaj veri aranjmanı',
      PrintConv => {
        'Chunky' => 'Chunky format',
        'Planar' => 'Planar format',
      },
    },
   'PrimaryChromaticities' => 'İlklerin kromatikleri',
   'Province-State' => 'Eyalet/Bölge',
   'Quality' => {
      PrintConv => {
        'Low' => 'Düşük Kalite',
        'Normal' => 'Standart Kalite',
      },
    },
   'RecordMode' => 'Kayıt modu',
   'ReferenceBlackWhite' => 'Bir çift siyah ve beyaz referans değerleri',
   'RelatedImageFileFormat' => 'İlgili Görüntü Dosya Formatı',
   'RelatedImageHeight' => 'İlgili İmaj Yüksekliği',
   'RelatedImageWidth' => 'İlgili İmaj Genişliği',
   'RelatedSoundFile' => 'İlgili ses dosyası',
   'ResolutionUnit' => {
      Description => 'X ve Y birim çözünürlüğü',
      PrintConv => {
        'cm' => 'santimetre',
        'inches' => 'inç',
      },
    },
   'RowsPerStrip' => 'Şerit başına satır sayısı',
   'SamplesPerPixel' => 'Komponent sayısı',
   'Saturation' => {
      Description => 'Doyma',
      PrintConv => {
        'High' => 'Yüksek Doyma',
        'Low' => 'Düşük Doyma',
        'Normal' => 'Standard',
      },
    },
   'SceneCaptureType' => {
      Description => 'Senaryo yakalama tipi',
      PrintConv => {
        'Landscape' => 'Manzara',
        'Night' => 'Gece çekimi',
        'Portrait' => 'Portre',
      },
    },
   'SceneMode' => {
      PrintConv => {
        'Sunset' => 'Günbatımı',
      },
    },
   'SceneType' => {
      Description => 'Senaryo tipi',
      PrintConv => {
        'Directly photographed' => 'Doğrudan fotograflanmış imaj',
      },
    },
   'SensingMethod' => {
      Description => 'Alıcı metodu',
      PrintConv => {
        'Color sequential area' => 'Renk ardaşık alan alıcısı',
        'Color sequential linear' => 'Renk ardışık çizgisel alıcı',
        'One-chip color area' => 'Tek-çip renk alanı alıcı',
        'Three-chip color area' => 'Üç-çip renk alanı alıcı',
        'Trilinear' => 'Triple çizgisel alıcı',
        'Two-chip color area' => 'İki-çip renk alanı alıcı',
      },
    },
   'SerialNumber' => 'Kamera ID',
   'ShadingCompensation' => 'Gölgelendirme Telafisi',
   'Sharpness' => {
      Description => 'Keskinlik',
      PrintConv => {
        'Hard' => 'Sert',
        'Normal' => 'Standard',
        'Soft' => 'Yumuşak',
      },
    },
   'ShootingMode' => 'Çekim Modu',
   'ShutterSpeed' => 'Poz süresi',
   'ShutterSpeedValue' => 'Deklanşör hızı',
   'Software' => 'Kullanılan yazılım',
   'Source' => 'Kaynak',
   'SpatialFrequencyResponse' => 'Uzaysal frekans cevabı',
   'SpectralSensitivity' => 'Uzaysal hassaslık',
   'State' => 'Eyalet',
   'StripByteCounts' => 'Sıkıştırılmış şerit başına bayt',
   'StripOffsets' => 'İmaj veri konumu',
   'SubSecTime' => 'TarihZaman altsaniyeler',
   'SubSecTimeDigitized' => 'TarihZamanDijitize altsaniyeler',
   'SubSecTimeOriginal' => 'TarihZamanOrjinal altsaniyeler',
   'SubjectArea' => 'Obje alanı',
   'SubjectDistance' => 'Obje uzaklığı',
   'SubjectDistanceRange' => {
      Description => 'Obje uzaklık menzili',
      PrintConv => {
        'Close' => 'Yakın görüntü',
        'Distant' => 'Uzak görüntü',
        'Macro' => 'Makro',
      },
    },
   'SubjectLocation' => 'Obje konumu',
   'SupplementalCategories' => 'Tamamlayıcı Kategoriler',
   'ThumbnailImage' => 'Küçük Resim',
   'ThumbnailImageSize' => 'Küçük resim boyutu',
   'Title' => 'Başlık',
   'TransferFunction' => 'Transfer fonksiyonu',
   'TransmissionReference' => 'İletim Başvurusu',
   'Urgency' => 'Acil',
   'UserComment' => 'Kullanıcı yorumları',
   'WhiteBalance' => {
      Description => 'Beyaz ayarı',
      PrintConv => {
        'Auto' => 'Otomatik beyaz ayarı',
        'Black & White' => 'Monokrom',
        'Cloudy' => 'Bulutlu hava',
        'Cool White Fluorescent' => 'Soğuk beyaz floresan',
        'Custom 1' => 'ÖZEL1',
        'Custom 2' => 'ÖZEL2',
        'Custom 3' => 'ÖZEL3',
        'Custom 4' => 'ÖZEL4',
        'Day White Fluorescent' => 'Gün beyaz floresan',
        'Daylight' => 'Günışığı',
        'Daylight Fluorescent' => 'Günışığı floresan',
        'Fluorescent' => 'Floresan',
        'Manual' => 'Elle beyaz ayarı',
        'Shade' => 'Gölge',
        'Tungsten' => 'Tungsten ışık',
      },
    },
   'WhitePoint' => 'Beya nokta kromatik',
   'Writer-Editor' => 'Açıklama Yazarı',
   'XResolution' => 'X çözünürlüğü',
   'YCbCrCoefficients' => 'Renk alanı transformasyon matriks katsayısı',
   'YCbCrPositioning' => {
      Description => 'Y ve C konumlama',
      PrintConv => {
        'Centered' => 'Ortalanmış',
        'Co-sited' => 'Birlikte-konumlanmış',
      },
    },
   'YCbCrSubSampling' => 'Y den C\'ye alt örnekleme oranı',
   'YResolution' => 'Y çözünürlüğü',
);

1;  # end


__END__

=head1 NAME

Image::ExifTool::Lang::tr.pm - ExifTool Turkish language translations

=head1 DESCRIPTION

This file is used by Image::ExifTool to generate localized tag descriptions
and values.

=head1 AUTHOR

Copyright 2003-2025, Phil Harvey (philharvey66 at gmail.com)

This library is free software; you can redistribute it and/or modify it
under the same terms as Perl itself.

=head1 ACKNOWLEDGEMENTS

Thanks to Jens Duttke, Hasan Yildirim and Cihan Ulusoy for providing this
translation.

=head1 SEE ALSO

L<Image::ExifTool(3pm)|Image::ExifTool>,
L<Image::ExifTool::TagInfoXML(3pm)|Image::ExifTool::TagInfoXML>

=cut
