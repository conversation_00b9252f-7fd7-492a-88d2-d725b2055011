#!/usr/bin/env python3
"""
Gema - Metadata Editor Application
Main entry point for the application
"""

import tkinter as tk
import sys
import traceback

def main():
    try:
        # Import here to catch import errors
        from views.main_window import MainWindow

        root = tk.Tk()
        app = MainWindow(root)
        root.mainloop()

    except ImportError as e:
        print(f"Import Error: {e}")
        print("\nMissing dependencies. Please install required packages:")
        print("pip install Pillow mutagen")
        print("\nOptional packages for full functionality:")
        print("pip install piexif exifread")
        input("\nPress Enter to exit...")
        sys.exit(1)

    except Exception as e:
        print(f"Error starting application: {e}")
        print("\nFull error details:")
        traceback.print_exc()
        input("\nPress Enter to exit...")
        sys.exit(1)

if __name__ == "__main__":
    main()
