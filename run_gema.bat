@echo off
echo Starting Gema Metadata Editor...
echo.

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo Error: Python is not installed or not in PATH
    echo Please install Python 3.7+ from https://python.org
    pause
    exit /b 1
)

REM Install dependencies if needed
echo Checking dependencies...
python -c "import PIL, mutagen" >nul 2>&1
if errorlevel 1 (
    echo Installing core dependencies...
    python -m pip install Pillow mutagen
    if errorlevel 1 (
        echo Error: Failed to install core dependencies
        pause
        exit /b 1
    )
)

REM Check for ExifTool
echo Checking ExifTool...
if exist "exiftool-13.32_64\exiftool.exe" (
    echo ✓ ExifTool found locally
) else if exist "exiftool-13.32_64\exiftool(-k).exe" (
    echo ✓ ExifTool found locally
) else (
    exiftool -ver >nul 2>&1
    if errorlevel 1 (
        echo Warning: ExifTool not found
        echo For best metadata writing support:
        echo   1. Extract ExifTool to exiftool-13.32_64\ folder
        echo   2. Or install to PATH
        echo.
        echo The application will work with limited metadata writing capability.
        timeout /t 3 >nul
    ) else (
        echo ✓ ExifTool found in PATH
    )
)

REM Run the application
echo Starting Gema...
python main.py

if errorlevel 1 (
    echo.
    echo Application exited with error
    pause
)
