#------------------------------------------------------------------------------
# File:         sv.pm
#
# Description:  ExifTool Swedish language translations
#
# Notes:        This file generated automatically by Image::ExifTool::TagInfoXML
#------------------------------------------------------------------------------

package Image::ExifTool::Lang::sv;

use strict;
use vars qw($VERSION);

$VERSION = '1.05';

%Image::ExifTool::Lang::sv::Translate = (
   'Aperture' => 'Bländare',
   'ApertureValue' => 'Bländare',
   'Artist' => 'Upphovsman',
   'Author' => 'Upphovsman',
   'AuthorsPosition' => 'Författarens befattning',
   'BitsPerSample' => 'Antal bitar per komponent',
   'Brightness' => 'Ljusstyrka',
   'By-line' => 'Upphovsman',
   'CFAPattern' => 'CFA-mönster',
   'CalibrationIlluminant1' => {
      PrintConv => {
        'Cloudy' => 'Mulet',
        'Cool White Fluorescent' => 'Kalljusrör (W 3800 - 4500 k)',
        'Day White Fluorescent' => 'Dagljusrör, högdager (N 4600 - 5500k)',
        'Daylight' => 'Dagsljus',
        'Daylight Fluorescent' => 'Dagljusrör (D 5700 - 7100 k)',
        'Fine Weather' => 'Fint väder',
        'Flash' => 'Blixt',
        'Fluorescent' => 'Lysrörsbelysning',
        'ISO Studio Tungsten' => 'ISO tungsten studiobelysning',
        'Other' => 'Annan ljuskälla',
        'Shade' => 'Skugga',
        'Standard Light A' => 'Standardljus A',
        'Standard Light B' => 'Standardljus B',
        'Standard Light C' => 'Standardljus C',
        'Tungsten (Incandescent)' => 'Tungsten',
        'Unknown' => 'Okänt',
        'Warm White Fluorescent' => 'Varmt vitt fluorescerande (L 2600 - 3250k)',
        'White Fluorescent' => 'Vit lysrörsbelysning (WW 3250 - 3800 k)',
      },
    },
   'CalibrationIlluminant2' => {
      PrintConv => {
        'Cloudy' => 'Mulet',
        'Cool White Fluorescent' => 'Kalljusrör (W 3800 - 4500 k)',
        'Day White Fluorescent' => 'Dagljusrör, högdager (N 4600 - 5500k)',
        'Daylight' => 'Dagsljus',
        'Daylight Fluorescent' => 'Dagljusrör (D 5700 - 7100 k)',
        'Fine Weather' => 'Fint väder',
        'Flash' => 'Blixt',
        'Fluorescent' => 'Lysrörsbelysning',
        'ISO Studio Tungsten' => 'ISO tungsten studiobelysning',
        'Other' => 'Annan ljuskälla',
        'Shade' => 'Skugga',
        'Standard Light A' => 'Standardljus A',
        'Standard Light B' => 'Standardljus B',
        'Standard Light C' => 'Standardljus C',
        'Tungsten (Incandescent)' => 'Tungsten',
        'Unknown' => 'Okänt',
        'Warm White Fluorescent' => 'Varmt vitt fluorescerande (L 2600 - 3250k)',
        'White Fluorescent' => 'Vit lysrörsbelysning (WW 3250 - 3800 k)',
      },
    },
   'Caption-Abstract' => 'Bildtext',
   'CaptionWriter' => 'Bildtextredigerare',
   'Categories' => 'Kategorier',
   'Category' => 'Kategorier',
   'City' => 'Ort',
   'ColorFilter' => 'Färgfilter',
   'ColorMode' => {
      Description => 'Kreativa inst.',
      PrintConv => {
        'Autumn Leaves' => 'Höstlöv',
        'B&W' => 'Svartvitt',
        'Clear' => 'Klart',
        'Deep' => 'Djupt',
        'Landscape' => 'Landskap',
        'Light' => 'Ljust',
        'Neutral' => 'Neutralt',
        'Night View' => 'Nattvy',
        'Night View/Portrait' => 'Nattporträtt',
        'Portrait' => 'Porträtt',
        'Sunset' => 'Solnedgång',
        'Vivid' => 'Levande färg',
      },
    },
   'ColorSpace' => {
      Description => 'Färgområdesinformation',
      PrintConv => {
        'ICC Profile' => 'ICC-profil',
        'Uncalibrated' => 'Ej kalibrerat',
      },
    },
   'ColorTemperature' => 'Färgtemperatur',
   'Comment' => 'Kommentar',
   'ComponentsConfiguration' => 'Enskilda komponenters betydelse',
   'CompressedBitsPerPixel' => 'Bildkomprimeringsläge',
   'Compression' => {
      Description => 'Komprimeringsschema',
      PrintConv => {
        'JPEG' => 'JPEG-komprimering',
        'Uncompressed' => 'Ingen komprimering',
      },
    },
   'Contrast' => {
      Description => 'Kontrast',
      PrintConv => {
        'High' => 'Hård',
        'Low' => 'Mjuk',
        'Normal' => 'Standard',
      },
    },
   'Copyright' => 'Copyright-innehavare',
   'CopyrightNotice' => 'Copyrightmeddelande',
   'Country' => 'Land',
   'Country-PrimaryLocationName' => 'Land',
   'CreateDate' => 'Skapat datum',
   'CreationDate' => 'Skapad datum',
   'Credit' => 'Medverkande',
   'CustomRendered' => {
      Description => 'Anpassad bildbearbetning',
      PrintConv => {
        'Custom' => 'Anpassad process',
        'Normal' => 'Normal process',
      },
    },
   'DateCreated' => 'Skapat datum',
   'DateTimeOriginal' => 'Ursprungligt datum & tid',
   'DeviceSettingDescription' => 'Beskrivning av enhetsinställning',
   'DigitalZoomRatio' => 'Digitalt zoomomfång',
   'Directory' => 'Filplats',
   'DriveMode' => 'Enhetsläge',
   'DynamicRangeOptimizer' => {
      Description => 'Opt.av dyn.omr.',
      PrintConv => {
        'Advanced Auto' => 'Avancerad auto',
        'Advanced Lv1' => 'Avancerad niv1',
        'Advanced Lv2' => 'Avancerad niv2',
        'Advanced Lv3' => 'Avancerad niv3',
        'Advanced Lv4' => 'Avancerad niv4',
        'Advanced Lv5' => 'Avancerad niv5',
        'Auto' => 'Automatisk',
        'Off' => 'Av',
      },
    },
   'ExifImageHeight' => 'Giltig bildhöjd',
   'ExifImageWidth' => 'Giltig bildbredd',
   'ExifOffset' => 'IFD-pekare för Exif',
   'ExifVersion' => 'Exif-version',
   'ExposureCompensation' => 'Exponeringsförskjutning',
   'ExposureIndex' => 'Exponeringsindex',
   'ExposureMode' => {
      Description => 'Exponeringsläge',
      PrintConv => {
        'Auto' => 'Automatisk exponering',
        'Auto bracket' => 'Automatisk alternativexponering',
        'Manual' => 'Manuell exponering',
      },
    },
   'ExposureProgram' => {
      Description => 'Exponeringsprogram',
      PrintConv => {
        'Action (High speed)' => 'Sport',
        'Aperture-priority AE' => 'Bländarprioritet',
        'Bulb' => 'Glödlampa',
        'Creative (Slow speed)' => 'Kreativ',
        'Landscape' => 'Liggande',
        'Manual' => 'Manuell exponering',
        'Portrait' => 'Stående',
        'Program AE' => 'Normalt program',
        'Shutter speed priority AE' => 'Slutarprioritet',
      },
    },
   'ExposureTime' => 'Exponeringstid',
   'FNumber' => 'Bländare',
   'FaceOrientation' => {
      PrintConv => {
        'Horizontal (normal)' => 'Positiv riktning',
        'Rotate 90 CW' => 'Rotera 90° medurs',
      },
    },
   'FileFormat' => 'Format',
   'FileModifyDate' => 'Uppdateringsdatum',
   'FileName' => 'Filnamn',
   'FileSize' => 'Filstorlek',
   'FileSource' => {
      Description => 'Filkälla',
      PrintConv => {
        'Digital Camera' => 'DSC',
        'Film Scanner' => 'Skanner av transparent typ',
        'Reflection Print Scanner' => 'Skanner av reflextyp',
      },
    },
   'FileType' => 'Filtyp',
   'Filename' => 'Filnamn',
   'Flash' => {
      Description => 'Blixt',
      PrintConv => {
        'Auto, Fired' => 'PÅ (automatisk blixt)',
        'Auto, Fired, Red-eye reduction' => '"PÅ (automatisk blixt, reducering av röda ögon)"',
        'Auto, Fired, Red-eye reduction, Return detected' => '"PÅ (automatisk blixt, reducering av röda ögon, reflekterat ljus)"',
        'Auto, Fired, Return detected' => '"PÅ (automatisk blixt, reflekterat ljus)"',
        'Did not fire' => 'Blixten utlöstes inte',
        'Fired' => 'Blixten utlöstes',
        'Fired, Red-eye reduction' => 'PÅ (reducering av röda ögon)',
        'Fired, Red-eye reduction, Return detected' => '"PÅ (reducering av röda ögon, reflekterat ljus)"',
        'Fired, Return detected' => 'PÅ (reflekterat ljus)',
        'No Flash' => 'Ingen blixtfunktion',
        'On, Fired' => 'PÅ (upplättnad)',
        'On, Red-eye reduction' => '"PÅ (upplättnad, reducering av röda ögon)"',
        'On, Red-eye reduction, Return detected' => '"PÅ upplättnad reducering av röda ögon, reflekterat ljus)"',
        'On, Return detected' => '"PÅ (upplättnad, reflekterat ljus)"',
      },
    },
   'FlashEnergy' => 'Blixtenergi',
   'FlashExposureComp' => 'Blixtkompensering',
   'FlashpixVersion' => 'Flashpix-version som stöds',
   'FocalLength' => 'Brännvidd',
   'FocalLength35efl' => 'Brännvidd (i 35 mm format)',
   'FocalLengthIn35mmFormat' => 'Brännvidd för 35 mm-film',
   'FocalPlaneResolutionUnit' => {
      PrintConv => {
        'inches' => 'tum',
      },
    },
   'FocusMode' => 'Fokustyp',
   'FrameRate' => 'Bildfrekvens',
   'FrameSize' => 'Bildstorlek',
   'GPSAltitude' => 'Höjd',
   'GPSAltitudeRef' => {
      Description => 'Höjdreferens',
      PrintConv => {
        'Above Sea Level' => 'Havsnivå',
        'Below Sea Level' => 'Havsnivåref. (negativt värde)',
      },
    },
   'GPSAreaInformation' => 'Namn på GPS-område',
   'GPSDOP' => 'Måttprecision',
   'GPSDateStamp' => 'GPS-datum',
   'GPSDestBearing' => 'Destination',
   'GPSDestBearingRef' => 'Referens för destination',
   'GPSDestDistance' => 'Avstånd till destination',
   'GPSDestDistanceRef' => 'Referens för avstånd till destination',
   'GPSDestLatitude' => 'Destinationsbredd',
   'GPSDestLatitudeRef' => 'Referens för destionationsbredd',
   'GPSDestLongitude' => 'Destionationslängd',
   'GPSDestLongitudeRef' => 'Referens för destinationslängd',
   'GPSDifferential' => {
      Description => 'GPS-differentialkorrigering',
      PrintConv => {
        'Differential Corrected' => 'Differentialkorrigering använd',
        'No Correction' => 'Mätning utan differentialkorrigering',
      },
    },
   'GPSImgDirection' => 'Bildriktning',
   'GPSImgDirectionRef' => 'Referens för bildriktning',
   'GPSInfo' => 'IFD-pekare för GPS-information',
   'GPSLatitude' => 'Bredd',
   'GPSLatitudeRef' => {
      Description => 'Nordlig eller sydlig bredd',
      PrintConv => {
        'North' => 'Nordlig bredd',
        'South' => 'Sydlig bredd',
      },
    },
   'GPSLongitude' => 'Längd',
   'GPSLongitudeRef' => {
      Description => 'Östlig eller västlig längd',
      PrintConv => {
        'East' => 'Östlig längd',
        'West' => 'Västlig längd',
      },
    },
   'GPSMapDatum' => 'Geodetiska mätningsdata använda',
   'GPSMeasureMode' => {
      Description => 'GPS-mätningsläge',
      PrintConv => {
        '3-Dimensional Measurement' => '3D-mätning',
      },
    },
   'GPSProcessingMethod' => 'Namn på GPS-bearbetningsmetod',
   'GPSSatellites' => 'GPS-satelliter använda för mätning',
   'GPSSpeed' => 'Hastighet på GPS-mottagare',
   'GPSSpeedRef' => {
      Description => 'Hastighetsenhet',
      PrintConv => {
        'km/h' => 'Km/tim',
        'knots' => 'Knop',
        'mph' => 'Miles/tim',
      },
    },
   'GPSStatus' => {
      Description => 'GPS-mottagarstatus',
      PrintConv => {
        'Measurement Active' => 'Mätning pågår',
        'Measurement Void' => 'Mätningssamverkan',
      },
    },
   'GPSTimeStamp' => 'GPS-tid (atomur)',
   'GPSTrack' => 'Rörelseriktning',
   'GPSTrackRef' => {
      Description => 'Referens för rörelseriktning',
      PrintConv => {
        'Magnetic North' => 'Magnetisk riktning',
        'True North' => 'Verklig riktning',
      },
    },
   'GPSVersionID' => 'GPS tag version',
   'GainControl' => {
      Description => 'Förstärkningskontroll',
      PrintConv => {
        'High gain down' => 'Hög förstärkning ned',
        'High gain up' => 'Hög förstärkning upp',
        'Low gain down' => 'Låg förstärkning ned',
        'Low gain up' => 'Låg förstärkning upp',
        'None' => 'Ingen',
      },
    },
   'Gradation' => 'Effektfull',
   'HDR' => {
      Description => 'Auto HDR',
      PrintConv => {
        'Off' => 'Av',
      },
    },
   'Headline' => 'Rubrik',
   'HighISONoiseReduction' => {
      Description => 'Hög-ISO brusred',
      PrintConv => {
        'Auto' => 'Automatisk',
        'High' => 'Hög',
        'Low' => 'Låg',
        'Off' => 'Av',
      },
    },
   'Hue' => 'Nyans',
   'ICCProfile' => 'ICC profil',
   'ISO' => 'ISO värde',
   'ImageHeight' => 'Bildhöjd',
   'ImageSize' => 'Bildstorlek',
   'ImageUniqueID' => 'Unikt bild-ID',
   'ImageWidth' => 'Bildbredd',
   'Index' => 'Småbilder',
   'Instructions' => 'Instruktioner',
   'InteropIndex' => 'Interoperability Identification',
   'InteropOffset' => 'Interoperability tag',
   'InteropVersion' => 'Interoperability Version',
   'JPEGQuality' => {
      Description => 'Bildkvalitet',
      PrintConv => {
        'Extra Fine' => 'Extra fin',
        'Fine' => 'Fin',
        'Standard' => 'Standardkvalitet',
      },
    },
   'Keywords' => 'Nyckelord',
   'Lens' => 'Objektiv',
   'LensInfo' => 'Objektivinformation',
   'LightSource' => {
      Description => 'Ljuskälla',
      PrintConv => {
        'Cloudy' => 'Mulet',
        'Cool White Fluorescent' => 'Kalljusrör (W 3800 - 4500 k)',
        'Day White Fluorescent' => 'Dagljusrör, högdager (N 4600 - 5500k)',
        'Daylight' => 'Dagsljus',
        'Daylight Fluorescent' => 'Dagljusrör (D 5700 - 7100 k)',
        'Fine Weather' => 'Fint väder',
        'Flash' => 'Blixt',
        'Fluorescent' => 'Lysrörsbelysning',
        'ISO Studio Tungsten' => 'ISO tungsten studiobelysning',
        'Other' => 'Annan ljuskälla',
        'Shade' => 'Skugga',
        'Standard Light A' => 'Standardljus A',
        'Standard Light B' => 'Standardljus B',
        'Standard Light C' => 'Standardljus C',
        'Tungsten (Incandescent)' => 'Tungsten',
        'Unknown' => 'Okänt',
        'Warm White Fluorescent' => 'Varmt vitt fluorescerande (L 2600 - 3250k)',
        'White Fluorescent' => 'Vit lysrörsbelysning (WW 3250 - 3800 k)',
      },
    },
   'Lightness' => 'Ljushet',
   'Location' => 'Plats',
   'LongExposureNoiseReduction' => {
      Description => 'Långexp.brusred',
      PrintConv => {
        'Off' => 'Av',
        'On' => 'På',
      },
    },
   'Make' => 'Tillverkare',
   'MakerNotes' => 'Tillverkare',
   'MaxAperture' => 'Största bländare',
   'MeteringMode' => {
      Description => 'Mätningstyp',
      PrintConv => {
        'Average' => 'Genomsnitt',
        'Center-weighted average' => 'Centrumvägd genomsnittsmätning',
        'Multi-segment' => 'Mönster',
        'Multi-spot' => 'MultiSpot',
        'Other' => 'Annat',
        'Partial' => 'Delvis',
        'Unknown' => 'Okänt',
      },
    },
   'Model' => 'Kamera',
   'ModifyDate' => 'Filändringsdatum och -tid',
   'MultiFrameNoiseReduction' => {
      Description => 'Multi Frame brusred.',
      PrintConv => {
        'Off' => 'Av',
        'On' => 'På',
      },
    },
   'NoiseReduction' => 'Brusreducering',
   'Opto-ElectricConvFactor' => 'Optoelektrisk konverteringsfaktor',
   'Orientation' => {
      Description => 'Orientering',
      PrintConv => {
        'Horizontal (normal)' => 'Positiv riktning',
        'Rotate 90 CW' => 'Rotera 90° medurs',
      },
    },
   'PhotometricInterpretation' => 'Pixelsammansättning',
   'PlanarConfiguration' => {
      Description => 'Bilddataordning',
      PrintConv => {
        'Chunky' => 'Kompakt format',
        'Planar' => 'Planar format',
      },
    },
   'PrimaryChromaticities' => 'Kromaticitet för primärfärger',
   'Province-State' => 'Län/provins',
   'Quality' => {
      Description => 'Bildkvalitet',
      PrintConv => {
        'Compressed RAW' => 'cRAW',
        'Compressed RAW + JPEG' => 'cRAW+JPEG',
        'Extra Fine' => 'Extra fin',
        'Fine' => 'Fin',
        'Low' => 'Låg kvalitet',
        'Normal' => 'Standardkvalitet',
        'RAW + JPEG' => 'RAW+JPEG',
      },
    },
   'RecordMode' => 'Inspelningsläge',
   'ReferenceBlackWhite' => 'Par av svartvita referensvärden',
   'RelatedImageFileFormat' => 'Tillhörande bildfilsformat',
   'RelatedImageHeight' => 'Tillhörande bildlängd',
   'RelatedImageWidth' => 'Tillhörande bildbredd',
   'RelatedSoundFile' => 'Tillhörande ljudfil',
   'ResolutionUnit' => {
      Description => 'Enhet för X- och Y-upplösning',
      PrintConv => {
        'cm' => 'centimeter',
        'inches' => 'tum',
      },
    },
   'RowsPerStrip' => 'Antal rader',
   'SamplesPerPixel' => 'Antal komponenter',
   'Saturation' => {
      Description => 'Mättnad',
      PrintConv => {
        'High' => 'Hög mättnad',
        'Low' => 'Låg mättnad',
        'Normal' => 'Standard',
      },
    },
   'SceneCaptureType' => {
      Description => 'Motivtyp',
      PrintConv => {
        'Landscape' => 'Landskap',
        'Night' => 'Nattmotiv',
        'Portrait' => 'Porträtt',
      },
    },
   'SceneMode' => {
      Description => 'Scenval',
      PrintConv => {
        '3D Sweep Panorama' => '3D',
        'Anti Motion Blur' => 'Anti-rörelseoskärpa',
        'Auto' => 'Automatisk',
        'Cont. Priority AE' => 'Kont. prioritet AE',
        'Handheld Night Shot' => 'Manuell nattbild',
        'Landscape' => 'Landskap',
        'Macro' => 'Makro',
        'Night Portrait' => 'Nattporträtt',
        'Night Scene' => 'Nattvy',
        'Night View/Portrait' => 'Nattvy/porträtt',
        'Portrait' => 'Porträtt',
        'Sports' => 'Sporthändelse',
        'Sunset' => 'Solnedgång',
        'Sweep Panorama' => 'Panorering',
      },
    },
   'SceneType' => {
      Description => 'Motivtyp',
      PrintConv => {
        'Directly photographed' => 'En direktfotograferad bild',
      },
    },
   'SensingMethod' => {
      Description => 'Avkänningsmetod',
      PrintConv => {
        'Color sequential area' => 'Color sequential area sensor',
        'Color sequential linear' => 'Color sequential linear sensor',
        'One-chip color area' => 'One-chip color area sensor',
        'Three-chip color area' => 'Three-chip color area sensor',
        'Trilinear' => 'Trilinear sensor',
        'Two-chip color area' => 'Two-chip color area sensor',
      },
    },
   'SerialNumber' => 'Kamera-ID',
   'ShadingCompensation' => 'Skuggkompensering',
   'Sharpness' => {
      Description => 'Skärpa',
      PrintConv => {
        'Hard' => 'Hård',
        'Normal' => 'Standard',
        'Soft' => 'Mjuk',
      },
    },
   'ShootingMode' => 'Fotograferingsläge',
   'ShutterSpeed' => 'Exponeringstid',
   'ShutterSpeedValue' => 'Slutartid',
   'Software' => 'Programvara',
   'Source' => 'Källa',
   'SpatialFrequencyResponse' => 'Frekvensomfång',
   'SpectralSensitivity' => 'Spektral känslighet',
   'State' => 'Län',
   'StripByteCounts' => 'Byte komprimerade',
   'StripOffsets' => 'Plats för bilddata',
   'SubSecTime' => 'DateTime subseconds',
   'SubSecTimeDigitized' => 'DateTimeDigitized subseconds',
   'SubSecTimeOriginal' => 'DateTimeOriginal subseconds',
   'SubjectArea' => 'Motivyta',
   'SubjectDistance' => 'Motivavstånd',
   'SubjectDistanceRange' => {
      Description => 'Motivavstånd',
      PrintConv => {
        'Close' => 'Nära håll',
        'Distant' => 'Långt håll',
        'Macro' => 'Makro',
      },
    },
   'SubjectLocation' => 'Motivets placering',
   'SupplementalCategories' => 'Tilläggskategorier',
   'ThumbnailImage' => 'Miniatyr',
   'ThumbnailImageSize' => 'Miniatyrstorlek',
   'Title' => 'Titel',
   'TransferFunction' => 'Överföringsfunktion',
   'TransmissionReference' => 'Sändningsreferens',
   'Urgency' => 'Prioritet',
   'UserComment' => 'Användarkommentarer',
   'WhiteBalance' => {
      Description => 'Vitbalans',
      PrintConv => {
        'Auto' => 'Automatisk vitbalans',
        'Black & White' => 'Monokrom',
        'Cloudy' => 'Mulet',
        'Color Temperature/Color Filter' => 'Färgtemperatur / Färgfilter',
        'Cool White Fluorescent' => 'Kalljusrör',
        'Custom' => 'Special',
        'Custom 1' => 'ANPASSNING1',
        'Custom 2' => 'ANPASSNING2',
        'Custom 3' => 'ANPASSNING3',
        'Custom 4' => 'ANPASSNING4',
        'Day White Fluorescent' => '"Dagljusrör, högdager"',
        'Daylight' => 'Dagsljus',
        'Daylight Fluorescent' => 'Dagljusrör',
        'Flash' => 'Blixt',
        'Fluorescent' => 'Lysrörsbelysning',
        'Manual' => 'Manuell vitbalans',
        'Shade' => 'Skugga',
        'Tungsten' => 'Glödlampa',
        'Unknown' => 'Okänt',
        'Warm White Fluorescent' => 'Varmt vitt fluorescerande',
        'White Fluorescent' => 'Vit lysrörsbelysning',
      },
    },
   'WhitePoint' => 'Vitpunktskromaticitet',
   'Writer-Editor' => 'Bildtextredigerare',
   'YCbCrCoefficients' => 'Koefficienter för färgområdesomvandling',
   'YCbCrPositioning' => {
      Description => 'Y- och C-placering',
      PrintConv => {
        'Centered' => 'Centrerad',
      },
    },
   'YCbCrSubSampling' => 'Subsampling ratio of Y to C',
   'ZoneMatching' => {
      Description => 'Zonmatchning',
      PrintConv => {
        'High Key' => 'Hög',
        'ISO Setting Used' => 'Av',
        'Low Key' => 'Låg',
      },
    },
);

1;  # end


__END__

=head1 NAME

Image::ExifTool::Lang::sv.pm - ExifTool Swedish language translations

=head1 DESCRIPTION

This file is used by Image::ExifTool to generate localized tag descriptions
and values.

=head1 AUTHOR

Copyright 2003-2025, Phil Harvey (philharvey66 at gmail.com)

This library is free software; you can redistribute it and/or modify it
under the same terms as Perl itself.

=head1 ACKNOWLEDGEMENTS

Thanks to Jens Duttke and BjE<ouml>rn SE<ouml>derstrE<ouml>m for providing
this translation.

=head1 SEE ALSO

L<Image::ExifTool(3pm)|Image::ExifTool>,
L<Image::ExifTool::TagInfoXML(3pm)|Image::ExifTool::TagInfoXML>

=cut
