@echo off
echo Starting Gema Metadata Editor (Minimal Mode)...
echo.

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo Error: Python is not installed or not in PATH
    echo Please install Python 3.7+ from https://python.org
    pause
    exit /b 1
)

REM Install only core dependencies
echo Installing core dependencies...
python -m pip install Pillow mutagen >nul 2>&1

REM Run the application
echo Starting Gema...
python main.py

if errorlevel 1 (
    echo.
    echo Application exited with error
    pause
)
