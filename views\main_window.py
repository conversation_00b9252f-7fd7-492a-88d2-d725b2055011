"""
Main window for Gema metadata application
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import os
import sys

# Add current directory to path for imports
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from services.theme_service import ThemeService
from services.metadata_service import MetadataService
from services.settings_service import SettingsService
from models.metadata_model import MetadataModel
from views.metadata_editor import MetadataEditor
from views.file_browser import FileBrowser
from config.app_config import AppConfig

class MainWindow:
    def __init__(self, root):
        self.root = root
        self.root.title(f"{AppConfig.APP_NAME} - {AppConfig.APP_DESCRIPTION}")

        # Initialize services first
        self.settings_service = SettingsService()
        self.theme_service = ThemeService()
        self.metadata_service = MetadataService()
        self.metadata_model = MetadataModel()

        # Load saved window geometry or use default
        saved_geometry = self.settings_service.get_window_geometry()
        self.root.geometry(saved_geometry)
        self.root.minsize(AppConfig.WINDOW_MIN_WIDTH, AppConfig.WINDOW_MIN_HEIGHT)

        # Load saved theme
        saved_theme = self.settings_service.get_theme()
        self.theme_service.set_theme(saved_theme)

        # Bind window close event to save settings
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        
        # Setup theme observer
        self.theme_service.add_observer(self.on_theme_changed)
        
        # Create main interface
        self.create_widgets()
        self.apply_theme()

        # Set initial theme button icon
        current_theme = self.theme_service.get_current_theme()
        self.theme_button.configure(text="☀️" if current_theme == "dark" else "🌙")

        # Center window
        self.center_window()
    
    def create_widgets(self):
        # Main container
        self.main_frame = tk.Frame(self.root)
        self.main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Header frame
        self.header_frame = tk.Frame(self.main_frame)
        self.header_frame.pack(fill=tk.X, pady=(0, 10))
        
        # Title
        self.title_label = tk.Label(
            self.header_frame,
            text=AppConfig.APP_NAME,
            font=("Arial", 20, "bold")
        )
        self.title_label.pack(side=tk.LEFT)
        
        # Theme toggle button
        self.theme_button = tk.Button(
            self.header_frame,
            text="🌙",
            font=("Arial", 12),
            width=3,
            command=self.toggle_theme
        )
        self.theme_button.pack(side=tk.RIGHT)
        
        # Tab frame
        self.tab_frame = tk.Frame(self.main_frame)
        self.tab_frame.pack(fill=tk.X, pady=(0, 10))
        
        # Tab buttons
        self.tab_buttons = {}
        self.create_tab_buttons()
        
        # Content frame
        self.content_frame = tk.Frame(self.main_frame)
        self.content_frame.pack(fill=tk.BOTH, expand=True)
        
        # Initialize tabs
        self.current_tab = "browser"
        self.tabs = {}
        self.create_tabs()
        
        # Show initial tab
        self.show_tab("browser")
    
    def create_tab_buttons(self):
        tab_configs = [
            ("browser", "📁 File Browser"),
            ("editor", "✏️ Metadata Editor"),
            ("batch", "📦 Batch Editor")
        ]
        
        for tab_id, tab_text in tab_configs:
            btn = tk.Button(
                self.tab_frame,
                text=tab_text,
                font=("Arial", 10),
                relief='flat',
                borderwidth=0,
                padx=20,
                pady=8,
                command=lambda t=tab_id: self.show_tab(t)
            )
            btn.pack(side=tk.LEFT, padx=(0, 5))
            self.tab_buttons[tab_id] = btn
    
    def create_tabs(self):
        # File Browser Tab
        self.tabs["browser"] = FileBrowser(
            self.content_frame, 
            self.theme_service,
            self.on_file_selected
        )
        
        # Metadata Editor Tab
        self.tabs["editor"] = MetadataEditor(
            self.content_frame,
            self.theme_service,
            self.metadata_service,
            self.metadata_model
        )
        
        # Batch Editor Tab (placeholder)
        self.tabs["batch"] = tk.Frame(self.content_frame)
        batch_label = tk.Label(
            self.tabs["batch"],
            text="Batch Editor - Coming Soon",
            font=("Arial", 14)
        )
        batch_label.pack(expand=True)
    
    def show_tab(self, tab_id):
        # Hide all tabs
        for tab in self.tabs.values():
            tab.pack_forget()
        
        # Update button states
        for btn_id, btn in self.tab_buttons.items():
            if btn_id == tab_id:
                self.theme_service.apply_theme_to_widget(btn, 'button')
            else:
                self.theme_service.apply_theme_to_widget(btn, 'button_secondary')
        
        # Show selected tab
        if tab_id in self.tabs:
            self.tabs[tab_id].pack(fill=tk.BOTH, expand=True)
            self.current_tab = tab_id
    
    def on_file_selected(self, file_path):
        # Switch to editor tab and load file
        self.metadata_model.set_file_path(file_path)
        if hasattr(self.tabs["editor"], 'load_file'):
            self.tabs["editor"].load_file(file_path)
        self.show_tab("editor")
    
    def toggle_theme(self):
        self.theme_service.toggle_theme()
        # Save theme to settings
        current_theme = self.theme_service.get_current_theme()
        self.settings_service.set_theme(current_theme)
        # Update theme button icon
        self.theme_button.configure(text="☀️" if current_theme == "dark" else "🌙")
    
    def on_theme_changed(self, theme_data):
        self.apply_theme()
    
    def apply_theme(self):
        # Apply theme to main components
        self.theme_service.apply_theme_to_widget(self.root, 'frame')
        self.theme_service.apply_theme_to_widget(self.main_frame, 'frame')
        self.theme_service.apply_theme_to_widget(self.header_frame, 'frame')
        self.theme_service.apply_theme_to_widget(self.tab_frame, 'frame')
        self.theme_service.apply_theme_to_widget(self.content_frame, 'frame')
        
        # Apply theme to labels
        self.theme_service.apply_theme_to_widget(self.title_label, 'label')
        
        # Apply theme to theme button
        self.theme_service.apply_theme_to_widget(self.theme_button, 'button_secondary')
        
        # Apply theme to tab buttons
        for btn_id, btn in self.tab_buttons.items():
            if btn_id == self.current_tab:
                self.theme_service.apply_theme_to_widget(btn, 'button')
            else:
                self.theme_service.apply_theme_to_widget(btn, 'button_secondary')
        
        # Apply theme to tabs
        for tab in self.tabs.values():
            if hasattr(tab, 'apply_theme'):
                tab.apply_theme()
    
    def center_window(self):
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f"{width}x{height}+{x}+{y}")

    def on_closing(self):
        # Save window geometry before closing
        geometry = self.root.geometry()
        self.settings_service.set_window_geometry(geometry)

        # Close application
        self.root.destroy()
