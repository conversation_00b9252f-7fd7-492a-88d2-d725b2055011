#------------------------------------------------------------------------------
# File:         MacChineseTW.pm
#
# Description:  Mac Chinese Traditional to Unicode
#
# Revisions:    2010/01/20 - P<PERSON> created
#
# References:   1) http://unicode.org/Public/MAPPINGS/VENDORS/APPLE/CHINTRAD.TXT
#
# Notes:        The table omits 1-byte characters with the same values as Unicode
#------------------------------------------------------------------------------
use strict;

%Image::ExifTool::Charset::MacChineseTW = (
  0x80 => [0x5c,0xf87f], 0x81 => 0xf880, 0x82 => 0xf881, 0xfd => 0xa9,
  0xfe => 0x2122, 0xff => 0x2026,
  0xa1 => {
    0x40 => 0x3000, 0x41 => 0xff0c, 0x42 => 0x3001, 0x43 => 0x3002,
    0x44 => 0xff0e, 0x45 => 0xb7, 0x46 => 0xff1b, 0x47 => 0xff1a,
    0x48 => 0xff1f, 0x49 => 0xff01, 0x4a => 0xfe30, 0x4b => 0x22ef,
    0x4c => 0x2025, 0x4d => [0xff0c,0xf87d], 0x4e => [0x3001,0xf87d],
    0x4f => [0xff0e,0xf87d], 0x50 => [0xff0e,0xf87e], 0x51 => [0xff1b,0xf87d],
    0x52 => [0xff1a,0xf87d], 0x53 => [0xff1f,0xf87d], 0x54 => [0xff01,0xf87d],
    0x55 => 0xff5c, 0x56 => 0x2013, 0x57 => 0xfe31, 0x58 => 0x2014,
    0x59 => 0xfe33, 0x5a => [0xff3f,0xf87f], 0x5b => 0xfe34, 0x5c => 0xfe4f,
    0x5d => 0xff08, 0x5e => 0xff09, 0x5f => 0xfe35, 0x60 => 0xfe36,
    0x61 => 0xff5b, 0x62 => 0xff5d, 0x63 => 0xfe37, 0x64 => 0xfe38,
    0x65 => 0x3014, 0x66 => 0x3015, 0x67 => 0xfe39, 0x68 => 0xfe3a,
    0x69 => 0x3010, 0x6a => 0x3011, 0x6b => 0xfe3b, 0x6c => 0xfe3c,
    0x6d => 0x300a, 0x6e => 0x300b, 0x6f => 0xfe3d, 0x70 => 0xfe3e,
    0x71 => 0x3008, 0x72 => 0x3009, 0x73 => 0xfe3f, 0x74 => 0xfe40,
    0x75 => 0x300c, 0x76 => 0x300d, 0x77 => 0xfe41, 0x78 => 0xfe42,
    0x79 => 0x300e, 0x7a => 0x300f, 0x7b => 0xfe43, 0x7c => 0xfe44,
    0x7d => [0xff08,0xf87f], 0x7e => [0xff09,0xf87f], 0xa1 => [0xff5b,0xf87f],
    0xa2 => [0xff5d,0xf87f], 0xa3 => [0x3014,0xf87f], 0xa4 => [0x3015,0xf87f],
    0xa5 => 0x2018, 0xa6 => 0x2019, 0xa7 => 0x201c, 0xa8 => 0x201d,
    0xa9 => 0x301d, 0xaa => 0x301e, 0xab => 0x2035, 0xac => 0x2032,
    0xad => 0xff03, 0xae => 0xff06, 0xaf => 0xff0a, 0xb0 => 0x203b,
    0xb1 => 0xa7, 0xb2 => 0x3003, 0xb3 => 0x25cb, 0xb4 => 0x25cf,
    0xb5 => 0x25b3, 0xb6 => 0x25b2, 0xb7 => 0x25ce, 0xb8 => 0x2606,
    0xb9 => 0x2605, 0xba => 0x25c7, 0xbb => 0x25c6, 0xbc => 0x25a1,
    0xbd => 0x25a0, 0xbe => 0x25bd, 0xbf => 0x25bc, 0xc0 => 0x32a3,
    0xc1 => 0x2105, 0xc2 => 0x203e, 0xc3 => [0x203e,0xf87c], 0xc4 => 0xff3f,
    0xc5 => [0xff3f,0xf87c], 0xc6 => 0xfe49, 0xc7 => 0xfe4a, 0xc8 => 0xfe4d,
    0xc9 => 0xfe4e, 0xca => 0xfe4b, 0xcb => [0xfe4b,0xf87c], 0xcc => 0xfe5f,
    0xcd => 0xfe60, 0xce => 0xfe61, 0xcf => 0xff0b, 0xd0 => 0xff0d,
    0xd1 => 0xd7, 0xd2 => 0xf7, 0xd3 => 0xb1, 0xd4 => 0x221a, 0xd5 => 0xff1c,
    0xd6 => 0xff1e, 0xd7 => 0xff1d, 0xd8 => 0x2266, 0xd9 => 0x2267,
    0xda => 0x2260, 0xdb => 0x221e, 0xdc => 0x2252, 0xdd => 0x2261,
    0xde => 0xfe62, 0xdf => 0xfe63, 0xe0 => 0xfe64, 0xe1 => 0xfe65,
    0xe2 => 0xfe66, 0xe3 => 0x223c, 0xe4 => 0x2229, 0xe5 => 0x222a,
    0xe6 => 0x22a5, 0xe7 => 0x2220, 0xe8 => 0x221f, 0xe9 => 0x22bf,
    0xea => 0x33d2, 0xeb => 0x33d1, 0xec => 0x222b, 0xed => 0x222e,
    0xee => 0x2235, 0xef => 0x2234, 0xf0 => 0x2640, 0xf1 => 0x2642,
    0xf2 => 0x2295, 0xf3 => 0x2609, 0xf4 => 0x2191, 0xf5 => 0x2193,
    0xf6 => 0x2190, 0xf7 => 0x2192, 0xf8 => 0x2196, 0xf9 => 0x2197,
    0xfa => 0x2199, 0xfb => 0x2198, 0xfc => 0x2225, 0xfd => 0x2223,
    0xfe => [0xff0f,0xf87f],
  },
  0xa2 => {
    0x40 => [0xff3c,0xf87f], 0x41 => 0xff0f, 0x42 => 0xff3c, 0x43 => 0xff04,
    0x44 => 0xa5, 0x45 => 0x3012, 0x46 => 0xa2, 0x47 => 0xa3, 0x48 => 0xff05,
    0x49 => 0xff20, 0x4a => 0x2103, 0x4b => 0x2109, 0x4c => 0xfe69,
    0x4d => 0xfe6a, 0x4e => 0xfe6b, 0x4f => 0x33d5, 0x50 => 0x339c,
    0x51 => 0x339d, 0x52 => 0x339e, 0x53 => 0x33ce, 0x54 => 0x33a1,
    0x55 => 0x338e, 0x56 => 0x338f, 0x57 => 0x33c4, 0x58 => 0xb0,
    0x59 => 0x5159, 0x5a => 0x515b, 0x5b => 0x515e, 0x5c => 0x515d,
    0x5d => 0x5161, 0x5e => 0x5163, 0x5f => 0x55e7, 0x60 => 0x74e9,
    0x61 => 0x7cce, 0x62 => 0x2581, 0x63 => 0x2582, 0x64 => 0x2583,
    0x65 => 0x2584, 0x66 => 0x2585, 0x67 => 0x2586, 0x68 => 0x2587,
    0x69 => 0x2588, 0x6a => 0x258f, 0x6b => 0x258e, 0x6c => 0x258d,
    0x6d => 0x258c, 0x6e => 0x258b, 0x6f => 0x258a, 0x70 => 0x2589,
    0x71 => 0x253c, 0x72 => 0x2534, 0x73 => 0x252c, 0x74 => 0x2524,
    0x75 => 0x251c, 0x76 => 0x2594, 0x77 => 0x2500, 0x78 => 0x2502,
    0x79 => [0x2502,0xf87f], 0x7a => 0x250c, 0x7b => 0x2510, 0x7c => 0x2514,
    0x7d => 0x2518, 0x7e => 0x256d, 0xa1 => 0x256e, 0xa2 => 0x2570,
    0xa3 => 0x256f, 0xa4 => 0x2550, 0xa5 => 0x255e, 0xa6 => 0x256a,
    0xa7 => 0x2561, 0xa8 => 0x25e2, 0xa9 => 0x25e3, 0xaa => 0x25e5,
    0xab => 0x25e4, 0xac => 0x2571, 0xad => 0x2572, 0xae => 0x2573,
    0xaf => 0xff10, 0xb0 => 0xff11, 0xb1 => 0xff12, 0xb2 => 0xff13,
    0xb3 => 0xff14, 0xb4 => 0xff15, 0xb5 => 0xff16, 0xb6 => 0xff17,
    0xb7 => 0xff18, 0xb8 => 0xff19, 0xb9 => 0x2160, 0xba => 0x2161,
    0xbb => 0x2162, 0xbc => 0x2163, 0xbd => 0x2164, 0xbe => 0x2165,
    0xbf => 0x2166, 0xc0 => 0x2167, 0xc1 => 0x2168, 0xc2 => 0x2169,
    0xc3 => 0x3021, 0xc4 => 0x3022, 0xc5 => 0x3023, 0xc6 => 0x3024,
    0xc7 => 0x3025, 0xc8 => 0x3026, 0xc9 => 0x3027, 0xca => 0x3028,
    0xcb => 0x3029, 0xcc => [0x5341,0xf87f], 0xcd => 0x5344,
    0xce => [0x5345,0xf87f], 0xcf => 0xff21, 0xd0 => 0xff22, 0xd1 => 0xff23,
    0xd2 => 0xff24, 0xd3 => 0xff25, 0xd4 => 0xff26, 0xd5 => 0xff27,
    0xd6 => 0xff28, 0xd7 => 0xff29, 0xd8 => 0xff2a, 0xd9 => 0xff2b,
    0xda => 0xff2c, 0xdb => 0xff2d, 0xdc => 0xff2e, 0xdd => 0xff2f,
    0xde => 0xff30, 0xdf => 0xff31, 0xe0 => 0xff32, 0xe1 => 0xff33,
    0xe2 => 0xff34, 0xe3 => 0xff35, 0xe4 => 0xff36, 0xe5 => 0xff37,
    0xe6 => 0xff38, 0xe7 => 0xff39, 0xe8 => 0xff3a, 0xe9 => 0xff41,
    0xea => 0xff42, 0xeb => 0xff43, 0xec => 0xff44, 0xed => 0xff45,
    0xee => 0xff46, 0xef => 0xff47, 0xf0 => 0xff48, 0xf1 => 0xff49,
    0xf2 => 0xff4a, 0xf3 => 0xff4b, 0xf4 => 0xff4c, 0xf5 => 0xff4d,
    0xf6 => 0xff4e, 0xf7 => 0xff4f, 0xf8 => 0xff50, 0xf9 => 0xff51,
    0xfa => 0xff52, 0xfb => 0xff53, 0xfc => 0xff54, 0xfd => 0xff55,
    0xfe => 0xff56,
  },
  0xa3 => {
    0x40 => 0xff57, 0x41 => 0xff58, 0x42 => 0xff59, 0x43 => 0xff5a,
    0x44 => 0x0391, 0x45 => 0x0392, 0x46 => 0x0393, 0x47 => 0x0394,
    0x48 => 0x0395, 0x49 => 0x0396, 0x4a => 0x0397, 0x4b => 0x0398,
    0x4c => 0x0399, 0x4d => 0x039a, 0x4e => 0x039b, 0x4f => 0x039c,
    0x50 => 0x039d, 0x51 => 0x039e, 0x52 => 0x039f, 0x53 => 0x03a0,
    0x54 => 0x03a1, 0x55 => 0x03a3, 0x56 => 0x03a4, 0x57 => 0x03a5,
    0x58 => 0x03a6, 0x59 => 0x03a7, 0x5a => 0x03a8, 0x5b => 0x03a9,
    0x5c => 0x03b1, 0x5d => 0x03b2, 0x5e => 0x03b3, 0x5f => 0x03b4,
    0x60 => 0x03b5, 0x61 => 0x03b6, 0x62 => 0x03b7, 0x63 => 0x03b8,
    0x64 => 0x03b9, 0x65 => 0x03ba, 0x66 => 0x03bb, 0x67 => 0x03bc,
    0x68 => 0x03bd, 0x69 => 0x03be, 0x6a => 0x03bf, 0x6b => 0x03c0,
    0x6c => 0x03c1, 0x6d => 0x03c3, 0x6e => 0x03c4, 0x6f => 0x03c5,
    0x70 => 0x03c6, 0x71 => 0x03c7, 0x72 => 0x03c8, 0x73 => 0x03c9,
    0x74 => 0x3105, 0x75 => 0x3106, 0x76 => 0x3107, 0x77 => 0x3108,
    0x78 => 0x3109, 0x79 => 0x310a, 0x7a => 0x310b, 0x7b => 0x310c,
    0x7c => 0x310d, 0x7d => 0x310e, 0x7e => 0x310f, 0xa1 => 0x3110,
    0xa2 => 0x3111, 0xa3 => 0x3112, 0xa4 => 0x3113, 0xa5 => 0x3114,
    0xa6 => 0x3115, 0xa7 => 0x3116, 0xa8 => 0x3117, 0xa9 => 0x3118,
    0xaa => 0x3119, 0xab => 0x311a, 0xac => 0x311b, 0xad => 0x311c,
    0xae => 0x311d, 0xaf => 0x311e, 0xb0 => 0x311f, 0xb1 => 0x3120,
    0xb2 => 0x3121, 0xb3 => 0x3122, 0xb4 => 0x3123, 0xb5 => 0x3124,
    0xb6 => 0x3125, 0xb7 => 0x3126, 0xb8 => 0x3127, 0xb9 => 0x3128,
    0xba => 0x3129, 0xbb => 0x02d9, 0xbc => 0x02c9, 0xbd => 0x02ca,
    0xbe => 0x02c7, 0xbf => 0x02cb,
  },
  0xa4 => {
    0x40 => 0x4e00, 0x41 => 0x4e59, 0x42 => 0x4e01, 0x43 => 0x4e03,
    0x44 => 0x4e43, 0x45 => 0x4e5d, 0x46 => 0x4e86, 0x47 => 0x4e8c,
    0x48 => 0x4eba, 0x49 => 0x513f, 0x4a => 0x5165, 0x4b => 0x516b,
    0x4c => 0x51e0, 0x4d => 0x5200, 0x4e => 0x5201, 0x4f => 0x529b,
    0x50 => 0x5315, 0x51 => 0x5341, 0x52 => 0x535c, 0x53 => 0x53c8,
    0x54 => 0x4e09, 0x55 => 0x4e0b, 0x56 => 0x4e08, 0x57 => 0x4e0a,
    0x58 => 0x4e2b, 0x59 => 0x4e38, 0x5a => 0x51e1, 0x5b => 0x4e45,
    0x5c => 0x4e48, 0x5d => 0x4e5f, 0x5e => 0x4e5e, 0x5f => 0x4e8e,
    0x60 => 0x4ea1, 0x61 => 0x5140, 0x62 => 0x5203, 0x63 => 0x52fa,
    0x64 => 0x5343, 0x65 => 0x53c9, 0x66 => 0x53e3, 0x67 => 0x571f,
    0x68 => 0x58eb, 0x69 => 0x5915, 0x6a => 0x5927, 0x6b => 0x5973,
    0x6c => 0x5b50, 0x6d => 0x5b51, 0x6e => 0x5b53, 0x6f => 0x5bf8,
    0x70 => 0x5c0f, 0x71 => 0x5c22, 0x72 => 0x5c38, 0x73 => 0x5c71,
    0x74 => 0x5ddd, 0x75 => 0x5de5, 0x76 => 0x5df1, 0x77 => 0x5df2,
    0x78 => 0x5df3, 0x79 => 0x5dfe, 0x7a => 0x5e72, 0x7b => 0x5efe,
    0x7c => 0x5f0b, 0x7d => 0x5f13, 0x7e => 0x624d, 0xa1 => 0x4e11,
    0xa2 => 0x4e10, 0xa3 => 0x4e0d, 0xa4 => 0x4e2d, 0xa5 => 0x4e30,
    0xa6 => 0x4e39, 0xa7 => 0x4e4b, 0xa8 => 0x5c39, 0xa9 => 0x4e88,
    0xaa => 0x4e91, 0xab => 0x4e95, 0xac => 0x4e92, 0xad => 0x4e94,
    0xae => 0x4ea2, 0xaf => 0x4ec1, 0xb0 => 0x4ec0, 0xb1 => 0x4ec3,
    0xb2 => 0x4ec6, 0xb3 => 0x4ec7, 0xb4 => 0x4ecd, 0xb5 => 0x4eca,
    0xb6 => 0x4ecb, 0xb7 => 0x4ec4, 0xb8 => 0x5143, 0xb9 => 0x5141,
    0xba => 0x5167, 0xbb => 0x516d, 0xbc => 0x516e, 0xbd => 0x516c,
    0xbe => 0x5197, 0xbf => 0x51f6, 0xc0 => 0x5206, 0xc1 => 0x5207,
    0xc2 => 0x5208, 0xc3 => 0x52fb, 0xc4 => 0x52fe, 0xc5 => 0x52ff,
    0xc6 => 0x5316, 0xc7 => 0x5339, 0xc8 => 0x5348, 0xc9 => 0x5347,
    0xca => 0x5345, 0xcb => 0x535e, 0xcc => 0x5384, 0xcd => 0x53cb,
    0xce => 0x53ca, 0xcf => 0x53cd, 0xd0 => 0x58ec, 0xd1 => 0x5929,
    0xd2 => 0x592b, 0xd3 => 0x592a, 0xd4 => 0x592d, 0xd5 => 0x5b54,
    0xd6 => 0x5c11, 0xd7 => 0x5c24, 0xd8 => 0x5c3a, 0xd9 => 0x5c6f,
    0xda => 0x5df4, 0xdb => 0x5e7b, 0xdc => 0x5eff, 0xdd => 0x5f14,
    0xde => 0x5f15, 0xdf => 0x5fc3, 0xe0 => 0x6208, 0xe1 => 0x6236,
    0xe2 => 0x624b, 0xe3 => 0x624e, 0xe4 => 0x652f, 0xe5 => 0x6587,
    0xe6 => 0x6597, 0xe7 => 0x65a4, 0xe8 => 0x65b9, 0xe9 => 0x65e5,
    0xea => 0x66f0, 0xeb => 0x6708, 0xec => 0x6728, 0xed => 0x6b20,
    0xee => 0x6b62, 0xef => 0x6b79, 0xf0 => 0x6bcb, 0xf1 => 0x6bd4,
    0xf2 => 0x6bdb, 0xf3 => 0x6c0f, 0xf4 => 0x6c34, 0xf5 => 0x706b,
    0xf6 => 0x722a, 0xf7 => 0x7236, 0xf8 => 0x723b, 0xf9 => 0x7247,
    0xfa => 0x7259, 0xfb => 0x725b, 0xfc => 0x72ac, 0xfd => 0x738b,
    0xfe => 0x4e19,
  },
  0xa5 => {
    0x40 => 0x4e16, 0x41 => 0x4e15, 0x42 => 0x4e14, 0x43 => 0x4e18,
    0x44 => 0x4e3b, 0x45 => 0x4e4d, 0x46 => 0x4e4f, 0x47 => 0x4e4e,
    0x48 => 0x4ee5, 0x49 => 0x4ed8, 0x4a => 0x4ed4, 0x4b => 0x4ed5,
    0x4c => 0x4ed6, 0x4d => 0x4ed7, 0x4e => 0x4ee3, 0x4f => 0x4ee4,
    0x50 => 0x4ed9, 0x51 => 0x4ede, 0x52 => 0x5145, 0x53 => 0x5144,
    0x54 => 0x5189, 0x55 => 0x518a, 0x56 => 0x51ac, 0x57 => 0x51f9,
    0x58 => 0x51fa, 0x59 => 0x51f8, 0x5a => 0x520a, 0x5b => 0x52a0,
    0x5c => 0x529f, 0x5d => 0x5305, 0x5e => 0x5306, 0x5f => 0x5317,
    0x60 => 0x531d, 0x61 => 0x4edf, 0x62 => 0x534a, 0x63 => 0x5349,
    0x64 => 0x5361, 0x65 => 0x5360, 0x66 => 0x536f, 0x67 => 0x536e,
    0x68 => 0x53bb, 0x69 => 0x53ef, 0x6a => 0x53e4, 0x6b => 0x53f3,
    0x6c => 0x53ec, 0x6d => 0x53ee, 0x6e => 0x53e9, 0x6f => 0x53e8,
    0x70 => 0x53fc, 0x71 => 0x53f8, 0x72 => 0x53f5, 0x73 => 0x53eb,
    0x74 => 0x53e6, 0x75 => 0x53ea, 0x76 => 0x53f2, 0x77 => 0x53f1,
    0x78 => 0x53f0, 0x79 => 0x53e5, 0x7a => 0x53ed, 0x7b => 0x53fb,
    0x7c => 0x56db, 0x7d => 0x56da, 0x7e => 0x5916, 0xa1 => 0x592e,
    0xa2 => 0x5931, 0xa3 => 0x5974, 0xa4 => 0x5976, 0xa5 => 0x5b55,
    0xa6 => 0x5b83, 0xa7 => 0x5c3c, 0xa8 => 0x5de8, 0xa9 => 0x5de7,
    0xaa => 0x5de6, 0xab => 0x5e02, 0xac => 0x5e03, 0xad => 0x5e73,
    0xae => 0x5e7c, 0xaf => 0x5f01, 0xb0 => 0x5f18, 0xb1 => 0x5f17,
    0xb2 => 0x5fc5, 0xb3 => 0x620a, 0xb4 => 0x6253, 0xb5 => 0x6254,
    0xb6 => 0x6252, 0xb7 => 0x6251, 0xb8 => 0x65a5, 0xb9 => 0x65e6,
    0xba => 0x672e, 0xbb => 0x672c, 0xbc => 0x672a, 0xbd => 0x672b,
    0xbe => 0x672d, 0xbf => 0x6b63, 0xc0 => 0x6bcd, 0xc1 => 0x6c11,
    0xc2 => 0x6c10, 0xc3 => 0x6c38, 0xc4 => 0x6c41, 0xc5 => 0x6c40,
    0xc6 => 0x6c3e, 0xc7 => 0x72af, 0xc8 => 0x7384, 0xc9 => 0x7389,
    0xca => 0x74dc, 0xcb => 0x74e6, 0xcc => 0x7518, 0xcd => 0x751f,
    0xce => 0x7528, 0xcf => 0x7529, 0xd0 => 0x7530, 0xd1 => 0x7531,
    0xd2 => 0x7532, 0xd3 => 0x7533, 0xd4 => 0x758b, 0xd5 => 0x767d,
    0xd6 => 0x76ae, 0xd7 => 0x76bf, 0xd8 => 0x76ee, 0xd9 => 0x77db,
    0xda => 0x77e2, 0xdb => 0x77f3, 0xdc => 0x793a, 0xdd => 0x79be,
    0xde => 0x7a74, 0xdf => 0x7acb, 0xe0 => 0x4e1e, 0xe1 => 0x4e1f,
    0xe2 => 0x4e52, 0xe3 => 0x4e53, 0xe4 => 0x4e69, 0xe5 => 0x4e99,
    0xe6 => 0x4ea4, 0xe7 => 0x4ea6, 0xe8 => 0x4ea5, 0xe9 => 0x4eff,
    0xea => 0x4f09, 0xeb => 0x4f19, 0xec => 0x4f0a, 0xed => 0x4f15,
    0xee => 0x4f0d, 0xef => 0x4f10, 0xf0 => 0x4f11, 0xf1 => 0x4f0f,
    0xf2 => 0x4ef2, 0xf3 => 0x4ef6, 0xf4 => 0x4efb, 0xf5 => 0x4ef0,
    0xf6 => 0x4ef3, 0xf7 => 0x4efd, 0xf8 => 0x4f01, 0xf9 => 0x4f0b,
    0xfa => 0x5149, 0xfb => 0x5147, 0xfc => 0x5146, 0xfd => 0x5148,
    0xfe => 0x5168,
  },
  0xa6 => {
    0x40 => 0x5171, 0x41 => 0x518d, 0x42 => 0x51b0, 0x43 => 0x5217,
    0x44 => 0x5211, 0x45 => 0x5212, 0x46 => 0x520e, 0x47 => 0x5216,
    0x48 => 0x52a3, 0x49 => 0x5308, 0x4a => 0x5321, 0x4b => 0x5320,
    0x4c => 0x5370, 0x4d => 0x5371, 0x4e => 0x5409, 0x4f => 0x540f,
    0x50 => 0x540c, 0x51 => 0x540a, 0x52 => 0x5410, 0x53 => 0x5401,
    0x54 => 0x540b, 0x55 => 0x5404, 0x56 => 0x5411, 0x57 => 0x540d,
    0x58 => 0x5408, 0x59 => 0x5403, 0x5a => 0x540e, 0x5b => 0x5406,
    0x5c => 0x5412, 0x5d => 0x56e0, 0x5e => 0x56de, 0x5f => 0x56dd,
    0x60 => 0x5733, 0x61 => 0x5730, 0x62 => 0x5728, 0x63 => 0x572d,
    0x64 => 0x572c, 0x65 => 0x572f, 0x66 => 0x5729, 0x67 => 0x5919,
    0x68 => 0x591a, 0x69 => 0x5937, 0x6a => 0x5938, 0x6b => 0x5984,
    0x6c => 0x5978, 0x6d => 0x5983, 0x6e => 0x597d, 0x6f => 0x5979,
    0x70 => 0x5982, 0x71 => 0x5981, 0x72 => 0x5b57, 0x73 => 0x5b58,
    0x74 => 0x5b87, 0x75 => 0x5b88, 0x76 => 0x5b85, 0x77 => 0x5b89,
    0x78 => 0x5bfa, 0x79 => 0x5c16, 0x7a => 0x5c79, 0x7b => 0x5dde,
    0x7c => 0x5e06, 0x7d => 0x5e76, 0x7e => 0x5e74, 0xa1 => 0x5f0f,
    0xa2 => 0x5f1b, 0xa3 => 0x5fd9, 0xa4 => 0x5fd6, 0xa5 => 0x620e,
    0xa6 => 0x620c, 0xa7 => 0x620d, 0xa8 => 0x6210, 0xa9 => 0x6263,
    0xaa => 0x625b, 0xab => 0x6258, 0xac => 0x6536, 0xad => 0x65e9,
    0xae => 0x65e8, 0xaf => 0x65ec, 0xb0 => 0x65ed, 0xb1 => 0x66f2,
    0xb2 => 0x66f3, 0xb3 => 0x6709, 0xb4 => 0x673d, 0xb5 => 0x6734,
    0xb6 => 0x6731, 0xb7 => 0x6735, 0xb8 => 0x6b21, 0xb9 => 0x6b64,
    0xba => 0x6b7b, 0xbb => 0x6c16, 0xbc => 0x6c5d, 0xbd => 0x6c57,
    0xbe => 0x6c59, 0xbf => 0x6c5f, 0xc0 => 0x6c60, 0xc1 => 0x6c50,
    0xc2 => 0x6c55, 0xc3 => 0x6c61, 0xc4 => 0x6c5b, 0xc5 => 0x6c4d,
    0xc6 => 0x6c4e, 0xc7 => 0x7070, 0xc8 => 0x725f, 0xc9 => 0x725d,
    0xca => 0x767e, 0xcb => 0x7af9, 0xcc => 0x7c73, 0xcd => 0x7cf8,
    0xce => 0x7f36, 0xcf => 0x7f8a, 0xd0 => 0x7fbd, 0xd1 => 0x8001,
    0xd2 => 0x8003, 0xd3 => 0x800c, 0xd4 => 0x8012, 0xd5 => 0x8033,
    0xd6 => 0x807f, 0xd7 => 0x8089, 0xd8 => 0x808b, 0xd9 => 0x808c,
    0xda => 0x81e3, 0xdb => 0x81ea, 0xdc => 0x81f3, 0xdd => 0x81fc,
    0xde => 0x820c, 0xdf => 0x821b, 0xe0 => 0x821f, 0xe1 => 0x826e,
    0xe2 => 0x8272, 0xe3 => 0x827e, 0xe4 => 0x866b, 0xe5 => 0x8840,
    0xe6 => 0x884c, 0xe7 => 0x8863, 0xe8 => 0x897f, 0xe9 => 0x9621,
    0xea => 0x4e32, 0xeb => 0x4ea8, 0xec => 0x4f4d, 0xed => 0x4f4f,
    0xee => 0x4f47, 0xef => 0x4f57, 0xf0 => 0x4f5e, 0xf1 => 0x4f34,
    0xf2 => 0x4f5b, 0xf3 => 0x4f55, 0xf4 => 0x4f30, 0xf5 => 0x4f50,
    0xf6 => 0x4f51, 0xf7 => 0x4f3d, 0xf8 => 0x4f3a, 0xf9 => 0x4f38,
    0xfa => 0x4f43, 0xfb => 0x4f54, 0xfc => 0x4f3c, 0xfd => 0x4f46,
    0xfe => 0x4f63,
  },
  0xa7 => {
    0x40 => 0x4f5c, 0x41 => 0x4f60, 0x42 => 0x4f2f, 0x43 => 0x4f4e,
    0x44 => 0x4f36, 0x45 => 0x4f59, 0x46 => 0x4f5d, 0x47 => 0x4f48,
    0x48 => 0x4f5a, 0x49 => 0x514c, 0x4a => 0x514b, 0x4b => 0x514d,
    0x4c => 0x5175, 0x4d => 0x51b6, 0x4e => 0x51b7, 0x4f => 0x5225,
    0x50 => 0x5224, 0x51 => 0x5229, 0x52 => 0x522a, 0x53 => 0x5228,
    0x54 => 0x52ab, 0x55 => 0x52a9, 0x56 => 0x52aa, 0x57 => 0x52ac,
    0x58 => 0x5323, 0x59 => 0x5373, 0x5a => 0x5375, 0x5b => 0x541d,
    0x5c => 0x542d, 0x5d => 0x541e, 0x5e => 0x543e, 0x5f => 0x5426,
    0x60 => 0x544e, 0x61 => 0x5427, 0x62 => 0x5446, 0x63 => 0x5443,
    0x64 => 0x5433, 0x65 => 0x5448, 0x66 => 0x5442, 0x67 => 0x541b,
    0x68 => 0x5429, 0x69 => 0x544a, 0x6a => 0x5439, 0x6b => 0x543b,
    0x6c => 0x5438, 0x6d => 0x542e, 0x6e => 0x5435, 0x6f => 0x5436,
    0x70 => 0x5420, 0x71 => 0x543c, 0x72 => 0x5440, 0x73 => 0x5431,
    0x74 => 0x542b, 0x75 => 0x541f, 0x76 => 0x542c, 0x77 => 0x56ea,
    0x78 => 0x56f0, 0x79 => 0x56e4, 0x7a => 0x56eb, 0x7b => 0x574a,
    0x7c => 0x5751, 0x7d => 0x5740, 0x7e => 0x574d, 0xa1 => 0x5747,
    0xa2 => 0x574e, 0xa3 => 0x573e, 0xa4 => 0x5750, 0xa5 => 0x574f,
    0xa6 => 0x573b, 0xa7 => 0x58ef, 0xa8 => 0x593e, 0xa9 => 0x599d,
    0xaa => 0x5992, 0xab => 0x59a8, 0xac => 0x599e, 0xad => 0x59a3,
    0xae => 0x5999, 0xaf => 0x5996, 0xb0 => 0x598d, 0xb1 => 0x59a4,
    0xb2 => 0x5993, 0xb3 => 0x598a, 0xb4 => 0x59a5, 0xb5 => 0x5b5d,
    0xb6 => 0x5b5c, 0xb7 => 0x5b5a, 0xb8 => 0x5b5b, 0xb9 => 0x5b8c,
    0xba => 0x5b8b, 0xbb => 0x5b8f, 0xbc => 0x5c2c, 0xbd => 0x5c40,
    0xbe => 0x5c41, 0xbf => 0x5c3f, 0xc0 => 0x5c3e, 0xc1 => 0x5c90,
    0xc2 => 0x5c91, 0xc3 => 0x5c94, 0xc4 => 0x5c8c, 0xc5 => 0x5deb,
    0xc6 => 0x5e0c, 0xc7 => 0x5e8f, 0xc8 => 0x5e87, 0xc9 => 0x5e8a,
    0xca => 0x5ef7, 0xcb => 0x5f04, 0xcc => 0x5f1f, 0xcd => 0x5f64,
    0xce => 0x5f62, 0xcf => 0x5f77, 0xd0 => 0x5f79, 0xd1 => 0x5fd8,
    0xd2 => 0x5fcc, 0xd3 => 0x5fd7, 0xd4 => 0x5fcd, 0xd5 => 0x5ff1,
    0xd6 => 0x5feb, 0xd7 => 0x5ff8, 0xd8 => 0x5fea, 0xd9 => 0x6212,
    0xda => 0x6211, 0xdb => 0x6284, 0xdc => 0x6297, 0xdd => 0x6296,
    0xde => 0x6280, 0xdf => 0x6276, 0xe0 => 0x6289, 0xe1 => 0x626d,
    0xe2 => 0x628a, 0xe3 => 0x627c, 0xe4 => 0x627e, 0xe5 => 0x6279,
    0xe6 => 0x6273, 0xe7 => 0x6292, 0xe8 => 0x626f, 0xe9 => 0x6298,
    0xea => 0x626e, 0xeb => 0x6295, 0xec => 0x6293, 0xed => 0x6291,
    0xee => 0x6286, 0xef => 0x6539, 0xf0 => 0x653b, 0xf1 => 0x6538,
    0xf2 => 0x65f1, 0xf3 => 0x66f4, 0xf4 => 0x675f, 0xf5 => 0x674e,
    0xf6 => 0x674f, 0xf7 => 0x6750, 0xf8 => 0x6751, 0xf9 => 0x675c,
    0xfa => 0x6756, 0xfb => 0x675e, 0xfc => 0x6749, 0xfd => 0x6746,
    0xfe => 0x6760,
  },
  0xa8 => {
    0x40 => 0x6753, 0x41 => 0x6757, 0x42 => 0x6b65, 0x43 => 0x6bcf,
    0x44 => 0x6c42, 0x45 => 0x6c5e, 0x46 => 0x6c99, 0x47 => 0x6c81,
    0x48 => 0x6c88, 0x49 => 0x6c89, 0x4a => 0x6c85, 0x4b => 0x6c9b,
    0x4c => 0x6c6a, 0x4d => 0x6c7a, 0x4e => 0x6c90, 0x4f => 0x6c70,
    0x50 => 0x6c8c, 0x51 => 0x6c68, 0x52 => 0x6c96, 0x53 => 0x6c92,
    0x54 => 0x6c7d, 0x55 => 0x6c83, 0x56 => 0x6c72, 0x57 => 0x6c7e,
    0x58 => 0x6c74, 0x59 => 0x6c86, 0x5a => 0x6c76, 0x5b => 0x6c8d,
    0x5c => 0x6c94, 0x5d => 0x6c98, 0x5e => 0x6c82, 0x5f => 0x7076,
    0x60 => 0x707c, 0x61 => 0x707d, 0x62 => 0x7078, 0x63 => 0x7262,
    0x64 => 0x7261, 0x65 => 0x7260, 0x66 => 0x72c4, 0x67 => 0x72c2,
    0x68 => 0x7396, 0x69 => 0x752c, 0x6a => 0x752b, 0x6b => 0x7537,
    0x6c => 0x7538, 0x6d => 0x7682, 0x6e => 0x76ef, 0x6f => 0x77e3,
    0x70 => 0x79c1, 0x71 => 0x79c0, 0x72 => 0x79bf, 0x73 => 0x7a76,
    0x74 => 0x7cfb, 0x75 => 0x7f55, 0x76 => 0x8096, 0x77 => 0x8093,
    0x78 => 0x809d, 0x79 => 0x8098, 0x7a => 0x809b, 0x7b => 0x809a,
    0x7c => 0x80b2, 0x7d => 0x826f, 0x7e => 0x8292, 0xa1 => 0x828b,
    0xa2 => 0x828d, 0xa3 => 0x898b, 0xa4 => 0x89d2, 0xa5 => 0x8a00,
    0xa6 => 0x8c37, 0xa7 => 0x8c46, 0xa8 => 0x8c55, 0xa9 => 0x8c9d,
    0xaa => 0x8d64, 0xab => 0x8d70, 0xac => 0x8db3, 0xad => 0x8eab,
    0xae => 0x8eca, 0xaf => 0x8f9b, 0xb0 => 0x8fb0, 0xb1 => 0x8fc2,
    0xb2 => 0x8fc6, 0xb3 => 0x8fc5, 0xb4 => 0x8fc4, 0xb5 => 0x5de1,
    0xb6 => 0x9091, 0xb7 => 0x90a2, 0xb8 => 0x90aa, 0xb9 => 0x90a6,
    0xba => 0x90a3, 0xbb => 0x9149, 0xbc => 0x91c6, 0xbd => 0x91cc,
    0xbe => 0x9632, 0xbf => 0x962e, 0xc0 => 0x9631, 0xc1 => 0x962a,
    0xc2 => 0x962c, 0xc3 => 0x4e26, 0xc4 => 0x4e56, 0xc5 => 0x4e73,
    0xc6 => 0x4e8b, 0xc7 => 0x4e9b, 0xc8 => 0x4e9e, 0xc9 => 0x4eab,
    0xca => 0x4eac, 0xcb => 0x4f6f, 0xcc => 0x4f9d, 0xcd => 0x4f8d,
    0xce => 0x4f73, 0xcf => 0x4f7f, 0xd0 => 0x4f6c, 0xd1 => 0x4f9b,
    0xd2 => 0x4f8b, 0xd3 => 0x4f86, 0xd4 => 0x4f83, 0xd5 => 0x4f70,
    0xd6 => 0x4f75, 0xd7 => 0x4f88, 0xd8 => 0x4f69, 0xd9 => 0x4f7b,
    0xda => 0x4f96, 0xdb => 0x4f7e, 0xdc => 0x4f8f, 0xdd => 0x4f91,
    0xde => 0x4f7a, 0xdf => 0x5154, 0xe0 => 0x5152, 0xe1 => 0x5155,
    0xe2 => 0x5169, 0xe3 => 0x5177, 0xe4 => 0x5176, 0xe5 => 0x5178,
    0xe6 => 0x51bd, 0xe7 => 0x51fd, 0xe8 => 0x523b, 0xe9 => 0x5238,
    0xea => 0x5237, 0xeb => 0x523a, 0xec => 0x5230, 0xed => 0x522e,
    0xee => 0x5236, 0xef => 0x5241, 0xf0 => 0x52be, 0xf1 => 0x52bb,
    0xf2 => 0x5352, 0xf3 => 0x5354, 0xf4 => 0x5353, 0xf5 => 0x5351,
    0xf6 => 0x5366, 0xf7 => 0x5377, 0xf8 => 0x5378, 0xf9 => 0x5379,
    0xfa => 0x53d6, 0xfb => 0x53d4, 0xfc => 0x53d7, 0xfd => 0x5473,
    0xfe => 0x5475,
  },
  0xa9 => {
    0x40 => 0x5496, 0x41 => 0x5478, 0x42 => 0x5495, 0x43 => 0x5480,
    0x44 => 0x547b, 0x45 => 0x5477, 0x46 => 0x5484, 0x47 => 0x5492,
    0x48 => 0x5486, 0x49 => 0x547c, 0x4a => 0x5490, 0x4b => 0x5471,
    0x4c => 0x5476, 0x4d => 0x548c, 0x4e => 0x549a, 0x4f => 0x5462,
    0x50 => 0x5468, 0x51 => 0x548b, 0x52 => 0x547d, 0x53 => 0x548e,
    0x54 => 0x56fa, 0x55 => 0x5783, 0x56 => 0x5777, 0x57 => 0x576a,
    0x58 => 0x5769, 0x59 => 0x5761, 0x5a => 0x5766, 0x5b => 0x5764,
    0x5c => 0x577c, 0x5d => 0x591c, 0x5e => 0x5949, 0x5f => 0x5947,
    0x60 => 0x5948, 0x61 => 0x5944, 0x62 => 0x5954, 0x63 => 0x59be,
    0x64 => 0x59bb, 0x65 => 0x59d4, 0x66 => 0x59b9, 0x67 => 0x59ae,
    0x68 => 0x59d1, 0x69 => 0x59c6, 0x6a => 0x59d0, 0x6b => 0x59cd,
    0x6c => 0x59cb, 0x6d => 0x59d3, 0x6e => 0x59ca, 0x6f => 0x59af,
    0x70 => 0x59b3, 0x71 => 0x59d2, 0x72 => 0x59c5, 0x73 => 0x5b5f,
    0x74 => 0x5b64, 0x75 => 0x5b63, 0x76 => 0x5b97, 0x77 => 0x5b9a,
    0x78 => 0x5b98, 0x79 => 0x5b9c, 0x7a => 0x5b99, 0x7b => 0x5b9b,
    0x7c => 0x5c1a, 0x7d => 0x5c48, 0x7e => 0x5c45, 0xa1 => 0x5c46,
    0xa2 => 0x5cb7, 0xa3 => 0x5ca1, 0xa4 => 0x5cb8, 0xa5 => 0x5ca9,
    0xa6 => 0x5cab, 0xa7 => 0x5cb1, 0xa8 => 0x5cb3, 0xa9 => 0x5e18,
    0xaa => 0x5e1a, 0xab => 0x5e16, 0xac => 0x5e15, 0xad => 0x5e1b,
    0xae => 0x5e11, 0xaf => 0x5e78, 0xb0 => 0x5e9a, 0xb1 => 0x5e97,
    0xb2 => 0x5e9c, 0xb3 => 0x5e95, 0xb4 => 0x5e96, 0xb5 => 0x5ef6,
    0xb6 => 0x5f26, 0xb7 => 0x5f27, 0xb8 => 0x5f29, 0xb9 => 0x5f80,
    0xba => 0x5f81, 0xbb => 0x5f7f, 0xbc => 0x5f7c, 0xbd => 0x5fdd,
    0xbe => 0x5fe0, 0xbf => 0x5ffd, 0xc0 => 0x5ff5, 0xc1 => 0x5fff,
    0xc2 => 0x600f, 0xc3 => 0x6014, 0xc4 => 0x602f, 0xc5 => 0x6035,
    0xc6 => 0x6016, 0xc7 => 0x602a, 0xc8 => 0x6015, 0xc9 => 0x6021,
    0xca => 0x6027, 0xcb => 0x6029, 0xcc => 0x602b, 0xcd => 0x601b,
    0xce => 0x6216, 0xcf => 0x6215, 0xd0 => 0x623f, 0xd1 => 0x623e,
    0xd2 => 0x6240, 0xd3 => 0x627f, 0xd4 => 0x62c9, 0xd5 => 0x62cc,
    0xd6 => 0x62c4, 0xd7 => 0x62bf, 0xd8 => 0x62c2, 0xd9 => 0x62b9,
    0xda => 0x62d2, 0xdb => 0x62db, 0xdc => 0x62ab, 0xdd => 0x62d3,
    0xde => 0x62d4, 0xdf => 0x62cb, 0xe0 => 0x62c8, 0xe1 => 0x62a8,
    0xe2 => 0x62bd, 0xe3 => 0x62bc, 0xe4 => 0x62d0, 0xe5 => 0x62d9,
    0xe6 => 0x62c7, 0xe7 => 0x62cd, 0xe8 => 0x62b5, 0xe9 => 0x62da,
    0xea => 0x62b1, 0xeb => 0x62d8, 0xec => 0x62d6, 0xed => 0x62d7,
    0xee => 0x62c6, 0xef => 0x62ac, 0xf0 => 0x62ce, 0xf1 => 0x653e,
    0xf2 => 0x65a7, 0xf3 => 0x65bc, 0xf4 => 0x65fa, 0xf5 => 0x6614,
    0xf6 => 0x6613, 0xf7 => 0x660c, 0xf8 => 0x6606, 0xf9 => 0x6602,
    0xfa => 0x660e, 0xfb => 0x6600, 0xfc => 0x660f, 0xfd => 0x6615,
    0xfe => 0x660a,
  },
  0xaa => {
    0x40 => 0x6607, 0x41 => 0x670d, 0x42 => 0x670b, 0x43 => 0x676d,
    0x44 => 0x678b, 0x45 => 0x6795, 0x46 => 0x6771, 0x47 => 0x679c,
    0x48 => 0x6773, 0x49 => 0x6777, 0x4a => 0x6787, 0x4b => 0x679d,
    0x4c => 0x6797, 0x4d => 0x676f, 0x4e => 0x6770, 0x4f => 0x677f,
    0x50 => 0x6789, 0x51 => 0x677e, 0x52 => 0x6790, 0x53 => 0x6775,
    0x54 => 0x679a, 0x55 => 0x6793, 0x56 => 0x677c, 0x57 => 0x676a,
    0x58 => 0x6772, 0x59 => 0x6b23, 0x5a => 0x6b66, 0x5b => 0x6b67,
    0x5c => 0x6b7f, 0x5d => 0x6c13, 0x5e => 0x6c1b, 0x5f => 0x6ce3,
    0x60 => 0x6ce8, 0x61 => 0x6cf3, 0x62 => 0x6cb1, 0x63 => 0x6ccc,
    0x64 => 0x6ce5, 0x65 => 0x6cb3, 0x66 => 0x6cbd, 0x67 => 0x6cbe,
    0x68 => 0x6cbc, 0x69 => 0x6ce2, 0x6a => 0x6cab, 0x6b => 0x6cd5,
    0x6c => 0x6cd3, 0x6d => 0x6cb8, 0x6e => 0x6cc4, 0x6f => 0x6cb9,
    0x70 => 0x6cc1, 0x71 => 0x6cae, 0x72 => 0x6cd7, 0x73 => 0x6cc5,
    0x74 => 0x6cf1, 0x75 => 0x6cbf, 0x76 => 0x6cbb, 0x77 => 0x6ce1,
    0x78 => 0x6cdb, 0x79 => 0x6cca, 0x7a => 0x6cac, 0x7b => 0x6cef,
    0x7c => 0x6cdc, 0x7d => 0x6cd6, 0x7e => 0x6ce0, 0xa1 => 0x7095,
    0xa2 => 0x708e, 0xa3 => 0x7092, 0xa4 => 0x708a, 0xa5 => 0x7099,
    0xa6 => 0x722c, 0xa7 => 0x722d, 0xa8 => 0x7238, 0xa9 => 0x7248,
    0xaa => 0x7267, 0xab => 0x7269, 0xac => 0x72c0, 0xad => 0x72ce,
    0xae => 0x72d9, 0xaf => 0x72d7, 0xb0 => 0x72d0, 0xb1 => 0x73a9,
    0xb2 => 0x73a8, 0xb3 => 0x739f, 0xb4 => 0x73ab, 0xb5 => 0x73a5,
    0xb6 => 0x753d, 0xb7 => 0x759d, 0xb8 => 0x7599, 0xb9 => 0x759a,
    0xba => 0x7684, 0xbb => 0x76c2, 0xbc => 0x76f2, 0xbd => 0x76f4,
    0xbe => 0x77e5, 0xbf => 0x77fd, 0xc0 => 0x793e, 0xc1 => 0x7940,
    0xc2 => 0x7941, 0xc3 => 0x79c9, 0xc4 => 0x79c8, 0xc5 => 0x7a7a,
    0xc6 => 0x7a79, 0xc7 => 0x7afa, 0xc8 => 0x7cfe, 0xc9 => 0x7f54,
    0xca => 0x7f8c, 0xcb => 0x7f8b, 0xcc => 0x8005, 0xcd => 0x80ba,
    0xce => 0x80a5, 0xcf => 0x80a2, 0xd0 => 0x80b1, 0xd1 => 0x80a1,
    0xd2 => 0x80ab, 0xd3 => 0x80a9, 0xd4 => 0x80b4, 0xd5 => 0x80aa,
    0xd6 => 0x80af, 0xd7 => 0x81e5, 0xd8 => 0x81fe, 0xd9 => 0x820d,
    0xda => 0x82b3, 0xdb => 0x829d, 0xdc => 0x8299, 0xdd => 0x82ad,
    0xde => 0x82bd, 0xdf => 0x829f, 0xe0 => 0x82b9, 0xe1 => 0x82b1,
    0xe2 => 0x82ac, 0xe3 => 0x82a5, 0xe4 => 0x82af, 0xe5 => 0x82b8,
    0xe6 => 0x82a3, 0xe7 => 0x82b0, 0xe8 => 0x82be, 0xe9 => 0x82b7,
    0xea => 0x864e, 0xeb => 0x8671, 0xec => 0x521d, 0xed => 0x8868,
    0xee => 0x8ecb, 0xef => 0x8fce, 0xf0 => 0x8fd4, 0xf1 => 0x8fd1,
    0xf2 => 0x90b5, 0xf3 => 0x90b8, 0xf4 => 0x90b1, 0xf5 => 0x90b6,
    0xf6 => 0x91c7, 0xf7 => 0x91d1, 0xf8 => 0x9577, 0xf9 => 0x9580,
    0xfa => 0x961c, 0xfb => 0x9640, 0xfc => 0x963f, 0xfd => 0x963b,
    0xfe => 0x9644,
  },
  0xab => {
    0x40 => 0x9642, 0x41 => 0x96b9, 0x42 => 0x96e8, 0x43 => 0x9752,
    0x44 => 0x975e, 0x45 => 0x4e9f, 0x46 => 0x4ead, 0x47 => 0x4eae,
    0x48 => 0x4fe1, 0x49 => 0x4fb5, 0x4a => 0x4faf, 0x4b => 0x4fbf,
    0x4c => 0x4fe0, 0x4d => 0x4fd1, 0x4e => 0x4fcf, 0x4f => 0x4fdd,
    0x50 => 0x4fc3, 0x51 => 0x4fb6, 0x52 => 0x4fd8, 0x53 => 0x4fdf,
    0x54 => 0x4fca, 0x55 => 0x4fd7, 0x56 => 0x4fae, 0x57 => 0x4fd0,
    0x58 => 0x4fc4, 0x59 => 0x4fc2, 0x5a => 0x4fda, 0x5b => 0x4fce,
    0x5c => 0x4fde, 0x5d => 0x4fb7, 0x5e => 0x5157, 0x5f => 0x5192,
    0x60 => 0x5191, 0x61 => 0x51a0, 0x62 => 0x524e, 0x63 => 0x5243,
    0x64 => 0x524a, 0x65 => 0x524d, 0x66 => 0x524c, 0x67 => 0x524b,
    0x68 => 0x5247, 0x69 => 0x52c7, 0x6a => 0x52c9, 0x6b => 0x52c3,
    0x6c => 0x52c1, 0x6d => 0x530d, 0x6e => 0x5357, 0x6f => 0x537b,
    0x70 => 0x539a, 0x71 => 0x53db, 0x72 => 0x54ac, 0x73 => 0x54c0,
    0x74 => 0x54a8, 0x75 => 0x54ce, 0x76 => 0x54c9, 0x77 => 0x54b8,
    0x78 => 0x54a6, 0x79 => 0x54b3, 0x7a => 0x54c7, 0x7b => 0x54c2,
    0x7c => 0x54bd, 0x7d => 0x54aa, 0x7e => 0x54c1, 0xa1 => 0x54c4,
    0xa2 => 0x54c8, 0xa3 => 0x54af, 0xa4 => 0x54ab, 0xa5 => 0x54b1,
    0xa6 => 0x54bb, 0xa7 => 0x54a9, 0xa8 => 0x54a7, 0xa9 => 0x54bf,
    0xaa => 0x56ff, 0xab => 0x5782, 0xac => 0x578b, 0xad => 0x57a0,
    0xae => 0x57a3, 0xaf => 0x57a2, 0xb0 => 0x57ce, 0xb1 => 0x57ae,
    0xb2 => 0x5793, 0xb3 => 0x5955, 0xb4 => 0x5951, 0xb5 => 0x594f,
    0xb6 => 0x594e, 0xb7 => 0x5950, 0xb8 => 0x59dc, 0xb9 => 0x59d8,
    0xba => 0x59ff, 0xbb => 0x59e3, 0xbc => 0x59e8, 0xbd => 0x5a03,
    0xbe => 0x59e5, 0xbf => 0x59ea, 0xc0 => 0x59da, 0xc1 => 0x59e6,
    0xc2 => 0x5a01, 0xc3 => 0x59fb, 0xc4 => 0x5b69, 0xc5 => 0x5ba3,
    0xc6 => 0x5ba6, 0xc7 => 0x5ba4, 0xc8 => 0x5ba2, 0xc9 => 0x5ba5,
    0xca => 0x5c01, 0xcb => 0x5c4e, 0xcc => 0x5c4f, 0xcd => 0x5c4d,
    0xce => 0x5c4b, 0xcf => 0x5cd9, 0xd0 => 0x5cd2, 0xd1 => 0x5df7,
    0xd2 => 0x5e1d, 0xd3 => 0x5e25, 0xd4 => 0x5e1f, 0xd5 => 0x5e7d,
    0xd6 => 0x5ea0, 0xd7 => 0x5ea6, 0xd8 => 0x5efa, 0xd9 => 0x5f08,
    0xda => 0x5f2d, 0xdb => 0x5f65, 0xdc => 0x5f88, 0xdd => 0x5f85,
    0xde => 0x5f8a, 0xdf => 0x5f8b, 0xe0 => 0x5f87, 0xe1 => 0x5f8c,
    0xe2 => 0x5f89, 0xe3 => 0x6012, 0xe4 => 0x601d, 0xe5 => 0x6020,
    0xe6 => 0x6025, 0xe7 => 0x600e, 0xe8 => 0x6028, 0xe9 => 0x604d,
    0xea => 0x6070, 0xeb => 0x6068, 0xec => 0x6062, 0xed => 0x6046,
    0xee => 0x6043, 0xef => 0x606c, 0xf0 => 0x606b, 0xf1 => 0x606a,
    0xf2 => 0x6064, 0xf3 => 0x6241, 0xf4 => 0x62dc, 0xf5 => 0x6316,
    0xf6 => 0x6309, 0xf7 => 0x62fc, 0xf8 => 0x62ed, 0xf9 => 0x6301,
    0xfa => 0x62ee, 0xfb => 0x62fd, 0xfc => 0x6307, 0xfd => 0x62f1,
    0xfe => 0x62f7,
  },
  0xac => {
    0x40 => 0x62ef, 0x41 => 0x62ec, 0x42 => 0x62fe, 0x43 => 0x62f4,
    0x44 => 0x6311, 0x45 => 0x6302, 0x46 => 0x653f, 0x47 => 0x6545,
    0x48 => 0x65ab, 0x49 => 0x65bd, 0x4a => 0x65e2, 0x4b => 0x6625,
    0x4c => 0x662d, 0x4d => 0x6620, 0x4e => 0x6627, 0x4f => 0x662f,
    0x50 => 0x661f, 0x51 => 0x6628, 0x52 => 0x6631, 0x53 => 0x6624,
    0x54 => 0x66f7, 0x55 => 0x67ff, 0x56 => 0x67d3, 0x57 => 0x67f1,
    0x58 => 0x67d4, 0x59 => 0x67d0, 0x5a => 0x67ec, 0x5b => 0x67b6,
    0x5c => 0x67af, 0x5d => 0x67f5, 0x5e => 0x67e9, 0x5f => 0x67ef,
    0x60 => 0x67c4, 0x61 => 0x67d1, 0x62 => 0x67b4, 0x63 => 0x67da,
    0x64 => 0x67e5, 0x65 => 0x67b8, 0x66 => 0x67cf, 0x67 => 0x67de,
    0x68 => 0x67f3, 0x69 => 0x67b0, 0x6a => 0x67d9, 0x6b => 0x67e2,
    0x6c => 0x67dd, 0x6d => 0x67d2, 0x6e => 0x6b6a, 0x6f => 0x6b83,
    0x70 => 0x6b86, 0x71 => 0x6bb5, 0x72 => 0x6bd2, 0x73 => 0x6bd7,
    0x74 => 0x6c1f, 0x75 => 0x6cc9, 0x76 => 0x6d0b, 0x77 => 0x6d32,
    0x78 => 0x6d2a, 0x79 => 0x6d41, 0x7a => 0x6d25, 0x7b => 0x6d0c,
    0x7c => 0x6d31, 0x7d => 0x6d1e, 0x7e => 0x6d17, 0xa1 => 0x6d3b,
    0xa2 => 0x6d3d, 0xa3 => 0x6d3e, 0xa4 => 0x6d36, 0xa5 => 0x6d1b,
    0xa6 => 0x6cf5, 0xa7 => 0x6d39, 0xa8 => 0x6d27, 0xa9 => 0x6d38,
    0xaa => 0x6d29, 0xab => 0x6d2e, 0xac => 0x6d35, 0xad => 0x6d0e,
    0xae => 0x6d2b, 0xaf => 0x70ab, 0xb0 => 0x70ba, 0xb1 => 0x70b3,
    0xb2 => 0x70ac, 0xb3 => 0x70af, 0xb4 => 0x70ad, 0xb5 => 0x70b8,
    0xb6 => 0x70ae, 0xb7 => 0x70a4, 0xb8 => 0x7230, 0xb9 => 0x7272,
    0xba => 0x726f, 0xbb => 0x7274, 0xbc => 0x72e9, 0xbd => 0x72e0,
    0xbe => 0x72e1, 0xbf => 0x73b7, 0xc0 => 0x73ca, 0xc1 => 0x73bb,
    0xc2 => 0x73b2, 0xc3 => 0x73cd, 0xc4 => 0x73c0, 0xc5 => 0x73b3,
    0xc6 => 0x751a, 0xc7 => 0x752d, 0xc8 => 0x754f, 0xc9 => 0x754c,
    0xca => 0x754e, 0xcb => 0x754b, 0xcc => 0x75ab, 0xcd => 0x75a4,
    0xce => 0x75a5, 0xcf => 0x75a2, 0xd0 => 0x75a3, 0xd1 => 0x7678,
    0xd2 => 0x7686, 0xd3 => 0x7687, 0xd4 => 0x7688, 0xd5 => 0x76c8,
    0xd6 => 0x76c6, 0xd7 => 0x76c3, 0xd8 => 0x76c5, 0xd9 => 0x7701,
    0xda => 0x76f9, 0xdb => 0x76f8, 0xdc => 0x7709, 0xdd => 0x770b,
    0xde => 0x76fe, 0xdf => 0x76fc, 0xe0 => 0x7707, 0xe1 => 0x77dc,
    0xe2 => 0x7802, 0xe3 => 0x7814, 0xe4 => 0x780c, 0xe5 => 0x780d,
    0xe6 => 0x7946, 0xe7 => 0x7949, 0xe8 => 0x7948, 0xe9 => 0x7947,
    0xea => 0x79b9, 0xeb => 0x79ba, 0xec => 0x79d1, 0xed => 0x79d2,
    0xee => 0x79cb, 0xef => 0x7a7f, 0xf0 => 0x7a81, 0xf1 => 0x7aff,
    0xf2 => 0x7afd, 0xf3 => 0x7c7d, 0xf4 => 0x7d02, 0xf5 => 0x7d05,
    0xf6 => 0x7d00, 0xf7 => 0x7d09, 0xf8 => 0x7d07, 0xf9 => 0x7d04,
    0xfa => 0x7d06, 0xfb => 0x7f38, 0xfc => 0x7f8e, 0xfd => 0x7fbf,
    0xfe => 0x8004,
  },
  0xad => {
    0x40 => 0x8010, 0x41 => 0x800d, 0x42 => 0x8011, 0x43 => 0x8036,
    0x44 => 0x80d6, 0x45 => 0x80e5, 0x46 => 0x80da, 0x47 => 0x80c3,
    0x48 => 0x80c4, 0x49 => 0x80cc, 0x4a => 0x80e1, 0x4b => 0x80db,
    0x4c => 0x80ce, 0x4d => 0x80de, 0x4e => 0x80e4, 0x4f => 0x80dd,
    0x50 => 0x81f4, 0x51 => 0x8222, 0x52 => 0x82e7, 0x53 => 0x8303,
    0x54 => 0x8305, 0x55 => 0x82e3, 0x56 => 0x82db, 0x57 => 0x82e6,
    0x58 => 0x8304, 0x59 => 0x82e5, 0x5a => 0x8302, 0x5b => 0x8309,
    0x5c => 0x82d2, 0x5d => 0x82d7, 0x5e => 0x82f1, 0x5f => 0x8301,
    0x60 => 0x82dc, 0x61 => 0x82d4, 0x62 => 0x82d1, 0x63 => 0x82de,
    0x64 => 0x82d3, 0x65 => 0x82df, 0x66 => 0x82ef, 0x67 => 0x8306,
    0x68 => 0x8650, 0x69 => 0x8679, 0x6a => 0x867b, 0x6b => 0x867a,
    0x6c => 0x884d, 0x6d => 0x886b, 0x6e => 0x8981, 0x6f => 0x89d4,
    0x70 => 0x8a08, 0x71 => 0x8a02, 0x72 => 0x8a03, 0x73 => 0x8c9e,
    0x74 => 0x8ca0, 0x75 => 0x8d74, 0x76 => 0x8d73, 0x77 => 0x8db4,
    0x78 => 0x8ecd, 0x79 => 0x8ecc, 0x7a => 0x8ff0, 0x7b => 0x8fe6,
    0x7c => 0x8fe2, 0x7d => 0x8fea, 0x7e => 0x8fe5, 0xa1 => 0x8fed,
    0xa2 => 0x8feb, 0xa3 => 0x8fe4, 0xa4 => 0x8fe8, 0xa5 => 0x90ca,
    0xa6 => 0x90ce, 0xa7 => 0x90c1, 0xa8 => 0x90c3, 0xa9 => 0x914b,
    0xaa => 0x914a, 0xab => 0x91cd, 0xac => 0x9582, 0xad => 0x9650,
    0xae => 0x964b, 0xaf => 0x964c, 0xb0 => 0x964d, 0xb1 => 0x9762,
    0xb2 => 0x9769, 0xb3 => 0x97cb, 0xb4 => 0x97ed, 0xb5 => 0x97f3,
    0xb6 => 0x9801, 0xb7 => 0x98a8, 0xb8 => 0x98db, 0xb9 => 0x98df,
    0xba => 0x9996, 0xbb => 0x9999, 0xbc => 0x4e58, 0xbd => 0x4eb3,
    0xbe => 0x500c, 0xbf => 0x500d, 0xc0 => 0x5023, 0xc1 => 0x4fef,
    0xc2 => 0x5026, 0xc3 => 0x5025, 0xc4 => 0x4ff8, 0xc5 => 0x5029,
    0xc6 => 0x5016, 0xc7 => 0x5006, 0xc8 => 0x503c, 0xc9 => 0x501f,
    0xca => 0x501a, 0xcb => 0x5012, 0xcc => 0x5011, 0xcd => 0x4ffa,
    0xce => 0x5000, 0xcf => 0x5014, 0xd0 => 0x5028, 0xd1 => 0x4ff1,
    0xd2 => 0x5021, 0xd3 => 0x500b, 0xd4 => 0x5019, 0xd5 => 0x5018,
    0xd6 => 0x4ff3, 0xd7 => 0x4fee, 0xd8 => 0x502d, 0xd9 => 0x502a,
    0xda => 0x4ffe, 0xdb => 0x502b, 0xdc => 0x5009, 0xdd => 0x517c,
    0xde => 0x51a4, 0xdf => 0x51a5, 0xe0 => 0x51a2, 0xe1 => 0x51cd,
    0xe2 => 0x51cc, 0xe3 => 0x51c6, 0xe4 => 0x51cb, 0xe5 => 0x5256,
    0xe6 => 0x525c, 0xe7 => 0x5254, 0xe8 => 0x525b, 0xe9 => 0x525d,
    0xea => 0x532a, 0xeb => 0x537f, 0xec => 0x539f, 0xed => 0x539d,
    0xee => 0x53df, 0xef => 0x54e8, 0xf0 => 0x5510, 0xf1 => 0x5501,
    0xf2 => 0x5537, 0xf3 => 0x54fc, 0xf4 => 0x54e5, 0xf5 => 0x54f2,
    0xf6 => 0x5506, 0xf7 => 0x54fa, 0xf8 => 0x5514, 0xf9 => 0x54e9,
    0xfa => 0x54ed, 0xfb => 0x54e1, 0xfc => 0x5509, 0xfd => 0x54ee,
    0xfe => 0x54ea,
  },
  0xae => {
    0x40 => 0x54e6, 0x41 => 0x5527, 0x42 => 0x5507, 0x43 => 0x54fd,
    0x44 => 0x550f, 0x45 => 0x5703, 0x46 => 0x5704, 0x47 => 0x57c2,
    0x48 => 0x57d4, 0x49 => 0x57cb, 0x4a => 0x57c3, 0x4b => 0x5809,
    0x4c => 0x590f, 0x4d => 0x5957, 0x4e => 0x5958, 0x4f => 0x595a,
    0x50 => 0x5a11, 0x51 => 0x5a18, 0x52 => 0x5a1c, 0x53 => 0x5a1f,
    0x54 => 0x5a1b, 0x55 => 0x5a13, 0x56 => 0x59ec, 0x57 => 0x5a20,
    0x58 => 0x5a23, 0x59 => 0x5a29, 0x5a => 0x5a25, 0x5b => 0x5a0c,
    0x5c => 0x5a09, 0x5d => 0x5b6b, 0x5e => 0x5c58, 0x5f => 0x5bb0,
    0x60 => 0x5bb3, 0x61 => 0x5bb6, 0x62 => 0x5bb4, 0x63 => 0x5bae,
    0x64 => 0x5bb5, 0x65 => 0x5bb9, 0x66 => 0x5bb8, 0x67 => 0x5c04,
    0x68 => 0x5c51, 0x69 => 0x5c55, 0x6a => 0x5c50, 0x6b => 0x5ced,
    0x6c => 0x5cfd, 0x6d => 0x5cfb, 0x6e => 0x5cea, 0x6f => 0x5ce8,
    0x70 => 0x5cf0, 0x71 => 0x5cf6, 0x72 => 0x5d01, 0x73 => 0x5cf4,
    0x74 => 0x5dee, 0x75 => 0x5e2d, 0x76 => 0x5e2b, 0x77 => 0x5eab,
    0x78 => 0x5ead, 0x79 => 0x5ea7, 0x7a => 0x5f31, 0x7b => 0x5f92,
    0x7c => 0x5f91, 0x7d => 0x5f90, 0x7e => 0x6059, 0xa1 => 0x6063,
    0xa2 => 0x6065, 0xa3 => 0x6050, 0xa4 => 0x6055, 0xa5 => 0x606d,
    0xa6 => 0x6069, 0xa7 => 0x606f, 0xa8 => 0x6084, 0xa9 => 0x609f,
    0xaa => 0x609a, 0xab => 0x608d, 0xac => 0x6094, 0xad => 0x608c,
    0xae => 0x6085, 0xaf => 0x6096, 0xb0 => 0x6247, 0xb1 => 0x62f3,
    0xb2 => 0x6308, 0xb3 => 0x62ff, 0xb4 => 0x634e, 0xb5 => 0x633e,
    0xb6 => 0x632f, 0xb7 => 0x6355, 0xb8 => 0x6342, 0xb9 => 0x6346,
    0xba => 0x634f, 0xbb => 0x6349, 0xbc => 0x633a, 0xbd => 0x6350,
    0xbe => 0x633d, 0xbf => 0x632a, 0xc0 => 0x632b, 0xc1 => 0x6328,
    0xc2 => 0x634d, 0xc3 => 0x634c, 0xc4 => 0x6548, 0xc5 => 0x6549,
    0xc6 => 0x6599, 0xc7 => 0x65c1, 0xc8 => 0x65c5, 0xc9 => 0x6642,
    0xca => 0x6649, 0xcb => 0x664f, 0xcc => 0x6643, 0xcd => 0x6652,
    0xce => 0x664c, 0xcf => 0x6645, 0xd0 => 0x6641, 0xd1 => 0x66f8,
    0xd2 => 0x6714, 0xd3 => 0x6715, 0xd4 => 0x6717, 0xd5 => 0x6821,
    0xd6 => 0x6838, 0xd7 => 0x6848, 0xd8 => 0x6846, 0xd9 => 0x6853,
    0xda => 0x6839, 0xdb => 0x6842, 0xdc => 0x6854, 0xdd => 0x6829,
    0xde => 0x68b3, 0xdf => 0x6817, 0xe0 => 0x684c, 0xe1 => 0x6851,
    0xe2 => 0x683d, 0xe3 => 0x67f4, 0xe4 => 0x6850, 0xe5 => 0x6840,
    0xe6 => 0x683c, 0xe7 => 0x6843, 0xe8 => 0x682a, 0xe9 => 0x6845,
    0xea => 0x6813, 0xeb => 0x6818, 0xec => 0x6841, 0xed => 0x6b8a,
    0xee => 0x6b89, 0xef => 0x6bb7, 0xf0 => 0x6c23, 0xf1 => 0x6c27,
    0xf2 => 0x6c28, 0xf3 => 0x6c26, 0xf4 => 0x6c24, 0xf5 => 0x6cf0,
    0xf6 => 0x6d6a, 0xf7 => 0x6d95, 0xf8 => 0x6d88, 0xf9 => 0x6d87,
    0xfa => 0x6d66, 0xfb => 0x6d78, 0xfc => 0x6d77, 0xfd => 0x6d59,
    0xfe => 0x6d93,
  },
  0xaf => {
    0x40 => 0x6d6c, 0x41 => 0x6d89, 0x42 => 0x6d6e, 0x43 => 0x6d5a,
    0x44 => 0x6d74, 0x45 => 0x6d69, 0x46 => 0x6d8c, 0x47 => 0x6d8a,
    0x48 => 0x6d79, 0x49 => 0x6d85, 0x4a => 0x6d65, 0x4b => 0x6d94,
    0x4c => 0x70ca, 0x4d => 0x70d8, 0x4e => 0x70e4, 0x4f => 0x70d9,
    0x50 => 0x70c8, 0x51 => 0x70cf, 0x52 => 0x7239, 0x53 => 0x7279,
    0x54 => 0x72fc, 0x55 => 0x72f9, 0x56 => 0x72fd, 0x57 => 0x72f8,
    0x58 => 0x72f7, 0x59 => 0x7386, 0x5a => 0x73ed, 0x5b => 0x7409,
    0x5c => 0x73ee, 0x5d => 0x73e0, 0x5e => 0x73ea, 0x5f => 0x73de,
    0x60 => 0x7554, 0x61 => 0x755d, 0x62 => 0x755c, 0x63 => 0x755a,
    0x64 => 0x7559, 0x65 => 0x75be, 0x66 => 0x75c5, 0x67 => 0x75c7,
    0x68 => 0x75b2, 0x69 => 0x75b3, 0x6a => 0x75bd, 0x6b => 0x75bc,
    0x6c => 0x75b9, 0x6d => 0x75c2, 0x6e => 0x75b8, 0x6f => 0x768b,
    0x70 => 0x76b0, 0x71 => 0x76ca, 0x72 => 0x76cd, 0x73 => 0x76ce,
    0x74 => 0x7729, 0x75 => 0x771f, 0x76 => 0x7720, 0x77 => 0x7728,
    0x78 => 0x77e9, 0x79 => 0x7830, 0x7a => 0x7827, 0x7b => 0x7838,
    0x7c => 0x781d, 0x7d => 0x7834, 0x7e => 0x7837, 0xa1 => 0x7825,
    0xa2 => 0x782d, 0xa3 => 0x7820, 0xa4 => 0x781f, 0xa5 => 0x7832,
    0xa6 => 0x7955, 0xa7 => 0x7950, 0xa8 => 0x7960, 0xa9 => 0x795f,
    0xaa => 0x7956, 0xab => 0x795e, 0xac => 0x795d, 0xad => 0x7957,
    0xae => 0x795a, 0xaf => 0x79e4, 0xb0 => 0x79e3, 0xb1 => 0x79e7,
    0xb2 => 0x79df, 0xb3 => 0x79e6, 0xb4 => 0x79e9, 0xb5 => 0x79d8,
    0xb6 => 0x7a84, 0xb7 => 0x7a88, 0xb8 => 0x7ad9, 0xb9 => 0x7b06,
    0xba => 0x7b11, 0xbb => 0x7c89, 0xbc => 0x7d21, 0xbd => 0x7d17,
    0xbe => 0x7d0b, 0xbf => 0x7d0a, 0xc0 => 0x7d20, 0xc1 => 0x7d22,
    0xc2 => 0x7d14, 0xc3 => 0x7d10, 0xc4 => 0x7d15, 0xc5 => 0x7d1a,
    0xc6 => 0x7d1c, 0xc7 => 0x7d0d, 0xc8 => 0x7d19, 0xc9 => 0x7d1b,
    0xca => 0x7f3a, 0xcb => 0x7f5f, 0xcc => 0x7f94, 0xcd => 0x7fc5,
    0xce => 0x7fc1, 0xcf => 0x8006, 0xd0 => 0x8018, 0xd1 => 0x8015,
    0xd2 => 0x8019, 0xd3 => 0x8017, 0xd4 => 0x803d, 0xd5 => 0x803f,
    0xd6 => 0x80f1, 0xd7 => 0x8102, 0xd8 => 0x80f0, 0xd9 => 0x8105,
    0xda => 0x80ed, 0xdb => 0x80f4, 0xdc => 0x8106, 0xdd => 0x80f8,
    0xde => 0x80f3, 0xdf => 0x8108, 0xe0 => 0x80fd, 0xe1 => 0x810a,
    0xe2 => 0x80fc, 0xe3 => 0x80ef, 0xe4 => 0x81ed, 0xe5 => 0x81ec,
    0xe6 => 0x8200, 0xe7 => 0x8210, 0xe8 => 0x822a, 0xe9 => 0x822b,
    0xea => 0x8228, 0xeb => 0x822c, 0xec => 0x82bb, 0xed => 0x832b,
    0xee => 0x8352, 0xef => 0x8354, 0xf0 => 0x834a, 0xf1 => 0x8338,
    0xf2 => 0x8350, 0xf3 => 0x8349, 0xf4 => 0x8335, 0xf5 => 0x8334,
    0xf6 => 0x834f, 0xf7 => 0x8332, 0xf8 => 0x8339, 0xf9 => 0x8336,
    0xfa => 0x8317, 0xfb => 0x8340, 0xfc => 0x8331, 0xfd => 0x8328,
    0xfe => 0x8343,
  },
  0xb0 => {
    0x40 => 0x8654, 0x41 => 0x868a, 0x42 => 0x86aa, 0x43 => 0x8693,
    0x44 => 0x86a4, 0x45 => 0x86a9, 0x46 => 0x868c, 0x47 => 0x86a3,
    0x48 => 0x869c, 0x49 => 0x8870, 0x4a => 0x8877, 0x4b => 0x8881,
    0x4c => 0x8882, 0x4d => 0x887d, 0x4e => 0x8879, 0x4f => 0x8a18,
    0x50 => 0x8a10, 0x51 => 0x8a0e, 0x52 => 0x8a0c, 0x53 => 0x8a15,
    0x54 => 0x8a0a, 0x55 => 0x8a17, 0x56 => 0x8a13, 0x57 => 0x8a16,
    0x58 => 0x8a0f, 0x59 => 0x8a11, 0x5a => 0x8c48, 0x5b => 0x8c7a,
    0x5c => 0x8c79, 0x5d => 0x8ca1, 0x5e => 0x8ca2, 0x5f => 0x8d77,
    0x60 => 0x8eac, 0x61 => 0x8ed2, 0x62 => 0x8ed4, 0x63 => 0x8ecf,
    0x64 => 0x8fb1, 0x65 => 0x9001, 0x66 => 0x9006, 0x67 => 0x8ff7,
    0x68 => 0x9000, 0x69 => 0x8ffa, 0x6a => 0x8ff4, 0x6b => 0x9003,
    0x6c => 0x8ffd, 0x6d => 0x9005, 0x6e => 0x8ff8, 0x6f => 0x9095,
    0x70 => 0x90e1, 0x71 => 0x90dd, 0x72 => 0x90e2, 0x73 => 0x9152,
    0x74 => 0x914d, 0x75 => 0x914c, 0x76 => 0x91d8, 0x77 => 0x91dd,
    0x78 => 0x91d7, 0x79 => 0x91dc, 0x7a => 0x91d9, 0x7b => 0x9583,
    0x7c => 0x9662, 0x7d => 0x9663, 0x7e => 0x9661, 0xa1 => 0x965b,
    0xa2 => 0x965d, 0xa3 => 0x9664, 0xa4 => 0x9658, 0xa5 => 0x965e,
    0xa6 => 0x96bb, 0xa7 => 0x98e2, 0xa8 => 0x99ac, 0xa9 => 0x9aa8,
    0xaa => 0x9ad8, 0xab => 0x9b25, 0xac => 0x9b32, 0xad => 0x9b3c,
    0xae => 0x4e7e, 0xaf => 0x507a, 0xb0 => 0x507d, 0xb1 => 0x505c,
    0xb2 => 0x5047, 0xb3 => 0x5043, 0xb4 => 0x504c, 0xb5 => 0x505a,
    0xb6 => 0x5049, 0xb7 => 0x5065, 0xb8 => 0x5076, 0xb9 => 0x504e,
    0xba => 0x5055, 0xbb => 0x5075, 0xbc => 0x5074, 0xbd => 0x5077,
    0xbe => 0x504f, 0xbf => 0x500f, 0xc0 => 0x506f, 0xc1 => 0x506d,
    0xc2 => 0x515c, 0xc3 => 0x5195, 0xc4 => 0x51f0, 0xc5 => 0x526a,
    0xc6 => 0x526f, 0xc7 => 0x52d2, 0xc8 => 0x52d9, 0xc9 => 0x52d8,
    0xca => 0x52d5, 0xcb => 0x5310, 0xcc => 0x530f, 0xcd => 0x5319,
    0xce => 0x533f, 0xcf => 0x5340, 0xd0 => 0x533e, 0xd1 => 0x53c3,
    0xd2 => 0x66fc, 0xd3 => 0x5546, 0xd4 => 0x556a, 0xd5 => 0x5566,
    0xd6 => 0x5544, 0xd7 => 0x555e, 0xd8 => 0x5561, 0xd9 => 0x5543,
    0xda => 0x554a, 0xdb => 0x5531, 0xdc => 0x5556, 0xdd => 0x554f,
    0xde => 0x5555, 0xdf => 0x552f, 0xe0 => 0x5564, 0xe1 => 0x5538,
    0xe2 => 0x552e, 0xe3 => 0x555c, 0xe4 => 0x552c, 0xe5 => 0x5563,
    0xe6 => 0x5533, 0xe7 => 0x5541, 0xe8 => 0x5557, 0xe9 => 0x5708,
    0xea => 0x570b, 0xeb => 0x5709, 0xec => 0x57df, 0xed => 0x5805,
    0xee => 0x580a, 0xef => 0x5806, 0xf0 => 0x57e0, 0xf1 => 0x57e4,
    0xf2 => 0x57fa, 0xf3 => 0x5802, 0xf4 => 0x5835, 0xf5 => 0x57f7,
    0xf6 => 0x57f9, 0xf7 => 0x5920, 0xf8 => 0x5962, 0xf9 => 0x5a36,
    0xfa => 0x5a41, 0xfb => 0x5a49, 0xfc => 0x5a66, 0xfd => 0x5a6a,
    0xfe => 0x5a40,
  },
  0xb1 => {
    0x40 => 0x5a3c, 0x41 => 0x5a62, 0x42 => 0x5a5a, 0x43 => 0x5a46,
    0x44 => 0x5a4a, 0x45 => 0x5b70, 0x46 => 0x5bc7, 0x47 => 0x5bc5,
    0x48 => 0x5bc4, 0x49 => 0x5bc2, 0x4a => 0x5bbf, 0x4b => 0x5bc6,
    0x4c => 0x5c09, 0x4d => 0x5c08, 0x4e => 0x5c07, 0x4f => 0x5c60,
    0x50 => 0x5c5c, 0x51 => 0x5c5d, 0x52 => 0x5d07, 0x53 => 0x5d06,
    0x54 => 0x5d0e, 0x55 => 0x5d1b, 0x56 => 0x5d16, 0x57 => 0x5d22,
    0x58 => 0x5d11, 0x59 => 0x5d29, 0x5a => 0x5d14, 0x5b => 0x5d19,
    0x5c => 0x5d24, 0x5d => 0x5d27, 0x5e => 0x5d17, 0x5f => 0x5de2,
    0x60 => 0x5e38, 0x61 => 0x5e36, 0x62 => 0x5e33, 0x63 => 0x5e37,
    0x64 => 0x5eb7, 0x65 => 0x5eb8, 0x66 => 0x5eb6, 0x67 => 0x5eb5,
    0x68 => 0x5ebe, 0x69 => 0x5f35, 0x6a => 0x5f37, 0x6b => 0x5f57,
    0x6c => 0x5f6c, 0x6d => 0x5f69, 0x6e => 0x5f6b, 0x6f => 0x5f97,
    0x70 => 0x5f99, 0x71 => 0x5f9e, 0x72 => 0x5f98, 0x73 => 0x5fa1,
    0x74 => 0x5fa0, 0x75 => 0x5f9c, 0x76 => 0x607f, 0x77 => 0x60a3,
    0x78 => 0x6089, 0x79 => 0x60a0, 0x7a => 0x60a8, 0x7b => 0x60cb,
    0x7c => 0x60b4, 0x7d => 0x60e6, 0x7e => 0x60bd, 0xa1 => 0x60c5,
    0xa2 => 0x60bb, 0xa3 => 0x60b5, 0xa4 => 0x60dc, 0xa5 => 0x60bc,
    0xa6 => 0x60d8, 0xa7 => 0x60d5, 0xa8 => 0x60c6, 0xa9 => 0x60df,
    0xaa => 0x60b8, 0xab => 0x60da, 0xac => 0x60c7, 0xad => 0x621a,
    0xae => 0x621b, 0xaf => 0x6248, 0xb0 => 0x63a0, 0xb1 => 0x63a7,
    0xb2 => 0x6372, 0xb3 => 0x6396, 0xb4 => 0x63a2, 0xb5 => 0x63a5,
    0xb6 => 0x6377, 0xb7 => 0x6367, 0xb8 => 0x6398, 0xb9 => 0x63aa,
    0xba => 0x6371, 0xbb => 0x63a9, 0xbc => 0x6389, 0xbd => 0x6383,
    0xbe => 0x639b, 0xbf => 0x636b, 0xc0 => 0x63a8, 0xc1 => 0x6384,
    0xc2 => 0x6388, 0xc3 => 0x6399, 0xc4 => 0x63a1, 0xc5 => 0x63ac,
    0xc6 => 0x6392, 0xc7 => 0x638f, 0xc8 => 0x6380, 0xc9 => 0x637b,
    0xca => 0x6369, 0xcb => 0x6368, 0xcc => 0x637a, 0xcd => 0x655d,
    0xce => 0x6556, 0xcf => 0x6551, 0xd0 => 0x6559, 0xd1 => 0x6557,
    0xd2 => 0x555f, 0xd3 => 0x654f, 0xd4 => 0x6558, 0xd5 => 0x6555,
    0xd6 => 0x6554, 0xd7 => 0x659c, 0xd8 => 0x659b, 0xd9 => 0x65ac,
    0xda => 0x65cf, 0xdb => 0x65cb, 0xdc => 0x65cc, 0xdd => 0x65ce,
    0xde => 0x665d, 0xdf => 0x665a, 0xe0 => 0x6664, 0xe1 => 0x6668,
    0xe2 => 0x6666, 0xe3 => 0x665e, 0xe4 => 0x66f9, 0xe5 => 0x52d7,
    0xe6 => 0x671b, 0xe7 => 0x6881, 0xe8 => 0x68af, 0xe9 => 0x68a2,
    0xea => 0x6893, 0xeb => 0x68b5, 0xec => 0x687f, 0xed => 0x6876,
    0xee => 0x68b1, 0xef => 0x68a7, 0xf0 => 0x6897, 0xf1 => 0x68b0,
    0xf2 => 0x6883, 0xf3 => 0x68c4, 0xf4 => 0x68ad, 0xf5 => 0x6886,
    0xf6 => 0x6885, 0xf7 => 0x6894, 0xf8 => 0x689d, 0xf9 => 0x68a8,
    0xfa => 0x689f, 0xfb => 0x68a1, 0xfc => 0x6882, 0xfd => 0x6b32,
    0xfe => 0x6bba,
  },
  0xb2 => {
    0x40 => 0x6beb, 0x41 => 0x6bec, 0x42 => 0x6c2b, 0x43 => 0x6d8e,
    0x44 => 0x6dbc, 0x45 => 0x6df3, 0x46 => 0x6dd9, 0x47 => 0x6db2,
    0x48 => 0x6de1, 0x49 => 0x6dcc, 0x4a => 0x6de4, 0x4b => 0x6dfb,
    0x4c => 0x6dfa, 0x4d => 0x6e05, 0x4e => 0x6dc7, 0x4f => 0x6dcb,
    0x50 => 0x6daf, 0x51 => 0x6dd1, 0x52 => 0x6dae, 0x53 => 0x6dde,
    0x54 => 0x6df9, 0x55 => 0x6db8, 0x56 => 0x6df7, 0x57 => 0x6df5,
    0x58 => 0x6dc5, 0x59 => 0x6dd2, 0x5a => 0x6e1a, 0x5b => 0x6db5,
    0x5c => 0x6dda, 0x5d => 0x6deb, 0x5e => 0x6dd8, 0x5f => 0x6dea,
    0x60 => 0x6df1, 0x61 => 0x6dee, 0x62 => 0x6de8, 0x63 => 0x6dc6,
    0x64 => 0x6dc4, 0x65 => 0x6daa, 0x66 => 0x6dec, 0x67 => 0x6dbf,
    0x68 => 0x6de6, 0x69 => 0x70f9, 0x6a => 0x7109, 0x6b => 0x710a,
    0x6c => 0x70fd, 0x6d => 0x70ef, 0x6e => 0x723d, 0x6f => 0x727d,
    0x70 => 0x7281, 0x71 => 0x731c, 0x72 => 0x731b, 0x73 => 0x7316,
    0x74 => 0x7313, 0x75 => 0x7319, 0x76 => 0x7387, 0x77 => 0x7405,
    0x78 => 0x740a, 0x79 => 0x7403, 0x7a => 0x7406, 0x7b => 0x73fe,
    0x7c => 0x740d, 0x7d => 0x74e0, 0x7e => 0x74f6, 0xa1 => 0x74f7,
    0xa2 => 0x751c, 0xa3 => 0x7522, 0xa4 => 0x7565, 0xa5 => 0x7566,
    0xa6 => 0x7562, 0xa7 => 0x7570, 0xa8 => 0x758f, 0xa9 => 0x75d4,
    0xaa => 0x75d5, 0xab => 0x75b5, 0xac => 0x75ca, 0xad => 0x75cd,
    0xae => 0x768e, 0xaf => 0x76d4, 0xb0 => 0x76d2, 0xb1 => 0x76db,
    0xb2 => 0x7737, 0xb3 => 0x773e, 0xb4 => 0x773c, 0xb5 => 0x7736,
    0xb6 => 0x7738, 0xb7 => 0x773a, 0xb8 => 0x786b, 0xb9 => 0x7843,
    0xba => 0x784e, 0xbb => 0x7965, 0xbc => 0x7968, 0xbd => 0x796d,
    0xbe => 0x79fb, 0xbf => 0x7a92, 0xc0 => 0x7a95, 0xc1 => 0x7b20,
    0xc2 => 0x7b28, 0xc3 => 0x7b1b, 0xc4 => 0x7b2c, 0xc5 => 0x7b26,
    0xc6 => 0x7b19, 0xc7 => 0x7b1e, 0xc8 => 0x7b2e, 0xc9 => 0x7c92,
    0xca => 0x7c97, 0xcb => 0x7c95, 0xcc => 0x7d46, 0xcd => 0x7d43,
    0xce => 0x7d71, 0xcf => 0x7d2e, 0xd0 => 0x7d39, 0xd1 => 0x7d3c,
    0xd2 => 0x7d40, 0xd3 => 0x7d30, 0xd4 => 0x7d33, 0xd5 => 0x7d44,
    0xd6 => 0x7d2f, 0xd7 => 0x7d42, 0xd8 => 0x7d32, 0xd9 => 0x7d31,
    0xda => 0x7f3d, 0xdb => 0x7f9e, 0xdc => 0x7f9a, 0xdd => 0x7fcc,
    0xde => 0x7fce, 0xdf => 0x7fd2, 0xe0 => 0x801c, 0xe1 => 0x804a,
    0xe2 => 0x8046, 0xe3 => 0x812f, 0xe4 => 0x8116, 0xe5 => 0x8123,
    0xe6 => 0x812b, 0xe7 => 0x8129, 0xe8 => 0x8130, 0xe9 => 0x8124,
    0xea => 0x8202, 0xeb => 0x8235, 0xec => 0x8237, 0xed => 0x8236,
    0xee => 0x8239, 0xef => 0x838e, 0xf0 => 0x839e, 0xf1 => 0x8398,
    0xf2 => 0x8378, 0xf3 => 0x83a2, 0xf4 => 0x8396, 0xf5 => 0x83bd,
    0xf6 => 0x83ab, 0xf7 => 0x8392, 0xf8 => 0x838a, 0xf9 => 0x8393,
    0xfa => 0x8389, 0xfb => 0x83a0, 0xfc => 0x8377, 0xfd => 0x837b,
    0xfe => 0x837c,
  },
  0xb3 => {
    0x40 => 0x8386, 0x41 => 0x83a7, 0x42 => 0x8655, 0x43 => 0x5f6a,
    0x44 => 0x86c7, 0x45 => 0x86c0, 0x46 => 0x86b6, 0x47 => 0x86c4,
    0x48 => 0x86b5, 0x49 => 0x86c6, 0x4a => 0x86cb, 0x4b => 0x86b1,
    0x4c => 0x86af, 0x4d => 0x86c9, 0x4e => 0x8853, 0x4f => 0x889e,
    0x50 => 0x8888, 0x51 => 0x88ab, 0x52 => 0x8892, 0x53 => 0x8896,
    0x54 => 0x888d, 0x55 => 0x888b, 0x56 => 0x8993, 0x57 => 0x898f,
    0x58 => 0x8a2a, 0x59 => 0x8a1d, 0x5a => 0x8a23, 0x5b => 0x8a25,
    0x5c => 0x8a31, 0x5d => 0x8a2d, 0x5e => 0x8a1f, 0x5f => 0x8a1b,
    0x60 => 0x8a22, 0x61 => 0x8c49, 0x62 => 0x8c5a, 0x63 => 0x8ca9,
    0x64 => 0x8cac, 0x65 => 0x8cab, 0x66 => 0x8ca8, 0x67 => 0x8caa,
    0x68 => 0x8ca7, 0x69 => 0x8d67, 0x6a => 0x8d66, 0x6b => 0x8dbe,
    0x6c => 0x8dba, 0x6d => 0x8edb, 0x6e => 0x8edf, 0x6f => 0x9019,
    0x70 => 0x900d, 0x71 => 0x901a, 0x72 => 0x9017, 0x73 => 0x9023,
    0x74 => 0x901f, 0x75 => 0x901d, 0x76 => 0x9010, 0x77 => 0x9015,
    0x78 => 0x901e, 0x79 => 0x9020, 0x7a => 0x900f, 0x7b => 0x9022,
    0x7c => 0x9016, 0x7d => 0x901b, 0x7e => 0x9014, 0xa1 => 0x90e8,
    0xa2 => 0x90ed, 0xa3 => 0x90fd, 0xa4 => 0x9157, 0xa5 => 0x91ce,
    0xa6 => 0x91f5, 0xa7 => 0x91e6, 0xa8 => 0x91e3, 0xa9 => 0x91e7,
    0xaa => 0x91ed, 0xab => 0x91e9, 0xac => 0x9589, 0xad => 0x966a,
    0xae => 0x9675, 0xaf => 0x9673, 0xb0 => 0x9678, 0xb1 => 0x9670,
    0xb2 => 0x9674, 0xb3 => 0x9676, 0xb4 => 0x9677, 0xb5 => 0x966c,
    0xb6 => 0x96c0, 0xb7 => 0x96ea, 0xb8 => 0x96e9, 0xb9 => 0x7ae0,
    0xba => 0x7adf, 0xbb => 0x9802, 0xbc => 0x9803, 0xbd => 0x9b5a,
    0xbe => 0x9ce5, 0xbf => 0x9e75, 0xc0 => 0x9e7f, 0xc1 => 0x9ea5,
    0xc2 => 0x9ebb, 0xc3 => 0x50a2, 0xc4 => 0x508d, 0xc5 => 0x5085,
    0xc6 => 0x5099, 0xc7 => 0x5091, 0xc8 => 0x5080, 0xc9 => 0x5096,
    0xca => 0x5098, 0xcb => 0x509a, 0xcc => 0x6700, 0xcd => 0x51f1,
    0xce => 0x5272, 0xcf => 0x5274, 0xd0 => 0x5275, 0xd1 => 0x5269,
    0xd2 => 0x52de, 0xd3 => 0x52dd, 0xd4 => 0x52db, 0xd5 => 0x535a,
    0xd6 => 0x53a5, 0xd7 => 0x557b, 0xd8 => 0x5580, 0xd9 => 0x55a7,
    0xda => 0x557c, 0xdb => 0x558a, 0xdc => 0x559d, 0xdd => 0x5598,
    0xde => 0x5582, 0xdf => 0x559c, 0xe0 => 0x55aa, 0xe1 => 0x5594,
    0xe2 => 0x5587, 0xe3 => 0x558b, 0xe4 => 0x5583, 0xe5 => 0x55b3,
    0xe6 => 0x55ae, 0xe7 => 0x559f, 0xe8 => 0x553e, 0xe9 => 0x55b2,
    0xea => 0x559a, 0xeb => 0x55bb, 0xec => 0x55ac, 0xed => 0x55b1,
    0xee => 0x557e, 0xef => 0x5589, 0xf0 => 0x55ab, 0xf1 => 0x5599,
    0xf2 => 0x570d, 0xf3 => 0x582f, 0xf4 => 0x582a, 0xf5 => 0x5834,
    0xf6 => 0x5824, 0xf7 => 0x5830, 0xf8 => 0x5831, 0xf9 => 0x5821,
    0xfa => 0x581d, 0xfb => 0x5820, 0xfc => 0x58f9, 0xfd => 0x58fa,
    0xfe => 0x5960,
  },
  0xb4 => {
    0x40 => 0x5a77, 0x41 => 0x5a9a, 0x42 => 0x5a7f, 0x43 => 0x5a92,
    0x44 => 0x5a9b, 0x45 => 0x5aa7, 0x46 => 0x5b73, 0x47 => 0x5b71,
    0x48 => 0x5bd2, 0x49 => 0x5bcc, 0x4a => 0x5bd3, 0x4b => 0x5bd0,
    0x4c => 0x5c0a, 0x4d => 0x5c0b, 0x4e => 0x5c31, 0x4f => 0x5d4c,
    0x50 => 0x5d50, 0x51 => 0x5d34, 0x52 => 0x5d47, 0x53 => 0x5dfd,
    0x54 => 0x5e45, 0x55 => 0x5e3d, 0x56 => 0x5e40, 0x57 => 0x5e43,
    0x58 => 0x5e7e, 0x59 => 0x5eca, 0x5a => 0x5ec1, 0x5b => 0x5ec2,
    0x5c => 0x5ec4, 0x5d => 0x5f3c, 0x5e => 0x5f6d, 0x5f => 0x5fa9,
    0x60 => 0x5faa, 0x61 => 0x5fa8, 0x62 => 0x60d1, 0x63 => 0x60e1,
    0x64 => 0x60b2, 0x65 => 0x60b6, 0x66 => 0x60e0, 0x67 => 0x611c,
    0x68 => 0x6123, 0x69 => 0x60fa, 0x6a => 0x6115, 0x6b => 0x60f0,
    0x6c => 0x60fb, 0x6d => 0x60f4, 0x6e => 0x6168, 0x6f => 0x60f1,
    0x70 => 0x610e, 0x71 => 0x60f6, 0x72 => 0x6109, 0x73 => 0x6100,
    0x74 => 0x6112, 0x75 => 0x621f, 0x76 => 0x6249, 0x77 => 0x63a3,
    0x78 => 0x638c, 0x79 => 0x63cf, 0x7a => 0x63c0, 0x7b => 0x63e9,
    0x7c => 0x63c9, 0x7d => 0x63c6, 0x7e => 0x63cd, 0xa1 => 0x63d2,
    0xa2 => 0x63e3, 0xa3 => 0x63d0, 0xa4 => 0x63e1, 0xa5 => 0x63d6,
    0xa6 => 0x63ed, 0xa7 => 0x63ee, 0xa8 => 0x6376, 0xa9 => 0x63f4,
    0xaa => 0x63ea, 0xab => 0x63db, 0xac => 0x6452, 0xad => 0x63da,
    0xae => 0x63f9, 0xaf => 0x655e, 0xb0 => 0x6566, 0xb1 => 0x6562,
    0xb2 => 0x6563, 0xb3 => 0x6591, 0xb4 => 0x6590, 0xb5 => 0x65af,
    0xb6 => 0x666e, 0xb7 => 0x6670, 0xb8 => 0x6674, 0xb9 => 0x6676,
    0xba => 0x666f, 0xbb => 0x6691, 0xbc => 0x667a, 0xbd => 0x667e,
    0xbe => 0x6677, 0xbf => 0x66fe, 0xc0 => 0x66ff, 0xc1 => 0x671f,
    0xc2 => 0x671d, 0xc3 => 0x68fa, 0xc4 => 0x68d5, 0xc5 => 0x68e0,
    0xc6 => 0x68d8, 0xc7 => 0x68d7, 0xc8 => 0x6905, 0xc9 => 0x68df,
    0xca => 0x68f5, 0xcb => 0x68ee, 0xcc => 0x68e7, 0xcd => 0x68f9,
    0xce => 0x68d2, 0xcf => 0x68f2, 0xd0 => 0x68e3, 0xd1 => 0x68cb,
    0xd2 => 0x68cd, 0xd3 => 0x690d, 0xd4 => 0x6912, 0xd5 => 0x690e,
    0xd6 => 0x68c9, 0xd7 => 0x68da, 0xd8 => 0x696e, 0xd9 => 0x68fb,
    0xda => 0x6b3e, 0xdb => 0x6b3a, 0xdc => 0x6b3d, 0xdd => 0x6b98,
    0xde => 0x6b96, 0xdf => 0x6bbc, 0xe0 => 0x6bef, 0xe1 => 0x6c2e,
    0xe2 => 0x6c2f, 0xe3 => 0x6c2c, 0xe4 => 0x6e2f, 0xe5 => 0x6e38,
    0xe6 => 0x6e54, 0xe7 => 0x6e21, 0xe8 => 0x6e32, 0xe9 => 0x6e67,
    0xea => 0x6e4a, 0xeb => 0x6e20, 0xec => 0x6e25, 0xed => 0x6e23,
    0xee => 0x6e1b, 0xef => 0x6e5b, 0xf0 => 0x6e58, 0xf1 => 0x6e24,
    0xf2 => 0x6e56, 0xf3 => 0x6e6e, 0xf4 => 0x6e2d, 0xf5 => 0x6e26,
    0xf6 => 0x6e6f, 0xf7 => 0x6e34, 0xf8 => 0x6e4d, 0xf9 => 0x6e3a,
    0xfa => 0x6e2c, 0xfb => 0x6e43, 0xfc => 0x6e1d, 0xfd => 0x6e3e,
    0xfe => 0x6ecb,
  },
  0xb5 => {
    0x40 => 0x6e89, 0x41 => 0x6e19, 0x42 => 0x6e4e, 0x43 => 0x6e63,
    0x44 => 0x6e44, 0x45 => 0x6e72, 0x46 => 0x6e69, 0x47 => 0x6e5f,
    0x48 => 0x7119, 0x49 => 0x711a, 0x4a => 0x7126, 0x4b => 0x7130,
    0x4c => 0x7121, 0x4d => 0x7136, 0x4e => 0x716e, 0x4f => 0x711c,
    0x50 => 0x724c, 0x51 => 0x7284, 0x52 => 0x7280, 0x53 => 0x7336,
    0x54 => 0x7325, 0x55 => 0x7334, 0x56 => 0x7329, 0x57 => 0x743a,
    0x58 => 0x742a, 0x59 => 0x7433, 0x5a => 0x7422, 0x5b => 0x7425,
    0x5c => 0x7435, 0x5d => 0x7436, 0x5e => 0x7434, 0x5f => 0x742f,
    0x60 => 0x741b, 0x61 => 0x7426, 0x62 => 0x7428, 0x63 => 0x7525,
    0x64 => 0x7526, 0x65 => 0x756b, 0x66 => 0x756a, 0x67 => 0x75e2,
    0x68 => 0x75db, 0x69 => 0x75e3, 0x6a => 0x75d9, 0x6b => 0x75d8,
    0x6c => 0x75de, 0x6d => 0x75e0, 0x6e => 0x767b, 0x6f => 0x767c,
    0x70 => 0x7696, 0x71 => 0x7693, 0x72 => 0x76b4, 0x73 => 0x76dc,
    0x74 => 0x774f, 0x75 => 0x77ed, 0x76 => 0x785d, 0x77 => 0x786c,
    0x78 => 0x786f, 0x79 => 0x7a0d, 0x7a => 0x7a08, 0x7b => 0x7a0b,
    0x7c => 0x7a05, 0x7d => 0x7a00, 0x7e => 0x7a98, 0xa1 => 0x7a97,
    0xa2 => 0x7a96, 0xa3 => 0x7ae5, 0xa4 => 0x7ae3, 0xa5 => 0x7b49,
    0xa6 => 0x7b56, 0xa7 => 0x7b46, 0xa8 => 0x7b50, 0xa9 => 0x7b52,
    0xaa => 0x7b54, 0xab => 0x7b4d, 0xac => 0x7b4b, 0xad => 0x7b4f,
    0xae => 0x7b51, 0xaf => 0x7c9f, 0xb0 => 0x7ca5, 0xb1 => 0x7d5e,
    0xb2 => 0x7d50, 0xb3 => 0x7d68, 0xb4 => 0x7d55, 0xb5 => 0x7d2b,
    0xb6 => 0x7d6e, 0xb7 => 0x7d72, 0xb8 => 0x7d61, 0xb9 => 0x7d66,
    0xba => 0x7d62, 0xbb => 0x7d70, 0xbc => 0x7d73, 0xbd => 0x5584,
    0xbe => 0x7fd4, 0xbf => 0x7fd5, 0xc0 => 0x800b, 0xc1 => 0x8052,
    0xc2 => 0x8085, 0xc3 => 0x8155, 0xc4 => 0x8154, 0xc5 => 0x814b,
    0xc6 => 0x8151, 0xc7 => 0x814e, 0xc8 => 0x8139, 0xc9 => 0x8146,
    0xca => 0x813e, 0xcb => 0x814c, 0xcc => 0x8153, 0xcd => 0x8174,
    0xce => 0x8212, 0xcf => 0x821c, 0xd0 => 0x83e9, 0xd1 => 0x8403,
    0xd2 => 0x83f8, 0xd3 => 0x840d, 0xd4 => 0x83e0, 0xd5 => 0x83c5,
    0xd6 => 0x840b, 0xd7 => 0x83c1, 0xd8 => 0x83ef, 0xd9 => 0x83f1,
    0xda => 0x83f4, 0xdb => 0x8457, 0xdc => 0x840a, 0xdd => 0x83f0,
    0xde => 0x840c, 0xdf => 0x83cc, 0xe0 => 0x83fd, 0xe1 => 0x83f2,
    0xe2 => 0x83ca, 0xe3 => 0x8438, 0xe4 => 0x840e, 0xe5 => 0x8404,
    0xe6 => 0x83dc, 0xe7 => 0x8407, 0xe8 => 0x83d4, 0xe9 => 0x83df,
    0xea => 0x865b, 0xeb => 0x86df, 0xec => 0x86d9, 0xed => 0x86ed,
    0xee => 0x86d4, 0xef => 0x86db, 0xf0 => 0x86e4, 0xf1 => 0x86d0,
    0xf2 => 0x86de, 0xf3 => 0x8857, 0xf4 => 0x88c1, 0xf5 => 0x88c2,
    0xf6 => 0x88b1, 0xf7 => 0x8983, 0xf8 => 0x8996, 0xf9 => 0x8a3b,
    0xfa => 0x8a60, 0xfb => 0x8a55, 0xfc => 0x8a5e, 0xfd => 0x8a3c,
    0xfe => 0x8a41,
  },
  0xb6 => {
    0x40 => 0x8a54, 0x41 => 0x8a5b, 0x42 => 0x8a50, 0x43 => 0x8a46,
    0x44 => 0x8a34, 0x45 => 0x8a3a, 0x46 => 0x8a36, 0x47 => 0x8a56,
    0x48 => 0x8c61, 0x49 => 0x8c82, 0x4a => 0x8caf, 0x4b => 0x8cbc,
    0x4c => 0x8cb3, 0x4d => 0x8cbd, 0x4e => 0x8cc1, 0x4f => 0x8cbb,
    0x50 => 0x8cc0, 0x51 => 0x8cb4, 0x52 => 0x8cb7, 0x53 => 0x8cb6,
    0x54 => 0x8cbf, 0x55 => 0x8cb8, 0x56 => 0x8d8a, 0x57 => 0x8d85,
    0x58 => 0x8d81, 0x59 => 0x8dce, 0x5a => 0x8ddd, 0x5b => 0x8dcb,
    0x5c => 0x8dda, 0x5d => 0x8dd1, 0x5e => 0x8dcc, 0x5f => 0x8ddb,
    0x60 => 0x8dc6, 0x61 => 0x8efb, 0x62 => 0x8ef8, 0x63 => 0x8efc,
    0x64 => 0x8f9c, 0x65 => 0x902e, 0x66 => 0x9035, 0x67 => 0x9031,
    0x68 => 0x9038, 0x69 => 0x9032, 0x6a => 0x9036, 0x6b => 0x9102,
    0x6c => 0x90f5, 0x6d => 0x9109, 0x6e => 0x90fe, 0x6f => 0x9163,
    0x70 => 0x9165, 0x71 => 0x91cf, 0x72 => 0x9214, 0x73 => 0x9215,
    0x74 => 0x9223, 0x75 => 0x9209, 0x76 => 0x921e, 0x77 => 0x920d,
    0x78 => 0x9210, 0x79 => 0x9207, 0x7a => 0x9211, 0x7b => 0x9594,
    0x7c => 0x958f, 0x7d => 0x958b, 0x7e => 0x9591, 0xa1 => 0x9593,
    0xa2 => 0x9592, 0xa3 => 0x958e, 0xa4 => 0x968a, 0xa5 => 0x968e,
    0xa6 => 0x968b, 0xa7 => 0x967d, 0xa8 => 0x9685, 0xa9 => 0x9686,
    0xaa => 0x968d, 0xab => 0x9672, 0xac => 0x9684, 0xad => 0x96c1,
    0xae => 0x96c5, 0xaf => 0x96c4, 0xb0 => 0x96c6, 0xb1 => 0x96c7,
    0xb2 => 0x96ef, 0xb3 => 0x96f2, 0xb4 => 0x97cc, 0xb5 => 0x9805,
    0xb6 => 0x9806, 0xb7 => 0x9808, 0xb8 => 0x98e7, 0xb9 => 0x98ea,
    0xba => 0x98ef, 0xbb => 0x98e9, 0xbc => 0x98f2, 0xbd => 0x98ed,
    0xbe => 0x99ae, 0xbf => 0x99ad, 0xc0 => 0x9ec3, 0xc1 => 0x9ecd,
    0xc2 => 0x9ed1, 0xc3 => 0x4e82, 0xc4 => 0x50ad, 0xc5 => 0x50b5,
    0xc6 => 0x50b2, 0xc7 => 0x50b3, 0xc8 => 0x50c5, 0xc9 => 0x50be,
    0xca => 0x50ac, 0xcb => 0x50b7, 0xcc => 0x50bb, 0xcd => 0x50af,
    0xce => 0x50c7, 0xcf => 0x527f, 0xd0 => 0x5277, 0xd1 => 0x527d,
    0xd2 => 0x52df, 0xd3 => 0x52e6, 0xd4 => 0x52e4, 0xd5 => 0x52e2,
    0xd6 => 0x52e3, 0xd7 => 0x532f, 0xd8 => 0x55df, 0xd9 => 0x55e8,
    0xda => 0x55d3, 0xdb => 0x55e6, 0xdc => 0x55ce, 0xdd => 0x55dc,
    0xde => 0x55c7, 0xdf => 0x55d1, 0xe0 => 0x55e3, 0xe1 => 0x55e4,
    0xe2 => 0x55ef, 0xe3 => 0x55da, 0xe4 => 0x55e1, 0xe5 => 0x55c5,
    0xe6 => 0x55c6, 0xe7 => 0x55e5, 0xe8 => 0x55c9, 0xe9 => 0x5712,
    0xea => 0x5713, 0xeb => 0x585e, 0xec => 0x5851, 0xed => 0x5858,
    0xee => 0x5857, 0xef => 0x585a, 0xf0 => 0x5854, 0xf1 => 0x586b,
    0xf2 => 0x584c, 0xf3 => 0x586d, 0xf4 => 0x584a, 0xf5 => 0x5862,
    0xf6 => 0x5852, 0xf7 => 0x584b, 0xf8 => 0x5967, 0xf9 => 0x5ac1,
    0xfa => 0x5ac9, 0xfb => 0x5acc, 0xfc => 0x5abe, 0xfd => 0x5abd,
    0xfe => 0x5abc,
  },
  0xb7 => {
    0x40 => 0x5ab3, 0x41 => 0x5ac2, 0x42 => 0x5ab2, 0x43 => 0x5d69,
    0x44 => 0x5d6f, 0x45 => 0x5e4c, 0x46 => 0x5e79, 0x47 => 0x5ec9,
    0x48 => 0x5ec8, 0x49 => 0x5f12, 0x4a => 0x5f59, 0x4b => 0x5fac,
    0x4c => 0x5fae, 0x4d => 0x611a, 0x4e => 0x610f, 0x4f => 0x6148,
    0x50 => 0x611f, 0x51 => 0x60f3, 0x52 => 0x611b, 0x53 => 0x60f9,
    0x54 => 0x6101, 0x55 => 0x6108, 0x56 => 0x614e, 0x57 => 0x614c,
    0x58 => 0x6144, 0x59 => 0x614d, 0x5a => 0x613e, 0x5b => 0x6134,
    0x5c => 0x6127, 0x5d => 0x610d, 0x5e => 0x6106, 0x5f => 0x6137,
    0x60 => 0x6221, 0x61 => 0x6222, 0x62 => 0x6413, 0x63 => 0x643e,
    0x64 => 0x641e, 0x65 => 0x642a, 0x66 => 0x642d, 0x67 => 0x643d,
    0x68 => 0x642c, 0x69 => 0x640f, 0x6a => 0x641c, 0x6b => 0x6414,
    0x6c => 0x640d, 0x6d => 0x6436, 0x6e => 0x6416, 0x6f => 0x6417,
    0x70 => 0x6406, 0x71 => 0x656c, 0x72 => 0x659f, 0x73 => 0x65b0,
    0x74 => 0x6697, 0x75 => 0x6689, 0x76 => 0x6687, 0x77 => 0x6688,
    0x78 => 0x6696, 0x79 => 0x6684, 0x7a => 0x6698, 0x7b => 0x668d,
    0x7c => 0x6703, 0x7d => 0x6994, 0x7e => 0x696d, 0xa1 => 0x695a,
    0xa2 => 0x6977, 0xa3 => 0x6960, 0xa4 => 0x6954, 0xa5 => 0x6975,
    0xa6 => 0x6930, 0xa7 => 0x6982, 0xa8 => 0x694a, 0xa9 => 0x6968,
    0xaa => 0x696b, 0xab => 0x695e, 0xac => 0x6953, 0xad => 0x6979,
    0xae => 0x6986, 0xaf => 0x695d, 0xb0 => 0x6963, 0xb1 => 0x695b,
    0xb2 => 0x6b47, 0xb3 => 0x6b72, 0xb4 => 0x6bc0, 0xb5 => 0x6bbf,
    0xb6 => 0x6bd3, 0xb7 => 0x6bfd, 0xb8 => 0x6ea2, 0xb9 => 0x6eaf,
    0xba => 0x6ed3, 0xbb => 0x6eb6, 0xbc => 0x6ec2, 0xbd => 0x6e90,
    0xbe => 0x6e9d, 0xbf => 0x6ec7, 0xc0 => 0x6ec5, 0xc1 => 0x6ea5,
    0xc2 => 0x6e98, 0xc3 => 0x6ebc, 0xc4 => 0x6eba, 0xc5 => 0x6eab,
    0xc6 => 0x6ed1, 0xc7 => 0x6e96, 0xc8 => 0x6e9c, 0xc9 => 0x6ec4,
    0xca => 0x6ed4, 0xcb => 0x6eaa, 0xcc => 0x6ea7, 0xcd => 0x6eb4,
    0xce => 0x714e, 0xcf => 0x7159, 0xd0 => 0x7169, 0xd1 => 0x7164,
    0xd2 => 0x7149, 0xd3 => 0x7167, 0xd4 => 0x715c, 0xd5 => 0x716c,
    0xd6 => 0x7166, 0xd7 => 0x714c, 0xd8 => 0x7165, 0xd9 => 0x715e,
    0xda => 0x7146, 0xdb => 0x7168, 0xdc => 0x7156, 0xdd => 0x723a,
    0xde => 0x7252, 0xdf => 0x7337, 0xe0 => 0x7345, 0xe1 => 0x733f,
    0xe2 => 0x733e, 0xe3 => 0x746f, 0xe4 => 0x745a, 0xe5 => 0x7455,
    0xe6 => 0x745f, 0xe7 => 0x745e, 0xe8 => 0x7441, 0xe9 => 0x743f,
    0xea => 0x7459, 0xeb => 0x745b, 0xec => 0x745c, 0xed => 0x7576,
    0xee => 0x7578, 0xef => 0x7600, 0xf0 => 0x75f0, 0xf1 => 0x7601,
    0xf2 => 0x75f2, 0xf3 => 0x75f1, 0xf4 => 0x75fa, 0xf5 => 0x75ff,
    0xf6 => 0x75f4, 0xf7 => 0x75f3, 0xf8 => 0x76de, 0xf9 => 0x76df,
    0xfa => 0x775b, 0xfb => 0x776b, 0xfc => 0x7766, 0xfd => 0x775e,
    0xfe => 0x7763,
  },
  0xb8 => {
    0x40 => 0x7779, 0x41 => 0x776a, 0x42 => 0x776c, 0x43 => 0x775c,
    0x44 => 0x7765, 0x45 => 0x7768, 0x46 => 0x7762, 0x47 => 0x77ee,
    0x48 => 0x788e, 0x49 => 0x78b0, 0x4a => 0x7897, 0x4b => 0x7898,
    0x4c => 0x788c, 0x4d => 0x7889, 0x4e => 0x787c, 0x4f => 0x7891,
    0x50 => 0x7893, 0x51 => 0x787f, 0x52 => 0x797a, 0x53 => 0x797f,
    0x54 => 0x7981, 0x55 => 0x842c, 0x56 => 0x79bd, 0x57 => 0x7a1c,
    0x58 => 0x7a1a, 0x59 => 0x7a20, 0x5a => 0x7a14, 0x5b => 0x7a1f,
    0x5c => 0x7a1e, 0x5d => 0x7a9f, 0x5e => 0x7aa0, 0x5f => 0x7b77,
    0x60 => 0x7bc0, 0x61 => 0x7b60, 0x62 => 0x7b6e, 0x63 => 0x7b67,
    0x64 => 0x7cb1, 0x65 => 0x7cb3, 0x66 => 0x7cb5, 0x67 => 0x7d93,
    0x68 => 0x7d79, 0x69 => 0x7d91, 0x6a => 0x7d81, 0x6b => 0x7d8f,
    0x6c => 0x7d5b, 0x6d => 0x7f6e, 0x6e => 0x7f69, 0x6f => 0x7f6a,
    0x70 => 0x7f72, 0x71 => 0x7fa9, 0x72 => 0x7fa8, 0x73 => 0x7fa4,
    0x74 => 0x8056, 0x75 => 0x8058, 0x76 => 0x8086, 0x77 => 0x8084,
    0x78 => 0x8171, 0x79 => 0x8170, 0x7a => 0x8178, 0x7b => 0x8165,
    0x7c => 0x816e, 0x7d => 0x8173, 0x7e => 0x816b, 0xa1 => 0x8179,
    0xa2 => 0x817a, 0xa3 => 0x8166, 0xa4 => 0x8205, 0xa5 => 0x8247,
    0xa6 => 0x8482, 0xa7 => 0x8477, 0xa8 => 0x843d, 0xa9 => 0x8431,
    0xaa => 0x8475, 0xab => 0x8466, 0xac => 0x846b, 0xad => 0x8449,
    0xae => 0x846c, 0xaf => 0x845b, 0xb0 => 0x843c, 0xb1 => 0x8435,
    0xb2 => 0x8461, 0xb3 => 0x8463, 0xb4 => 0x8469, 0xb5 => 0x846d,
    0xb6 => 0x8446, 0xb7 => 0x865e, 0xb8 => 0x865c, 0xb9 => 0x865f,
    0xba => 0x86f9, 0xbb => 0x8713, 0xbc => 0x8708, 0xbd => 0x8707,
    0xbe => 0x8700, 0xbf => 0x86fe, 0xc0 => 0x86fb, 0xc1 => 0x8702,
    0xc2 => 0x8703, 0xc3 => 0x8706, 0xc4 => 0x870a, 0xc5 => 0x8859,
    0xc6 => 0x88df, 0xc7 => 0x88d4, 0xc8 => 0x88d9, 0xc9 => 0x88dc,
    0xca => 0x88d8, 0xcb => 0x88dd, 0xcc => 0x88e1, 0xcd => 0x88ca,
    0xce => 0x88d5, 0xcf => 0x88d2, 0xd0 => 0x899c, 0xd1 => 0x89e3,
    0xd2 => 0x8a6b, 0xd3 => 0x8a72, 0xd4 => 0x8a73, 0xd5 => 0x8a66,
    0xd6 => 0x8a69, 0xd7 => 0x8a70, 0xd8 => 0x8a87, 0xd9 => 0x8a7c,
    0xda => 0x8a63, 0xdb => 0x8aa0, 0xdc => 0x8a71, 0xdd => 0x8a85,
    0xde => 0x8a6d, 0xdf => 0x8a62, 0xe0 => 0x8a6e, 0xe1 => 0x8a6c,
    0xe2 => 0x8a79, 0xe3 => 0x8a7b, 0xe4 => 0x8a3e, 0xe5 => 0x8a68,
    0xe6 => 0x8c62, 0xe7 => 0x8c8a, 0xe8 => 0x8c89, 0xe9 => 0x8cca,
    0xea => 0x8cc7, 0xeb => 0x8cc8, 0xec => 0x8cc4, 0xed => 0x8cb2,
    0xee => 0x8cc3, 0xef => 0x8cc2, 0xf0 => 0x8cc5, 0xf1 => 0x8de1,
    0xf2 => 0x8ddf, 0xf3 => 0x8de8, 0xf4 => 0x8def, 0xf5 => 0x8df3,
    0xf6 => 0x8dfa, 0xf7 => 0x8dea, 0xf8 => 0x8de4, 0xf9 => 0x8de6,
    0xfa => 0x8eb2, 0xfb => 0x8f03, 0xfc => 0x8f09, 0xfd => 0x8efe,
    0xfe => 0x8f0a,
  },
  0xb9 => {
    0x40 => 0x8f9f, 0x41 => 0x8fb2, 0x42 => 0x904b, 0x43 => 0x904a,
    0x44 => 0x9053, 0x45 => 0x9042, 0x46 => 0x9054, 0x47 => 0x903c,
    0x48 => 0x9055, 0x49 => 0x9050, 0x4a => 0x9047, 0x4b => 0x904f,
    0x4c => 0x904e, 0x4d => 0x904d, 0x4e => 0x9051, 0x4f => 0x903e,
    0x50 => 0x9041, 0x51 => 0x9112, 0x52 => 0x9117, 0x53 => 0x916c,
    0x54 => 0x916a, 0x55 => 0x9169, 0x56 => 0x91c9, 0x57 => 0x9237,
    0x58 => 0x9257, 0x59 => 0x9238, 0x5a => 0x923d, 0x5b => 0x9240,
    0x5c => 0x923e, 0x5d => 0x925b, 0x5e => 0x924b, 0x5f => 0x9264,
    0x60 => 0x9251, 0x61 => 0x9234, 0x62 => 0x9249, 0x63 => 0x924d,
    0x64 => 0x9245, 0x65 => 0x9239, 0x66 => 0x923f, 0x67 => 0x925a,
    0x68 => 0x9598, 0x69 => 0x9698, 0x6a => 0x9694, 0x6b => 0x9695,
    0x6c => 0x96cd, 0x6d => 0x96cb, 0x6e => 0x96c9, 0x6f => 0x96ca,
    0x70 => 0x96f7, 0x71 => 0x96fb, 0x72 => 0x96f9, 0x73 => 0x96f6,
    0x74 => 0x9756, 0x75 => 0x9774, 0x76 => 0x9776, 0x77 => 0x9810,
    0x78 => 0x9811, 0x79 => 0x9813, 0x7a => 0x980a, 0x7b => 0x9812,
    0x7c => 0x980c, 0x7d => 0x98fc, 0x7e => 0x98f4, 0xa1 => 0x98fd,
    0xa2 => 0x98fe, 0xa3 => 0x99b3, 0xa4 => 0x99b1, 0xa5 => 0x99b4,
    0xa6 => 0x9ae1, 0xa7 => 0x9ce9, 0xa8 => 0x9e82, 0xa9 => 0x9f0e,
    0xaa => 0x9f13, 0xab => 0x9f20, 0xac => 0x50e7, 0xad => 0x50ee,
    0xae => 0x50e5, 0xaf => 0x50d6, 0xb0 => 0x50ed, 0xb1 => 0x50da,
    0xb2 => 0x50d5, 0xb3 => 0x50cf, 0xb4 => 0x50d1, 0xb5 => 0x50f1,
    0xb6 => 0x50ce, 0xb7 => 0x50e9, 0xb8 => 0x5162, 0xb9 => 0x51f3,
    0xba => 0x5283, 0xbb => 0x5282, 0xbc => 0x5331, 0xbd => 0x53ad,
    0xbe => 0x55fe, 0xbf => 0x5600, 0xc0 => 0x561b, 0xc1 => 0x5617,
    0xc2 => 0x55fd, 0xc3 => 0x5614, 0xc4 => 0x5606, 0xc5 => 0x5609,
    0xc6 => 0x560d, 0xc7 => 0x560e, 0xc8 => 0x55f7, 0xc9 => 0x5616,
    0xca => 0x561f, 0xcb => 0x5608, 0xcc => 0x5610, 0xcd => 0x55f6,
    0xce => 0x5718, 0xcf => 0x5716, 0xd0 => 0x5875, 0xd1 => 0x587e,
    0xd2 => 0x5883, 0xd3 => 0x5893, 0xd4 => 0x588a, 0xd5 => 0x5879,
    0xd6 => 0x5885, 0xd7 => 0x587d, 0xd8 => 0x58fd, 0xd9 => 0x5925,
    0xda => 0x5922, 0xdb => 0x5924, 0xdc => 0x596a, 0xdd => 0x5969,
    0xde => 0x5ae1, 0xdf => 0x5ae6, 0xe0 => 0x5ae9, 0xe1 => 0x5ad7,
    0xe2 => 0x5ad6, 0xe3 => 0x5ad8, 0xe4 => 0x5ae3, 0xe5 => 0x5b75,
    0xe6 => 0x5bde, 0xe7 => 0x5be7, 0xe8 => 0x5be1, 0xe9 => 0x5be5,
    0xea => 0x5be6, 0xeb => 0x5be8, 0xec => 0x5be2, 0xed => 0x5be4,
    0xee => 0x5bdf, 0xef => 0x5c0d, 0xf0 => 0x5c62, 0xf1 => 0x5d84,
    0xf2 => 0x5d87, 0xf3 => 0x5e5b, 0xf4 => 0x5e63, 0xf5 => 0x5e55,
    0xf6 => 0x5e57, 0xf7 => 0x5e54, 0xf8 => 0x5ed3, 0xf9 => 0x5ed6,
    0xfa => 0x5f0a, 0xfb => 0x5f46, 0xfc => 0x5f70, 0xfd => 0x5fb9,
    0xfe => 0x6147,
  },
  0xba => {
    0x40 => 0x613f, 0x41 => 0x614b, 0x42 => 0x6177, 0x43 => 0x6162,
    0x44 => 0x6163, 0x45 => 0x615f, 0x46 => 0x615a, 0x47 => 0x6158,
    0x48 => 0x6175, 0x49 => 0x622a, 0x4a => 0x6487, 0x4b => 0x6458,
    0x4c => 0x6454, 0x4d => 0x64a4, 0x4e => 0x6478, 0x4f => 0x645f,
    0x50 => 0x647a, 0x51 => 0x6451, 0x52 => 0x6467, 0x53 => 0x6434,
    0x54 => 0x646d, 0x55 => 0x647b, 0x56 => 0x6572, 0x57 => 0x65a1,
    0x58 => 0x65d7, 0x59 => 0x65d6, 0x5a => 0x66a2, 0x5b => 0x66a8,
    0x5c => 0x669d, 0x5d => 0x699c, 0x5e => 0x69a8, 0x5f => 0x6995,
    0x60 => 0x69c1, 0x61 => 0x69ae, 0x62 => 0x69d3, 0x63 => 0x69cb,
    0x64 => 0x699b, 0x65 => 0x69b7, 0x66 => 0x69bb, 0x67 => 0x69ab,
    0x68 => 0x69b4, 0x69 => 0x69d0, 0x6a => 0x69cd, 0x6b => 0x69ad,
    0x6c => 0x69cc, 0x6d => 0x69a6, 0x6e => 0x69c3, 0x6f => 0x69a3,
    0x70 => 0x6b49, 0x71 => 0x6b4c, 0x72 => 0x6c33, 0x73 => 0x6f33,
    0x74 => 0x6f14, 0x75 => 0x6efe, 0x76 => 0x6f13, 0x77 => 0x6ef4,
    0x78 => 0x6f29, 0x79 => 0x6f3e, 0x7a => 0x6f20, 0x7b => 0x6f2c,
    0x7c => 0x6f0f, 0x7d => 0x6f02, 0x7e => 0x6f22, 0xa1 => 0x6eff,
    0xa2 => 0x6eef, 0xa3 => 0x6f06, 0xa4 => 0x6f31, 0xa5 => 0x6f38,
    0xa6 => 0x6f32, 0xa7 => 0x6f23, 0xa8 => 0x6f15, 0xa9 => 0x6f2b,
    0xaa => 0x6f2f, 0xab => 0x6f88, 0xac => 0x6f2a, 0xad => 0x6eec,
    0xae => 0x6f01, 0xaf => 0x6ef2, 0xb0 => 0x6ecc, 0xb1 => 0x6ef7,
    0xb2 => 0x7194, 0xb3 => 0x7199, 0xb4 => 0x717d, 0xb5 => 0x718a,
    0xb6 => 0x7184, 0xb7 => 0x7192, 0xb8 => 0x723e, 0xb9 => 0x7292,
    0xba => 0x7296, 0xbb => 0x7344, 0xbc => 0x7350, 0xbd => 0x7464,
    0xbe => 0x7463, 0xbf => 0x746a, 0xc0 => 0x7470, 0xc1 => 0x746d,
    0xc2 => 0x7504, 0xc3 => 0x7591, 0xc4 => 0x7627, 0xc5 => 0x760d,
    0xc6 => 0x760b, 0xc7 => 0x7609, 0xc8 => 0x7613, 0xc9 => 0x76e1,
    0xca => 0x76e3, 0xcb => 0x7784, 0xcc => 0x777d, 0xcd => 0x777f,
    0xce => 0x7761, 0xcf => 0x78c1, 0xd0 => 0x789f, 0xd1 => 0x78a7,
    0xd2 => 0x78b3, 0xd3 => 0x78a9, 0xd4 => 0x78a3, 0xd5 => 0x798e,
    0xd6 => 0x798f, 0xd7 => 0x798d, 0xd8 => 0x7a2e, 0xd9 => 0x7a31,
    0xda => 0x7aaa, 0xdb => 0x7aa9, 0xdc => 0x7aed, 0xdd => 0x7aef,
    0xde => 0x7ba1, 0xdf => 0x7b95, 0xe0 => 0x7b8b, 0xe1 => 0x7b75,
    0xe2 => 0x7b97, 0xe3 => 0x7b9d, 0xe4 => 0x7b94, 0xe5 => 0x7b8f,
    0xe6 => 0x7bb8, 0xe7 => 0x7b87, 0xe8 => 0x7b84, 0xe9 => 0x7cb9,
    0xea => 0x7cbd, 0xeb => 0x7cbe, 0xec => 0x7dbb, 0xed => 0x7db0,
    0xee => 0x7d9c, 0xef => 0x7dbd, 0xf0 => 0x7dbe, 0xf1 => 0x7da0,
    0xf2 => 0x7dca, 0xf3 => 0x7db4, 0xf4 => 0x7db2, 0xf5 => 0x7db1,
    0xf6 => 0x7dba, 0xf7 => 0x7da2, 0xf8 => 0x7dbf, 0xf9 => 0x7db5,
    0xfa => 0x7db8, 0xfb => 0x7dad, 0xfc => 0x7dd2, 0xfd => 0x7dc7,
    0xfe => 0x7dac,
  },
  0xbb => {
    0x40 => 0x7f70, 0x41 => 0x7fe0, 0x42 => 0x7fe1, 0x43 => 0x7fdf,
    0x44 => 0x805e, 0x45 => 0x805a, 0x46 => 0x8087, 0x47 => 0x8150,
    0x48 => 0x8180, 0x49 => 0x818f, 0x4a => 0x8188, 0x4b => 0x818a,
    0x4c => 0x817f, 0x4d => 0x8182, 0x4e => 0x81e7, 0x4f => 0x81fa,
    0x50 => 0x8207, 0x51 => 0x8214, 0x52 => 0x821e, 0x53 => 0x824b,
    0x54 => 0x84c9, 0x55 => 0x84bf, 0x56 => 0x84c6, 0x57 => 0x84c4,
    0x58 => 0x8499, 0x59 => 0x849e, 0x5a => 0x84b2, 0x5b => 0x849c,
    0x5c => 0x84cb, 0x5d => 0x84b8, 0x5e => 0x84c0, 0x5f => 0x84d3,
    0x60 => 0x8490, 0x61 => 0x84bc, 0x62 => 0x84d1, 0x63 => 0x84ca,
    0x64 => 0x873f, 0x65 => 0x871c, 0x66 => 0x873b, 0x67 => 0x8722,
    0x68 => 0x8725, 0x69 => 0x8734, 0x6a => 0x8718, 0x6b => 0x8755,
    0x6c => 0x8737, 0x6d => 0x8729, 0x6e => 0x88f3, 0x6f => 0x8902,
    0x70 => 0x88f4, 0x71 => 0x88f9, 0x72 => 0x88f8, 0x73 => 0x88fd,
    0x74 => 0x88e8, 0x75 => 0x891a, 0x76 => 0x88ef, 0x77 => 0x8aa6,
    0x78 => 0x8a8c, 0x79 => 0x8a9e, 0x7a => 0x8aa3, 0x7b => 0x8a8d,
    0x7c => 0x8aa1, 0x7d => 0x8a93, 0x7e => 0x8aa4, 0xa1 => 0x8aaa,
    0xa2 => 0x8aa5, 0xa3 => 0x8aa8, 0xa4 => 0x8a98, 0xa5 => 0x8a91,
    0xa6 => 0x8a9a, 0xa7 => 0x8aa7, 0xa8 => 0x8c6a, 0xa9 => 0x8c8d,
    0xaa => 0x8c8c, 0xab => 0x8cd3, 0xac => 0x8cd1, 0xad => 0x8cd2,
    0xae => 0x8d6b, 0xaf => 0x8d99, 0xb0 => 0x8d95, 0xb1 => 0x8dfc,
    0xb2 => 0x8f14, 0xb3 => 0x8f12, 0xb4 => 0x8f15, 0xb5 => 0x8f13,
    0xb6 => 0x8fa3, 0xb7 => 0x9060, 0xb8 => 0x9058, 0xb9 => 0x905c,
    0xba => 0x9063, 0xbb => 0x9059, 0xbc => 0x905e, 0xbd => 0x9062,
    0xbe => 0x905d, 0xbf => 0x905b, 0xc0 => 0x9119, 0xc1 => 0x9118,
    0xc2 => 0x911e, 0xc3 => 0x9175, 0xc4 => 0x9178, 0xc5 => 0x9177,
    0xc6 => 0x9174, 0xc7 => 0x9278, 0xc8 => 0x9280, 0xc9 => 0x9285,
    0xca => 0x9298, 0xcb => 0x9296, 0xcc => 0x927b, 0xcd => 0x9293,
    0xce => 0x929c, 0xcf => 0x92a8, 0xd0 => 0x927c, 0xd1 => 0x9291,
    0xd2 => 0x95a1, 0xd3 => 0x95a8, 0xd4 => 0x95a9, 0xd5 => 0x95a3,
    0xd6 => 0x95a5, 0xd7 => 0x95a4, 0xd8 => 0x9699, 0xd9 => 0x969c,
    0xda => 0x969b, 0xdb => 0x96cc, 0xdc => 0x96d2, 0xdd => 0x9700,
    0xde => 0x977c, 0xdf => 0x9785, 0xe0 => 0x97f6, 0xe1 => 0x9817,
    0xe2 => 0x9818, 0xe3 => 0x98af, 0xe4 => 0x98b1, 0xe5 => 0x9903,
    0xe6 => 0x9905, 0xe7 => 0x990c, 0xe8 => 0x9909, 0xe9 => 0x99c1,
    0xea => 0x9aaf, 0xeb => 0x9ab0, 0xec => 0x9ae6, 0xed => 0x9b41,
    0xee => 0x9b42, 0xef => 0x9cf4, 0xf0 => 0x9cf6, 0xf1 => 0x9cf3,
    0xf2 => 0x9ebc, 0xf3 => 0x9f3b, 0xf4 => 0x9f4a, 0xf5 => 0x5104,
    0xf6 => 0x5100, 0xf7 => 0x50fb, 0xf8 => 0x50f5, 0xf9 => 0x50f9,
    0xfa => 0x5102, 0xfb => 0x5108, 0xfc => 0x5109, 0xfd => 0x5105,
    0xfe => 0x51dc,
  },
  0xbc => {
    0x40 => 0x5287, 0x41 => 0x5288, 0x42 => 0x5289, 0x43 => 0x528d,
    0x44 => 0x528a, 0x45 => 0x52f0, 0x46 => 0x53b2, 0x47 => 0x562e,
    0x48 => 0x563b, 0x49 => 0x5639, 0x4a => 0x5632, 0x4b => 0x563f,
    0x4c => 0x5634, 0x4d => 0x5629, 0x4e => 0x5653, 0x4f => 0x564e,
    0x50 => 0x5657, 0x51 => 0x5674, 0x52 => 0x5636, 0x53 => 0x562f,
    0x54 => 0x5630, 0x55 => 0x5880, 0x56 => 0x589f, 0x57 => 0x589e,
    0x58 => 0x58b3, 0x59 => 0x589c, 0x5a => 0x58ae, 0x5b => 0x58a9,
    0x5c => 0x58a6, 0x5d => 0x596d, 0x5e => 0x5b09, 0x5f => 0x5afb,
    0x60 => 0x5b0b, 0x61 => 0x5af5, 0x62 => 0x5b0c, 0x63 => 0x5b08,
    0x64 => 0x5bee, 0x65 => 0x5bec, 0x66 => 0x5be9, 0x67 => 0x5beb,
    0x68 => 0x5c64, 0x69 => 0x5c65, 0x6a => 0x5d9d, 0x6b => 0x5d94,
    0x6c => 0x5e62, 0x6d => 0x5e5f, 0x6e => 0x5e61, 0x6f => 0x5ee2,
    0x70 => 0x5eda, 0x71 => 0x5edf, 0x72 => 0x5edd, 0x73 => 0x5ee3,
    0x74 => 0x5ee0, 0x75 => 0x5f48, 0x76 => 0x5f71, 0x77 => 0x5fb7,
    0x78 => 0x5fb5, 0x79 => 0x6176, 0x7a => 0x6167, 0x7b => 0x616e,
    0x7c => 0x615d, 0x7d => 0x6155, 0x7e => 0x6182, 0xa1 => 0x617c,
    0xa2 => 0x6170, 0xa3 => 0x616b, 0xa4 => 0x617e, 0xa5 => 0x61a7,
    0xa6 => 0x6190, 0xa7 => 0x61ab, 0xa8 => 0x618e, 0xa9 => 0x61ac,
    0xaa => 0x619a, 0xab => 0x61a4, 0xac => 0x6194, 0xad => 0x61ae,
    0xae => 0x622e, 0xaf => 0x6469, 0xb0 => 0x646f, 0xb1 => 0x6479,
    0xb2 => 0x649e, 0xb3 => 0x64b2, 0xb4 => 0x6488, 0xb5 => 0x6490,
    0xb6 => 0x64b0, 0xb7 => 0x64a5, 0xb8 => 0x6493, 0xb9 => 0x6495,
    0xba => 0x64a9, 0xbb => 0x6492, 0xbc => 0x64ae, 0xbd => 0x64ad,
    0xbe => 0x64ab, 0xbf => 0x649a, 0xc0 => 0x64ac, 0xc1 => 0x6499,
    0xc2 => 0x64a2, 0xc3 => 0x64b3, 0xc4 => 0x6575, 0xc5 => 0x6577,
    0xc6 => 0x6578, 0xc7 => 0x66ae, 0xc8 => 0x66ab, 0xc9 => 0x66b4,
    0xca => 0x66b1, 0xcb => 0x6a23, 0xcc => 0x6a1f, 0xcd => 0x69e8,
    0xce => 0x6a01, 0xcf => 0x6a1e, 0xd0 => 0x6a19, 0xd1 => 0x69fd,
    0xd2 => 0x6a21, 0xd3 => 0x6a13, 0xd4 => 0x6a0a, 0xd5 => 0x69f3,
    0xd6 => 0x6a02, 0xd7 => 0x6a05, 0xd8 => 0x69ed, 0xd9 => 0x6a11,
    0xda => 0x6b50, 0xdb => 0x6b4e, 0xdc => 0x6ba4, 0xdd => 0x6bc5,
    0xde => 0x6bc6, 0xdf => 0x6f3f, 0xe0 => 0x6f7c, 0xe1 => 0x6f84,
    0xe2 => 0x6f51, 0xe3 => 0x6f66, 0xe4 => 0x6f54, 0xe5 => 0x6f86,
    0xe6 => 0x6f6d, 0xe7 => 0x6f5b, 0xe8 => 0x6f78, 0xe9 => 0x6f6e,
    0xea => 0x6f8e, 0xeb => 0x6f7a, 0xec => 0x6f70, 0xed => 0x6f64,
    0xee => 0x6f97, 0xef => 0x6f58, 0xf0 => 0x6ed5, 0xf1 => 0x6f6f,
    0xf2 => 0x6f60, 0xf3 => 0x6f5f, 0xf4 => 0x719f, 0xf5 => 0x71ac,
    0xf6 => 0x71b1, 0xf7 => 0x71a8, 0xf8 => 0x7256, 0xf9 => 0x729b,
    0xfa => 0x734e, 0xfb => 0x7357, 0xfc => 0x7469, 0xfd => 0x748b,
    0xfe => 0x7483,
  },
  0xbd => {
    0x40 => 0x747e, 0x41 => 0x7480, 0x42 => 0x757f, 0x43 => 0x7620,
    0x44 => 0x7629, 0x45 => 0x761f, 0x46 => 0x7624, 0x47 => 0x7626,
    0x48 => 0x7621, 0x49 => 0x7622, 0x4a => 0x769a, 0x4b => 0x76ba,
    0x4c => 0x76e4, 0x4d => 0x778e, 0x4e => 0x7787, 0x4f => 0x778c,
    0x50 => 0x7791, 0x51 => 0x778b, 0x52 => 0x78cb, 0x53 => 0x78c5,
    0x54 => 0x78ba, 0x55 => 0x78ca, 0x56 => 0x78be, 0x57 => 0x78d5,
    0x58 => 0x78bc, 0x59 => 0x78d0, 0x5a => 0x7a3f, 0x5b => 0x7a3c,
    0x5c => 0x7a40, 0x5d => 0x7a3d, 0x5e => 0x7a37, 0x5f => 0x7a3b,
    0x60 => 0x7aaf, 0x61 => 0x7aae, 0x62 => 0x7bad, 0x63 => 0x7bb1,
    0x64 => 0x7bc4, 0x65 => 0x7bb4, 0x66 => 0x7bc6, 0x67 => 0x7bc7,
    0x68 => 0x7bc1, 0x69 => 0x7ba0, 0x6a => 0x7bcc, 0x6b => 0x7cca,
    0x6c => 0x7de0, 0x6d => 0x7df4, 0x6e => 0x7def, 0x6f => 0x7dfb,
    0x70 => 0x7dd8, 0x71 => 0x7dec, 0x72 => 0x7ddd, 0x73 => 0x7de8,
    0x74 => 0x7de3, 0x75 => 0x7dda, 0x76 => 0x7dde, 0x77 => 0x7de9,
    0x78 => 0x7d9e, 0x79 => 0x7dd9, 0x7a => 0x7df2, 0x7b => 0x7df9,
    0x7c => 0x7f75, 0x7d => 0x7f77, 0x7e => 0x7faf, 0xa1 => 0x7fe9,
    0xa2 => 0x8026, 0xa3 => 0x819b, 0xa4 => 0x819c, 0xa5 => 0x819d,
    0xa6 => 0x81a0, 0xa7 => 0x819a, 0xa8 => 0x8198, 0xa9 => 0x8517,
    0xaa => 0x853d, 0xab => 0x851a, 0xac => 0x84ee, 0xad => 0x852c,
    0xae => 0x852d, 0xaf => 0x8513, 0xb0 => 0x8511, 0xb1 => 0x8523,
    0xb2 => 0x8521, 0xb3 => 0x8514, 0xb4 => 0x84ec, 0xb5 => 0x8525,
    0xb6 => 0x84ff, 0xb7 => 0x8506, 0xb8 => 0x8782, 0xb9 => 0x8774,
    0xba => 0x8776, 0xbb => 0x8760, 0xbc => 0x8766, 0xbd => 0x8778,
    0xbe => 0x8768, 0xbf => 0x8759, 0xc0 => 0x8757, 0xc1 => 0x874c,
    0xc2 => 0x8753, 0xc3 => 0x885b, 0xc4 => 0x885d, 0xc5 => 0x8910,
    0xc6 => 0x8907, 0xc7 => 0x8912, 0xc8 => 0x8913, 0xc9 => 0x8915,
    0xca => 0x890a, 0xcb => 0x8abc, 0xcc => 0x8ad2, 0xcd => 0x8ac7,
    0xce => 0x8ac4, 0xcf => 0x8a95, 0xd0 => 0x8acb, 0xd1 => 0x8af8,
    0xd2 => 0x8ab2, 0xd3 => 0x8ac9, 0xd4 => 0x8ac2, 0xd5 => 0x8abf,
    0xd6 => 0x8ab0, 0xd7 => 0x8ad6, 0xd8 => 0x8acd, 0xd9 => 0x8ab6,
    0xda => 0x8ab9, 0xdb => 0x8adb, 0xdc => 0x8c4c, 0xdd => 0x8c4e,
    0xde => 0x8c6c, 0xdf => 0x8ce0, 0xe0 => 0x8cde, 0xe1 => 0x8ce6,
    0xe2 => 0x8ce4, 0xe3 => 0x8cec, 0xe4 => 0x8ced, 0xe5 => 0x8ce2,
    0xe6 => 0x8ce3, 0xe7 => 0x8cdc, 0xe8 => 0x8cea, 0xe9 => 0x8ce1,
    0xea => 0x8d6d, 0xeb => 0x8d9f, 0xec => 0x8da3, 0xed => 0x8e2b,
    0xee => 0x8e10, 0xef => 0x8e1d, 0xf0 => 0x8e22, 0xf1 => 0x8e0f,
    0xf2 => 0x8e29, 0xf3 => 0x8e1f, 0xf4 => 0x8e21, 0xf5 => 0x8e1e,
    0xf6 => 0x8eba, 0xf7 => 0x8f1d, 0xf8 => 0x8f1b, 0xf9 => 0x8f1f,
    0xfa => 0x8f29, 0xfb => 0x8f26, 0xfc => 0x8f2a, 0xfd => 0x8f1c,
    0xfe => 0x8f1e,
  },
  0xbe => {
    0x40 => 0x8f25, 0x41 => 0x9069, 0x42 => 0x906e, 0x43 => 0x9068,
    0x44 => 0x906d, 0x45 => 0x9077, 0x46 => 0x9130, 0x47 => 0x912d,
    0x48 => 0x9127, 0x49 => 0x9131, 0x4a => 0x9187, 0x4b => 0x9189,
    0x4c => 0x918b, 0x4d => 0x9183, 0x4e => 0x92c5, 0x4f => 0x92bb,
    0x50 => 0x92b7, 0x51 => 0x92ea, 0x52 => 0x92ac, 0x53 => 0x92e4,
    0x54 => 0x92c1, 0x55 => 0x92b3, 0x56 => 0x92bc, 0x57 => 0x92d2,
    0x58 => 0x92c7, 0x59 => 0x92f0, 0x5a => 0x92b2, 0x5b => 0x95ad,
    0x5c => 0x95b1, 0x5d => 0x9704, 0x5e => 0x9706, 0x5f => 0x9707,
    0x60 => 0x9709, 0x61 => 0x9760, 0x62 => 0x978d, 0x63 => 0x978b,
    0x64 => 0x978f, 0x65 => 0x9821, 0x66 => 0x982b, 0x67 => 0x981c,
    0x68 => 0x98b3, 0x69 => 0x990a, 0x6a => 0x9913, 0x6b => 0x9912,
    0x6c => 0x9918, 0x6d => 0x99dd, 0x6e => 0x99d0, 0x6f => 0x99df,
    0x70 => 0x99db, 0x71 => 0x99d1, 0x72 => 0x99d5, 0x73 => 0x99d2,
    0x74 => 0x99d9, 0x75 => 0x9ab7, 0x76 => 0x9aee, 0x77 => 0x9aef,
    0x78 => 0x9b27, 0x79 => 0x9b45, 0x7a => 0x9b44, 0x7b => 0x9b77,
    0x7c => 0x9b6f, 0x7d => 0x9d06, 0x7e => 0x9d09, 0xa1 => 0x9d03,
    0xa2 => 0x9ea9, 0xa3 => 0x9ebe, 0xa4 => 0x9ece, 0xa5 => 0x58a8,
    0xa6 => 0x9f52, 0xa7 => 0x5112, 0xa8 => 0x5118, 0xa9 => 0x5114,
    0xaa => 0x5110, 0xab => 0x5115, 0xac => 0x5180, 0xad => 0x51aa,
    0xae => 0x51dd, 0xaf => 0x5291, 0xb0 => 0x5293, 0xb1 => 0x52f3,
    0xb2 => 0x5659, 0xb3 => 0x566b, 0xb4 => 0x5679, 0xb5 => 0x5669,
    0xb6 => 0x5664, 0xb7 => 0x5678, 0xb8 => 0x566a, 0xb9 => 0x5668,
    0xba => 0x5665, 0xbb => 0x5671, 0xbc => 0x566f, 0xbd => 0x566c,
    0xbe => 0x5662, 0xbf => 0x5676, 0xc0 => 0x58c1, 0xc1 => 0x58be,
    0xc2 => 0x58c7, 0xc3 => 0x58c5, 0xc4 => 0x596e, 0xc5 => 0x5b1d,
    0xc6 => 0x5b34, 0xc7 => 0x5b78, 0xc8 => 0x5bf0, 0xc9 => 0x5c0e,
    0xca => 0x5f4a, 0xcb => 0x61b2, 0xcc => 0x6191, 0xcd => 0x61a9,
    0xce => 0x618a, 0xcf => 0x61cd, 0xd0 => 0x61b6, 0xd1 => 0x61be,
    0xd2 => 0x61ca, 0xd3 => 0x61c8, 0xd4 => 0x6230, 0xd5 => 0x64c5,
    0xd6 => 0x64c1, 0xd7 => 0x64cb, 0xd8 => 0x64bb, 0xd9 => 0x64bc,
    0xda => 0x64da, 0xdb => 0x64c4, 0xdc => 0x64c7, 0xdd => 0x64c2,
    0xde => 0x64cd, 0xdf => 0x64bf, 0xe0 => 0x64d2, 0xe1 => 0x64d4,
    0xe2 => 0x64be, 0xe3 => 0x6574, 0xe4 => 0x66c6, 0xe5 => 0x66c9,
    0xe6 => 0x66b9, 0xe7 => 0x66c4, 0xe8 => 0x66c7, 0xe9 => 0x66b8,
    0xea => 0x6a3d, 0xeb => 0x6a38, 0xec => 0x6a3a, 0xed => 0x6a59,
    0xee => 0x6a6b, 0xef => 0x6a58, 0xf0 => 0x6a39, 0xf1 => 0x6a44,
    0xf2 => 0x6a62, 0xf3 => 0x6a61, 0xf4 => 0x6a4b, 0xf5 => 0x6a47,
    0xf6 => 0x6a35, 0xf7 => 0x6a5f, 0xf8 => 0x6a48, 0xf9 => 0x6b59,
    0xfa => 0x6b77, 0xfb => 0x6c05, 0xfc => 0x6fc2, 0xfd => 0x6fb1,
    0xfe => 0x6fa1,
  },
  0xbf => {
    0x40 => 0x6fc3, 0x41 => 0x6fa4, 0x42 => 0x6fc1, 0x43 => 0x6fa7,
    0x44 => 0x6fb3, 0x45 => 0x6fc0, 0x46 => 0x6fb9, 0x47 => 0x6fb6,
    0x48 => 0x6fa6, 0x49 => 0x6fa0, 0x4a => 0x6fb4, 0x4b => 0x71be,
    0x4c => 0x71c9, 0x4d => 0x71d0, 0x4e => 0x71d2, 0x4f => 0x71c8,
    0x50 => 0x71d5, 0x51 => 0x71b9, 0x52 => 0x71ce, 0x53 => 0x71d9,
    0x54 => 0x71dc, 0x55 => 0x71c3, 0x56 => 0x71c4, 0x57 => 0x7368,
    0x58 => 0x749c, 0x59 => 0x74a3, 0x5a => 0x7498, 0x5b => 0x749f,
    0x5c => 0x749e, 0x5d => 0x74e2, 0x5e => 0x750c, 0x5f => 0x750d,
    0x60 => 0x7634, 0x61 => 0x7638, 0x62 => 0x763a, 0x63 => 0x76e7,
    0x64 => 0x76e5, 0x65 => 0x77a0, 0x66 => 0x779e, 0x67 => 0x779f,
    0x68 => 0x77a5, 0x69 => 0x78e8, 0x6a => 0x78da, 0x6b => 0x78ec,
    0x6c => 0x78e7, 0x6d => 0x79a6, 0x6e => 0x7a4d, 0x6f => 0x7a4e,
    0x70 => 0x7a46, 0x71 => 0x7a4c, 0x72 => 0x7a4b, 0x73 => 0x7aba,
    0x74 => 0x7bd9, 0x75 => 0x7c11, 0x76 => 0x7bc9, 0x77 => 0x7be4,
    0x78 => 0x7bdb, 0x79 => 0x7be1, 0x7a => 0x7be9, 0x7b => 0x7be6,
    0x7c => 0x7cd5, 0x7d => 0x7cd6, 0x7e => 0x7e0a, 0xa1 => 0x7e11,
    0xa2 => 0x7e08, 0xa3 => 0x7e1b, 0xa4 => 0x7e23, 0xa5 => 0x7e1e,
    0xa6 => 0x7e1d, 0xa7 => 0x7e09, 0xa8 => 0x7e10, 0xa9 => 0x7f79,
    0xaa => 0x7fb2, 0xab => 0x7ff0, 0xac => 0x7ff1, 0xad => 0x7fee,
    0xae => 0x8028, 0xaf => 0x81b3, 0xb0 => 0x81a9, 0xb1 => 0x81a8,
    0xb2 => 0x81fb, 0xb3 => 0x8208, 0xb4 => 0x8258, 0xb5 => 0x8259,
    0xb6 => 0x854a, 0xb7 => 0x8559, 0xb8 => 0x8548, 0xb9 => 0x8568,
    0xba => 0x8569, 0xbb => 0x8543, 0xbc => 0x8549, 0xbd => 0x856d,
    0xbe => 0x856a, 0xbf => 0x855e, 0xc0 => 0x8783, 0xc1 => 0x879f,
    0xc2 => 0x879e, 0xc3 => 0x87a2, 0xc4 => 0x878d, 0xc5 => 0x8861,
    0xc6 => 0x892a, 0xc7 => 0x8932, 0xc8 => 0x8925, 0xc9 => 0x892b,
    0xca => 0x8921, 0xcb => 0x89aa, 0xcc => 0x89a6, 0xcd => 0x8ae6,
    0xce => 0x8afa, 0xcf => 0x8aeb, 0xd0 => 0x8af1, 0xd1 => 0x8b00,
    0xd2 => 0x8adc, 0xd3 => 0x8ae7, 0xd4 => 0x8aee, 0xd5 => 0x8afe,
    0xd6 => 0x8b01, 0xd7 => 0x8b02, 0xd8 => 0x8af7, 0xd9 => 0x8aed,
    0xda => 0x8af3, 0xdb => 0x8af6, 0xdc => 0x8afc, 0xdd => 0x8c6b,
    0xde => 0x8c6d, 0xdf => 0x8c93, 0xe0 => 0x8cf4, 0xe1 => 0x8e44,
    0xe2 => 0x8e31, 0xe3 => 0x8e34, 0xe4 => 0x8e42, 0xe5 => 0x8e39,
    0xe6 => 0x8e35, 0xe7 => 0x8f3b, 0xe8 => 0x8f2f, 0xe9 => 0x8f38,
    0xea => 0x8f33, 0xeb => 0x8fa8, 0xec => 0x8fa6, 0xed => 0x9075,
    0xee => 0x9074, 0xef => 0x9078, 0xf0 => 0x9072, 0xf1 => 0x907c,
    0xf2 => 0x907a, 0xf3 => 0x9134, 0xf4 => 0x9192, 0xf5 => 0x9320,
    0xf6 => 0x9336, 0xf7 => 0x92f8, 0xf8 => 0x9333, 0xf9 => 0x932f,
    0xfa => 0x9322, 0xfb => 0x92fc, 0xfc => 0x932b, 0xfd => 0x9304,
    0xfe => 0x931a,
  },
  0xc0 => {
    0x40 => 0x9310, 0x41 => 0x9326, 0x42 => 0x9321, 0x43 => 0x9315,
    0x44 => 0x932e, 0x45 => 0x9319, 0x46 => 0x95bb, 0x47 => 0x96a7,
    0x48 => 0x96a8, 0x49 => 0x96aa, 0x4a => 0x96d5, 0x4b => 0x970e,
    0x4c => 0x9711, 0x4d => 0x9716, 0x4e => 0x970d, 0x4f => 0x9713,
    0x50 => 0x970f, 0x51 => 0x975b, 0x52 => 0x975c, 0x53 => 0x9766,
    0x54 => 0x9798, 0x55 => 0x9830, 0x56 => 0x9838, 0x57 => 0x983b,
    0x58 => 0x9837, 0x59 => 0x982d, 0x5a => 0x9839, 0x5b => 0x9824,
    0x5c => 0x9910, 0x5d => 0x9928, 0x5e => 0x991e, 0x5f => 0x991b,
    0x60 => 0x9921, 0x61 => 0x991a, 0x62 => 0x99ed, 0x63 => 0x99e2,
    0x64 => 0x99f1, 0x65 => 0x9ab8, 0x66 => 0x9abc, 0x67 => 0x9afb,
    0x68 => 0x9aed, 0x69 => 0x9b28, 0x6a => 0x9b91, 0x6b => 0x9d15,
    0x6c => 0x9d23, 0x6d => 0x9d26, 0x6e => 0x9d28, 0x6f => 0x9d12,
    0x70 => 0x9d1b, 0x71 => 0x9ed8, 0x72 => 0x9ed4, 0x73 => 0x9f8d,
    0x74 => 0x9f9c, 0x75 => 0x512a, 0x76 => 0x511f, 0x77 => 0x5121,
    0x78 => 0x5132, 0x79 => 0x52f5, 0x7a => 0x568e, 0x7b => 0x5680,
    0x7c => 0x5690, 0x7d => 0x5685, 0x7e => 0x5687, 0xa1 => 0x568f,
    0xa2 => 0x58d5, 0xa3 => 0x58d3, 0xa4 => 0x58d1, 0xa5 => 0x58ce,
    0xa6 => 0x5b30, 0xa7 => 0x5b2a, 0xa8 => 0x5b24, 0xa9 => 0x5b7a,
    0xaa => 0x5c37, 0xab => 0x5c68, 0xac => 0x5dbc, 0xad => 0x5dba,
    0xae => 0x5dbd, 0xaf => 0x5db8, 0xb0 => 0x5e6b, 0xb1 => 0x5f4c,
    0xb2 => 0x5fbd, 0xb3 => 0x61c9, 0xb4 => 0x61c2, 0xb5 => 0x61c7,
    0xb6 => 0x61e6, 0xb7 => 0x61cb, 0xb8 => 0x6232, 0xb9 => 0x6234,
    0xba => 0x64ce, 0xbb => 0x64ca, 0xbc => 0x64d8, 0xbd => 0x64e0,
    0xbe => 0x64f0, 0xbf => 0x64e6, 0xc0 => 0x64ec, 0xc1 => 0x64f1,
    0xc2 => 0x64e2, 0xc3 => 0x64ed, 0xc4 => 0x6582, 0xc5 => 0x6583,
    0xc6 => 0x66d9, 0xc7 => 0x66d6, 0xc8 => 0x6a80, 0xc9 => 0x6a94,
    0xca => 0x6a84, 0xcb => 0x6aa2, 0xcc => 0x6a9c, 0xcd => 0x6adb,
    0xce => 0x6aa3, 0xcf => 0x6a7e, 0xd0 => 0x6a97, 0xd1 => 0x6a90,
    0xd2 => 0x6aa0, 0xd3 => 0x6b5c, 0xd4 => 0x6bae, 0xd5 => 0x6bda,
    0xd6 => 0x6c08, 0xd7 => 0x6fd8, 0xd8 => 0x6ff1, 0xd9 => 0x6fdf,
    0xda => 0x6fe0, 0xdb => 0x6fdb, 0xdc => 0x6fe4, 0xdd => 0x6feb,
    0xde => 0x6fef, 0xdf => 0x6f80, 0xe0 => 0x6fec, 0xe1 => 0x6fe1,
    0xe2 => 0x6fe9, 0xe3 => 0x6fd5, 0xe4 => 0x6fee, 0xe5 => 0x6ff0,
    0xe6 => 0x71e7, 0xe7 => 0x71df, 0xe8 => 0x71ee, 0xe9 => 0x71e6,
    0xea => 0x71e5, 0xeb => 0x71ed, 0xec => 0x71ec, 0xed => 0x71f4,
    0xee => 0x71e0, 0xef => 0x7235, 0xf0 => 0x7246, 0xf1 => 0x7370,
    0xf2 => 0x7372, 0xf3 => 0x74a9, 0xf4 => 0x74b0, 0xf5 => 0x74a6,
    0xf6 => 0x74a8, 0xf7 => 0x7646, 0xf8 => 0x7642, 0xf9 => 0x764c,
    0xfa => 0x76ea, 0xfb => 0x77b3, 0xfc => 0x77aa, 0xfd => 0x77b0,
    0xfe => 0x77ac,
  },
  0xc1 => {
    0x40 => 0x77a7, 0x41 => 0x77ad, 0x42 => 0x77ef, 0x43 => 0x78f7,
    0x44 => 0x78fa, 0x45 => 0x78f4, 0x46 => 0x78ef, 0x47 => 0x7901,
    0x48 => 0x79a7, 0x49 => 0x79aa, 0x4a => 0x7a57, 0x4b => 0x7abf,
    0x4c => 0x7c07, 0x4d => 0x7c0d, 0x4e => 0x7bfe, 0x4f => 0x7bf7,
    0x50 => 0x7c0c, 0x51 => 0x7be0, 0x52 => 0x7ce0, 0x53 => 0x7cdc,
    0x54 => 0x7cde, 0x55 => 0x7ce2, 0x56 => 0x7cdf, 0x57 => 0x7cd9,
    0x58 => 0x7cdd, 0x59 => 0x7e2e, 0x5a => 0x7e3e, 0x5b => 0x7e46,
    0x5c => 0x7e37, 0x5d => 0x7e32, 0x5e => 0x7e43, 0x5f => 0x7e2b,
    0x60 => 0x7e3d, 0x61 => 0x7e31, 0x62 => 0x7e45, 0x63 => 0x7e41,
    0x64 => 0x7e34, 0x65 => 0x7e39, 0x66 => 0x7e48, 0x67 => 0x7e35,
    0x68 => 0x7e3f, 0x69 => 0x7e2f, 0x6a => 0x7f44, 0x6b => 0x7ff3,
    0x6c => 0x7ffc, 0x6d => 0x8071, 0x6e => 0x8072, 0x6f => 0x8070,
    0x70 => 0x806f, 0x71 => 0x8073, 0x72 => 0x81c6, 0x73 => 0x81c3,
    0x74 => 0x81ba, 0x75 => 0x81c2, 0x76 => 0x81c0, 0x77 => 0x81bf,
    0x78 => 0x81bd, 0x79 => 0x81c9, 0x7a => 0x81be, 0x7b => 0x81e8,
    0x7c => 0x8209, 0x7d => 0x8271, 0x7e => 0x85aa, 0xa1 => 0x8584,
    0xa2 => 0x857e, 0xa3 => 0x859c, 0xa4 => 0x8591, 0xa5 => 0x8594,
    0xa6 => 0x85af, 0xa7 => 0x859b, 0xa8 => 0x8587, 0xa9 => 0x85a8,
    0xaa => 0x858a, 0xab => 0x8667, 0xac => 0x87c0, 0xad => 0x87d1,
    0xae => 0x87b3, 0xaf => 0x87d2, 0xb0 => 0x87c6, 0xb1 => 0x87ab,
    0xb2 => 0x87bb, 0xb3 => 0x87ba, 0xb4 => 0x87c8, 0xb5 => 0x87cb,
    0xb6 => 0x893b, 0xb7 => 0x8936, 0xb8 => 0x8944, 0xb9 => 0x8938,
    0xba => 0x893d, 0xbb => 0x89ac, 0xbc => 0x8b0e, 0xbd => 0x8b17,
    0xbe => 0x8b19, 0xbf => 0x8b1b, 0xc0 => 0x8b0a, 0xc1 => 0x8b20,
    0xc2 => 0x8b1d, 0xc3 => 0x8b04, 0xc4 => 0x8b10, 0xc5 => 0x8c41,
    0xc6 => 0x8c3f, 0xc7 => 0x8c73, 0xc8 => 0x8cfa, 0xc9 => 0x8cfd,
    0xca => 0x8cfc, 0xcb => 0x8cf8, 0xcc => 0x8cfb, 0xcd => 0x8da8,
    0xce => 0x8e49, 0xcf => 0x8e4b, 0xd0 => 0x8e48, 0xd1 => 0x8e4a,
    0xd2 => 0x8f44, 0xd3 => 0x8f3e, 0xd4 => 0x8f42, 0xd5 => 0x8f45,
    0xd6 => 0x8f3f, 0xd7 => 0x907f, 0xd8 => 0x907d, 0xd9 => 0x9084,
    0xda => 0x9081, 0xdb => 0x9082, 0xdc => 0x9080, 0xdd => 0x9139,
    0xde => 0x91a3, 0xdf => 0x919e, 0xe0 => 0x919c, 0xe1 => 0x934d,
    0xe2 => 0x9382, 0xe3 => 0x9328, 0xe4 => 0x9375, 0xe5 => 0x934a,
    0xe6 => 0x9365, 0xe7 => 0x934b, 0xe8 => 0x9318, 0xe9 => 0x937e,
    0xea => 0x936c, 0xeb => 0x935b, 0xec => 0x9370, 0xed => 0x935a,
    0xee => 0x9354, 0xef => 0x95ca, 0xf0 => 0x95cb, 0xf1 => 0x95cc,
    0xf2 => 0x95c8, 0xf3 => 0x95c6, 0xf4 => 0x96b1, 0xf5 => 0x96b8,
    0xf6 => 0x96d6, 0xf7 => 0x971c, 0xf8 => 0x971e, 0xf9 => 0x97a0,
    0xfa => 0x97d3, 0xfb => 0x9846, 0xfc => 0x98b6, 0xfd => 0x9935,
    0xfe => 0x9a01,
  },
  0xc2 => {
    0x40 => 0x99ff, 0x41 => 0x9bae, 0x42 => 0x9bab, 0x43 => 0x9baa,
    0x44 => 0x9bad, 0x45 => 0x9d3b, 0x46 => 0x9d3f, 0x47 => 0x9e8b,
    0x48 => 0x9ecf, 0x49 => 0x9ede, 0x4a => 0x9edc, 0x4b => 0x9edd,
    0x4c => 0x9edb, 0x4d => 0x9f3e, 0x4e => 0x9f4b, 0x4f => 0x53e2,
    0x50 => 0x5695, 0x51 => 0x56ae, 0x52 => 0x58d9, 0x53 => 0x58d8,
    0x54 => 0x5b38, 0x55 => 0x5f5d, 0x56 => 0x61e3, 0x57 => 0x6233,
    0x58 => 0x64f4, 0x59 => 0x64f2, 0x5a => 0x64fe, 0x5b => 0x6506,
    0x5c => 0x64fa, 0x5d => 0x64fb, 0x5e => 0x64f7, 0x5f => 0x65b7,
    0x60 => 0x66dc, 0x61 => 0x6726, 0x62 => 0x6ab3, 0x63 => 0x6aac,
    0x64 => 0x6ac3, 0x65 => 0x6abb, 0x66 => 0x6ab8, 0x67 => 0x6ac2,
    0x68 => 0x6aae, 0x69 => 0x6aaf, 0x6a => 0x6b5f, 0x6b => 0x6b78,
    0x6c => 0x6baf, 0x6d => 0x7009, 0x6e => 0x700b, 0x6f => 0x6ffe,
    0x70 => 0x7006, 0x71 => 0x6ffa, 0x72 => 0x7011, 0x73 => 0x700f,
    0x74 => 0x71fb, 0x75 => 0x71fc, 0x76 => 0x71fe, 0x77 => 0x71f8,
    0x78 => 0x7377, 0x79 => 0x7375, 0x7a => 0x74a7, 0x7b => 0x74bf,
    0x7c => 0x7515, 0x7d => 0x7656, 0x7e => 0x7658, 0xa1 => 0x7652,
    0xa2 => 0x77bd, 0xa3 => 0x77bf, 0xa4 => 0x77bb, 0xa5 => 0x77bc,
    0xa6 => 0x790e, 0xa7 => 0x79ae, 0xa8 => 0x7a61, 0xa9 => 0x7a62,
    0xaa => 0x7a60, 0xab => 0x7ac4, 0xac => 0x7ac5, 0xad => 0x7c2b,
    0xae => 0x7c27, 0xaf => 0x7c2a, 0xb0 => 0x7c1e, 0xb1 => 0x7c23,
    0xb2 => 0x7c21, 0xb3 => 0x7ce7, 0xb4 => 0x7e54, 0xb5 => 0x7e55,
    0xb6 => 0x7e5e, 0xb7 => 0x7e5a, 0xb8 => 0x7e61, 0xb9 => 0x7e52,
    0xba => 0x7e59, 0xbb => 0x7f48, 0xbc => 0x7ff9, 0xbd => 0x7ffb,
    0xbe => 0x8077, 0xbf => 0x8076, 0xc0 => 0x81cd, 0xc1 => 0x81cf,
    0xc2 => 0x820a, 0xc3 => 0x85cf, 0xc4 => 0x85a9, 0xc5 => 0x85cd,
    0xc6 => 0x85d0, 0xc7 => 0x85c9, 0xc8 => 0x85b0, 0xc9 => 0x85ba,
    0xca => 0x85b9, 0xcb => 0x85a6, 0xcc => 0x87ef, 0xcd => 0x87ec,
    0xce => 0x87f2, 0xcf => 0x87e0, 0xd0 => 0x8986, 0xd1 => 0x89b2,
    0xd2 => 0x89f4, 0xd3 => 0x8b28, 0xd4 => 0x8b39, 0xd5 => 0x8b2c,
    0xd6 => 0x8b2b, 0xd7 => 0x8c50, 0xd8 => 0x8d05, 0xd9 => 0x8e59,
    0xda => 0x8e63, 0xdb => 0x8e66, 0xdc => 0x8e64, 0xdd => 0x8e5f,
    0xde => 0x8e55, 0xdf => 0x8ec0, 0xe0 => 0x8f49, 0xe1 => 0x8f4d,
    0xe2 => 0x9087, 0xe3 => 0x9083, 0xe4 => 0x9088, 0xe5 => 0x91ab,
    0xe6 => 0x91ac, 0xe7 => 0x91d0, 0xe8 => 0x9394, 0xe9 => 0x938a,
    0xea => 0x9396, 0xeb => 0x93a2, 0xec => 0x93b3, 0xed => 0x93ae,
    0xee => 0x93ac, 0xef => 0x93b0, 0xf0 => 0x9398, 0xf1 => 0x939a,
    0xf2 => 0x9397, 0xf3 => 0x95d4, 0xf4 => 0x95d6, 0xf5 => 0x95d0,
    0xf6 => 0x95d5, 0xf7 => 0x96e2, 0xf8 => 0x96dc, 0xf9 => 0x96d9,
    0xfa => 0x96db, 0xfb => 0x96de, 0xfc => 0x9724, 0xfd => 0x97a3,
    0xfe => 0x97a6,
  },
  0xc3 => {
    0x40 => 0x97ad, 0x41 => 0x97f9, 0x42 => 0x984d, 0x43 => 0x984f,
    0x44 => 0x984c, 0x45 => 0x984e, 0x46 => 0x9853, 0x47 => 0x98ba,
    0x48 => 0x993e, 0x49 => 0x993f, 0x4a => 0x993d, 0x4b => 0x992e,
    0x4c => 0x99a5, 0x4d => 0x9a0e, 0x4e => 0x9ac1, 0x4f => 0x9b03,
    0x50 => 0x9b06, 0x51 => 0x9b4f, 0x52 => 0x9b4e, 0x53 => 0x9b4d,
    0x54 => 0x9bca, 0x55 => 0x9bc9, 0x56 => 0x9bfd, 0x57 => 0x9bc8,
    0x58 => 0x9bc0, 0x59 => 0x9d51, 0x5a => 0x9d5d, 0x5b => 0x9d60,
    0x5c => 0x9ee0, 0x5d => 0x9f15, 0x5e => 0x9f2c, 0x5f => 0x5133,
    0x60 => 0x56a5, 0x61 => 0x58de, 0x62 => 0x58df, 0x63 => 0x58e2,
    0x64 => 0x5bf5, 0x65 => 0x9f90, 0x66 => 0x5eec, 0x67 => 0x61f2,
    0x68 => 0x61f7, 0x69 => 0x61f6, 0x6a => 0x61f5, 0x6b => 0x6500,
    0x6c => 0x650f, 0x6d => 0x66e0, 0x6e => 0x66dd, 0x6f => 0x6ae5,
    0x70 => 0x6add, 0x71 => 0x6ada, 0x72 => 0x6ad3, 0x73 => 0x701b,
    0x74 => 0x701f, 0x75 => 0x7028, 0x76 => 0x701a, 0x77 => 0x701d,
    0x78 => 0x7015, 0x79 => 0x7018, 0x7a => 0x7206, 0x7b => 0x720d,
    0x7c => 0x7258, 0x7d => 0x72a2, 0x7e => 0x7378, 0xa1 => 0x737a,
    0xa2 => 0x74bd, 0xa3 => 0x74ca, 0xa4 => 0x74e3, 0xa5 => 0x7587,
    0xa6 => 0x7586, 0xa7 => 0x765f, 0xa8 => 0x7661, 0xa9 => 0x77c7,
    0xaa => 0x7919, 0xab => 0x79b1, 0xac => 0x7a6b, 0xad => 0x7a69,
    0xae => 0x7c3e, 0xaf => 0x7c3f, 0xb0 => 0x7c38, 0xb1 => 0x7c3d,
    0xb2 => 0x7c37, 0xb3 => 0x7c40, 0xb4 => 0x7e6b, 0xb5 => 0x7e6d,
    0xb6 => 0x7e79, 0xb7 => 0x7e69, 0xb8 => 0x7e6a, 0xb9 => 0x7f85,
    0xba => 0x7e73, 0xbb => 0x7fb6, 0xbc => 0x7fb9, 0xbd => 0x7fb8,
    0xbe => 0x81d8, 0xbf => 0x85e9, 0xc0 => 0x85dd, 0xc1 => 0x85ea,
    0xc2 => 0x85d5, 0xc3 => 0x85e4, 0xc4 => 0x85e5, 0xc5 => 0x85f7,
    0xc6 => 0x87fb, 0xc7 => 0x8805, 0xc8 => 0x880d, 0xc9 => 0x87f9,
    0xca => 0x87fe, 0xcb => 0x8960, 0xcc => 0x895f, 0xcd => 0x8956,
    0xce => 0x895e, 0xcf => 0x8b41, 0xd0 => 0x8b5c, 0xd1 => 0x8b58,
    0xd2 => 0x8b49, 0xd3 => 0x8b5a, 0xd4 => 0x8b4e, 0xd5 => 0x8b4f,
    0xd6 => 0x8b46, 0xd7 => 0x8b59, 0xd8 => 0x8d08, 0xd9 => 0x8d0a,
    0xda => 0x8e7c, 0xdb => 0x8e72, 0xdc => 0x8e87, 0xdd => 0x8e76,
    0xde => 0x8e6c, 0xdf => 0x8e7a, 0xe0 => 0x8e74, 0xe1 => 0x8f54,
    0xe2 => 0x8f4e, 0xe3 => 0x8fad, 0xe4 => 0x908a, 0xe5 => 0x908b,
    0xe6 => 0x91b1, 0xe7 => 0x91ae, 0xe8 => 0x93e1, 0xe9 => 0x93d1,
    0xea => 0x93df, 0xeb => 0x93c3, 0xec => 0x93c8, 0xed => 0x93dc,
    0xee => 0x93dd, 0xef => 0x93d6, 0xf0 => 0x93e2, 0xf1 => 0x93cd,
    0xf2 => 0x93d8, 0xf3 => 0x93e4, 0xf4 => 0x93d7, 0xf5 => 0x93e8,
    0xf6 => 0x95dc, 0xf7 => 0x96b4, 0xf8 => 0x96e3, 0xf9 => 0x972a,
    0xfa => 0x9727, 0xfb => 0x9761, 0xfc => 0x97dc, 0xfd => 0x97fb,
    0xfe => 0x985e,
  },
  0xc4 => {
    0x40 => 0x9858, 0x41 => 0x985b, 0x42 => 0x98bc, 0x43 => 0x9945,
    0x44 => 0x9949, 0x45 => 0x9a16, 0x46 => 0x9a19, 0x47 => 0x9b0d,
    0x48 => 0x9be8, 0x49 => 0x9be7, 0x4a => 0x9bd6, 0x4b => 0x9bdb,
    0x4c => 0x9d89, 0x4d => 0x9d61, 0x4e => 0x9d72, 0x4f => 0x9d6a,
    0x50 => 0x9d6c, 0x51 => 0x9e92, 0x52 => 0x9e97, 0x53 => 0x9e93,
    0x54 => 0x9eb4, 0x55 => 0x52f8, 0x56 => 0x56a8, 0x57 => 0x56b7,
    0x58 => 0x56b6, 0x59 => 0x56b4, 0x5a => 0x56bc, 0x5b => 0x58e4,
    0x5c => 0x5b40, 0x5d => 0x5b43, 0x5e => 0x5b7d, 0x5f => 0x5bf6,
    0x60 => 0x5dc9, 0x61 => 0x61f8, 0x62 => 0x61fa, 0x63 => 0x6518,
    0x64 => 0x6514, 0x65 => 0x6519, 0x66 => 0x66e6, 0x67 => 0x6727,
    0x68 => 0x6aec, 0x69 => 0x703e, 0x6a => 0x7030, 0x6b => 0x7032,
    0x6c => 0x7210, 0x6d => 0x737b, 0x6e => 0x74cf, 0x6f => 0x7662,
    0x70 => 0x7665, 0x71 => 0x7926, 0x72 => 0x792a, 0x73 => 0x792c,
    0x74 => 0x792b, 0x75 => 0x7ac7, 0x76 => 0x7af6, 0x77 => 0x7c4c,
    0x78 => 0x7c43, 0x79 => 0x7c4d, 0x7a => 0x7cef, 0x7b => 0x7cf0,
    0x7c => 0x8fae, 0x7d => 0x7e7d, 0x7e => 0x7e7c, 0xa1 => 0x7e82,
    0xa2 => 0x7f4c, 0xa3 => 0x8000, 0xa4 => 0x81da, 0xa5 => 0x8266,
    0xa6 => 0x85fb, 0xa7 => 0x85f9, 0xa8 => 0x8611, 0xa9 => 0x85fa,
    0xaa => 0x8606, 0xab => 0x860b, 0xac => 0x8607, 0xad => 0x860a,
    0xae => 0x8814, 0xaf => 0x8815, 0xb0 => 0x8964, 0xb1 => 0x89ba,
    0xb2 => 0x89f8, 0xb3 => 0x8b70, 0xb4 => 0x8b6c, 0xb5 => 0x8b66,
    0xb6 => 0x8b6f, 0xb7 => 0x8b5f, 0xb8 => 0x8b6b, 0xb9 => 0x8d0f,
    0xba => 0x8d0d, 0xbb => 0x8e89, 0xbc => 0x8e81, 0xbd => 0x8e85,
    0xbe => 0x8e82, 0xbf => 0x91b4, 0xc0 => 0x91cb, 0xc1 => 0x9418,
    0xc2 => 0x9403, 0xc3 => 0x93fd, 0xc4 => 0x95e1, 0xc5 => 0x9730,
    0xc6 => 0x98c4, 0xc7 => 0x9952, 0xc8 => 0x9951, 0xc9 => 0x99a8,
    0xca => 0x9a2b, 0xcb => 0x9a30, 0xcc => 0x9a37, 0xcd => 0x9a35,
    0xce => 0x9c13, 0xcf => 0x9c0d, 0xd0 => 0x9e79, 0xd1 => 0x9eb5,
    0xd2 => 0x9ee8, 0xd3 => 0x9f2f, 0xd4 => 0x9f5f, 0xd5 => 0x9f63,
    0xd6 => 0x9f61, 0xd7 => 0x5137, 0xd8 => 0x5138, 0xd9 => 0x56c1,
    0xda => 0x56c0, 0xdb => 0x56c2, 0xdc => 0x5914, 0xdd => 0x5c6c,
    0xde => 0x5dcd, 0xdf => 0x61fc, 0xe0 => 0x61fe, 0xe1 => 0x651d,
    0xe2 => 0x651c, 0xe3 => 0x6595, 0xe4 => 0x66e9, 0xe5 => 0x6afb,
    0xe6 => 0x6b04, 0xe7 => 0x6afa, 0xe8 => 0x6bb2, 0xe9 => 0x704c,
    0xea => 0x721b, 0xeb => 0x72a7, 0xec => 0x74d6, 0xed => 0x74d4,
    0xee => 0x7669, 0xef => 0x77d3, 0xf0 => 0x7c50, 0xf1 => 0x7e8f,
    0xf2 => 0x7e8c, 0xf3 => 0x7fbc, 0xf4 => 0x8617, 0xf5 => 0x862d,
    0xf6 => 0x861a, 0xf7 => 0x8823, 0xf8 => 0x8822, 0xf9 => 0x8821,
    0xfa => 0x881f, 0xfb => 0x896a, 0xfc => 0x896c, 0xfd => 0x89bd,
    0xfe => 0x8b74,
  },
  0xc5 => {
    0x40 => 0x8b77, 0x41 => 0x8b7d, 0x42 => 0x8d13, 0x43 => 0x8e8a,
    0x44 => 0x8e8d, 0x45 => 0x8e8b, 0x46 => 0x8f5f, 0x47 => 0x8faf,
    0x48 => 0x91ba, 0x49 => 0x942e, 0x4a => 0x9433, 0x4b => 0x9435,
    0x4c => 0x943a, 0x4d => 0x9438, 0x4e => 0x9432, 0x4f => 0x942b,
    0x50 => 0x95e2, 0x51 => 0x9738, 0x52 => 0x9739, 0x53 => 0x9732,
    0x54 => 0x97ff, 0x55 => 0x9867, 0x56 => 0x9865, 0x57 => 0x9957,
    0x58 => 0x9a45, 0x59 => 0x9a43, 0x5a => 0x9a40, 0x5b => 0x9a3e,
    0x5c => 0x9acf, 0x5d => 0x9b54, 0x5e => 0x9b51, 0x5f => 0x9c2d,
    0x60 => 0x9c25, 0x61 => 0x9daf, 0x62 => 0x9db4, 0x63 => 0x9dc2,
    0x64 => 0x9db8, 0x65 => 0x9e9d, 0x66 => 0x9eef, 0x67 => 0x9f19,
    0x68 => 0x9f5c, 0x69 => 0x9f66, 0x6a => 0x9f67, 0x6b => 0x513c,
    0x6c => 0x513b, 0x6d => 0x56c8, 0x6e => 0x56ca, 0x6f => 0x56c9,
    0x70 => 0x5b7f, 0x71 => 0x5dd4, 0x72 => 0x5dd2, 0x73 => 0x5f4e,
    0x74 => 0x61ff, 0x75 => 0x6524, 0x76 => 0x6b0a, 0x77 => 0x6b61,
    0x78 => 0x7051, 0x79 => 0x7058, 0x7a => 0x7380, 0x7b => 0x74e4,
    0x7c => 0x758a, 0x7d => 0x766e, 0x7e => 0x766c, 0xa1 => 0x79b3,
    0xa2 => 0x7c60, 0xa3 => 0x7c5f, 0xa4 => 0x807e, 0xa5 => 0x807d,
    0xa6 => 0x81df, 0xa7 => 0x8972, 0xa8 => 0x896f, 0xa9 => 0x89fc,
    0xaa => 0x8b80, 0xab => 0x8d16, 0xac => 0x8d17, 0xad => 0x8e91,
    0xae => 0x8e93, 0xaf => 0x8f61, 0xb0 => 0x9148, 0xb1 => 0x9444,
    0xb2 => 0x9451, 0xb3 => 0x9452, 0xb4 => 0x973d, 0xb5 => 0x973e,
    0xb6 => 0x97c3, 0xb7 => 0x97c1, 0xb8 => 0x986b, 0xb9 => 0x9955,
    0xba => 0x9a55, 0xbb => 0x9a4d, 0xbc => 0x9ad2, 0xbd => 0x9b1a,
    0xbe => 0x9c49, 0xbf => 0x9c31, 0xc0 => 0x9c3e, 0xc1 => 0x9c3b,
    0xc2 => 0x9dd3, 0xc3 => 0x9dd7, 0xc4 => 0x9f34, 0xc5 => 0x9f6c,
    0xc6 => 0x9f6a, 0xc7 => 0x9f94, 0xc8 => 0x56cc, 0xc9 => 0x5dd6,
    0xca => 0x6200, 0xcb => 0x6523, 0xcc => 0x652b, 0xcd => 0x652a,
    0xce => 0x66ec, 0xcf => 0x6b10, 0xd0 => 0x74da, 0xd1 => 0x7aca,
    0xd2 => 0x7c64, 0xd3 => 0x7c63, 0xd4 => 0x7c65, 0xd5 => 0x7e93,
    0xd6 => 0x7e96, 0xd7 => 0x7e94, 0xd8 => 0x81e2, 0xd9 => 0x8638,
    0xda => 0x863f, 0xdb => 0x8831, 0xdc => 0x8b8a, 0xdd => 0x9090,
    0xde => 0x908f, 0xdf => 0x9463, 0xe0 => 0x9460, 0xe1 => 0x9464,
    0xe2 => 0x9768, 0xe3 => 0x986f, 0xe4 => 0x995c, 0xe5 => 0x9a5a,
    0xe6 => 0x9a5b, 0xe7 => 0x9a57, 0xe8 => 0x9ad3, 0xe9 => 0x9ad4,
    0xea => 0x9ad1, 0xeb => 0x9c54, 0xec => 0x9c57, 0xed => 0x9c56,
    0xee => 0x9de5, 0xef => 0x9e9f, 0xf0 => 0x9ef4, 0xf1 => 0x56d1,
    0xf2 => 0x58e9, 0xf3 => 0x652c, 0xf4 => 0x705e, 0xf5 => 0x7671,
    0xf6 => 0x7672, 0xf7 => 0x77d7, 0xf8 => 0x7f50, 0xf9 => 0x7f88,
    0xfa => 0x8836, 0xfb => 0x8839, 0xfc => 0x8862, 0xfd => 0x8b93,
    0xfe => 0x8b92,
  },
  0xc6 => {
    0x40 => 0x8b96, 0x41 => 0x8277, 0x42 => 0x8d1b, 0x43 => 0x91c0,
    0x44 => 0x946a, 0x45 => 0x9742, 0x46 => 0x9748, 0x47 => 0x9744,
    0x48 => 0x97c6, 0x49 => 0x9870, 0x4a => 0x9a5f, 0x4b => 0x9b22,
    0x4c => 0x9b58, 0x4d => 0x9c5f, 0x4e => 0x9df9, 0x4f => 0x9dfa,
    0x50 => 0x9e7c, 0x51 => 0x9e7d, 0x52 => 0x9f07, 0x53 => 0x9f77,
    0x54 => 0x9f72, 0x55 => 0x5ef3, 0x56 => 0x6b16, 0x57 => 0x7063,
    0x58 => 0x7c6c, 0x59 => 0x7c6e, 0x5a => 0x883b, 0x5b => 0x89c0,
    0x5c => 0x8ea1, 0x5d => 0x91c1, 0x5e => 0x9472, 0x5f => 0x9470,
    0x60 => 0x9871, 0x61 => 0x995e, 0x62 => 0x9ad6, 0x63 => 0x9b23,
    0x64 => 0x9ecc, 0x65 => 0x7064, 0x66 => 0x77da, 0x67 => 0x8b9a,
    0x68 => 0x9477, 0x69 => 0x97c9, 0x6a => 0x9a62, 0x6b => 0x9a65,
    0x6c => 0x7e9c, 0x6d => 0x8b9c, 0x6e => 0x8eaa, 0x6f => 0x91c5,
    0x70 => 0x947d, 0x71 => 0x947e, 0x72 => 0x947c, 0x73 => 0x9c77,
    0x74 => 0x9c78, 0x75 => 0x9ef7, 0x76 => 0x8c54, 0x77 => 0x947f,
    0x78 => 0x9e1a, 0x79 => 0x7228, 0x7a => 0x9a6a, 0x7b => 0x9b31,
    0x7c => 0x9e1b, 0x7d => 0x9e1e, 0x7e => 0x7c72,
  },
  0xc9 => {
    0x40 => 0x4e42, 0x41 => 0x4e5c, 0x42 => 0x51f5, 0x43 => 0x531a,
    0x44 => 0x5382, 0x45 => 0x4e07, 0x46 => 0x4e0c, 0x47 => 0x4e47,
    0x48 => 0x4e8d, 0x49 => 0x56d7, 0x4a => 0xfa0c, 0x4b => 0x5c6e,
    0x4c => 0x5f73, 0x4d => 0x4e0f, 0x4e => 0x5187, 0x4f => 0x4e0e,
    0x50 => 0x4e2e, 0x51 => 0x4e93, 0x52 => 0x4ec2, 0x53 => 0x4ec9,
    0x54 => 0x4ec8, 0x55 => 0x5198, 0x56 => 0x52fc, 0x57 => 0x536c,
    0x58 => 0x53b9, 0x59 => 0x5720, 0x5a => 0x5903, 0x5b => 0x592c,
    0x5c => 0x5c10, 0x5d => 0x5dff, 0x5e => 0x65e1, 0x5f => 0x6bb3,
    0x60 => 0x6bcc, 0x61 => 0x6c14, 0x62 => 0x723f, 0x63 => 0x4e31,
    0x64 => 0x4e3c, 0x65 => 0x4ee8, 0x66 => 0x4edc, 0x67 => 0x4ee9,
    0x68 => 0x4ee1, 0x69 => 0x4edd, 0x6a => 0x4eda, 0x6b => 0x520c,
    0x6c => 0x531c, 0x6d => 0x534c, 0x6e => 0x5722, 0x6f => 0x5723,
    0x70 => 0x5917, 0x71 => 0x592f, 0x72 => 0x5b81, 0x73 => 0x5b84,
    0x74 => 0x5c12, 0x75 => 0x5c3b, 0x76 => 0x5c74, 0x77 => 0x5c73,
    0x78 => 0x5e04, 0x79 => 0x5e80, 0x7a => 0x5e82, 0x7b => 0x5fc9,
    0x7c => 0x6209, 0x7d => 0x6250, 0x7e => 0x6c15, 0xa1 => 0x6c36,
    0xa2 => 0x6c43, 0xa3 => 0x6c3f, 0xa4 => 0x6c3b, 0xa5 => 0x72ae,
    0xa6 => 0x72b0, 0xa7 => 0x738a, 0xa8 => 0x79b8, 0xa9 => 0x808a,
    0xaa => 0x961e, 0xab => 0x4f0e, 0xac => 0x4f18, 0xad => 0x4f2c,
    0xae => 0x4ef5, 0xaf => 0x4f14, 0xb0 => 0x4ef1, 0xb1 => 0x4f00,
    0xb2 => 0x4ef7, 0xb3 => 0x4f08, 0xb4 => 0x4f1d, 0xb5 => 0x4f02,
    0xb6 => 0x4f05, 0xb7 => 0x4f22, 0xb8 => 0x4f13, 0xb9 => 0x4f04,
    0xba => 0x4ef4, 0xbb => 0x4f12, 0xbc => 0x51b1, 0xbd => 0x5213,
    0xbe => 0x5209, 0xbf => 0x5210, 0xc0 => 0x52a6, 0xc1 => 0x5322,
    0xc2 => 0x531f, 0xc3 => 0x534d, 0xc4 => 0x538a, 0xc5 => 0x5407,
    0xc6 => 0x56e1, 0xc7 => 0x56df, 0xc8 => 0x572e, 0xc9 => 0x572a,
    0xca => 0x5734, 0xcb => 0x593c, 0xcc => 0x5980, 0xcd => 0x597c,
    0xce => 0x5985, 0xcf => 0x597b, 0xd0 => 0x597e, 0xd1 => 0x5977,
    0xd2 => 0x597f, 0xd3 => 0x5b56, 0xd4 => 0x5c15, 0xd5 => 0x5c25,
    0xd6 => 0x5c7c, 0xd7 => 0x5c7a, 0xd8 => 0x5c7b, 0xd9 => 0x5c7e,
    0xda => 0x5ddf, 0xdb => 0x5e75, 0xdc => 0x5e84, 0xdd => 0x5f02,
    0xde => 0x5f1a, 0xdf => 0x5f74, 0xe0 => 0x5fd5, 0xe1 => 0x5fd4,
    0xe2 => 0x5fcf, 0xe3 => 0x625c, 0xe4 => 0x625e, 0xe5 => 0x6264,
    0xe6 => 0x6261, 0xe7 => 0x6266, 0xe8 => 0x6262, 0xe9 => 0x6259,
    0xea => 0x6260, 0xeb => 0x625a, 0xec => 0x6265, 0xed => 0x65ef,
    0xee => 0x65ee, 0xef => 0x673e, 0xf0 => 0x6739, 0xf1 => 0x6738,
    0xf2 => 0x673b, 0xf3 => 0x673a, 0xf4 => 0x673f, 0xf5 => 0x673c,
    0xf6 => 0x6733, 0xf7 => 0x6c18, 0xf8 => 0x6c46, 0xf9 => 0x6c52,
    0xfa => 0x6c5c, 0xfb => 0x6c4f, 0xfc => 0x6c4a, 0xfd => 0x6c54,
    0xfe => 0x6c4b,
  },
  0xca => {
    0x40 => 0x6c4c, 0x41 => 0x7071, 0x42 => 0x725e, 0x43 => 0x72b4,
    0x44 => 0x72b5, 0x45 => 0x738e, 0x46 => 0x752a, 0x47 => 0x767f,
    0x48 => 0x7a75, 0x49 => 0x7f51, 0x4a => 0x8278, 0x4b => 0x827c,
    0x4c => 0x8280, 0x4d => 0x827d, 0x4e => 0x827f, 0x4f => 0x864d,
    0x50 => 0x897e, 0x51 => 0x9099, 0x52 => 0x9097, 0x53 => 0x9098,
    0x54 => 0x909b, 0x55 => 0x9094, 0x56 => 0x9622, 0x57 => 0x9624,
    0x58 => 0x9620, 0x59 => 0x9623, 0x5a => 0x4f56, 0x5b => 0x4f3b,
    0x5c => 0x4f62, 0x5d => 0x4f49, 0x5e => 0x4f53, 0x5f => 0x4f64,
    0x60 => 0x4f3e, 0x61 => 0x4f67, 0x62 => 0x4f52, 0x63 => 0x4f5f,
    0x64 => 0x4f41, 0x65 => 0x4f58, 0x66 => 0x4f2d, 0x67 => 0x4f33,
    0x68 => 0x4f3f, 0x69 => 0x4f61, 0x6a => 0x518f, 0x6b => 0x51b9,
    0x6c => 0x521c, 0x6d => 0x521e, 0x6e => 0x5221, 0x6f => 0x52ad,
    0x70 => 0x52ae, 0x71 => 0x5309, 0x72 => 0x5363, 0x73 => 0x5372,
    0x74 => 0x538e, 0x75 => 0x538f, 0x76 => 0x5430, 0x77 => 0x5437,
    0x78 => 0x542a, 0x79 => 0x5454, 0x7a => 0x5445, 0x7b => 0x5419,
    0x7c => 0x541c, 0x7d => 0x5425, 0x7e => 0x5418, 0xa1 => 0x543d,
    0xa2 => 0x544f, 0xa3 => 0x5441, 0xa4 => 0x5428, 0xa5 => 0x5424,
    0xa6 => 0x5447, 0xa7 => 0x56ee, 0xa8 => 0x56e7, 0xa9 => 0x56e5,
    0xaa => 0x5741, 0xab => 0x5745, 0xac => 0x574c, 0xad => 0x5749,
    0xae => 0x574b, 0xaf => 0x5752, 0xb0 => 0x5906, 0xb1 => 0x5940,
    0xb2 => 0x59a6, 0xb3 => 0x5998, 0xb4 => 0x59a0, 0xb5 => 0x5997,
    0xb6 => 0x598e, 0xb7 => 0x59a2, 0xb8 => 0x5990, 0xb9 => 0x598f,
    0xba => 0x59a7, 0xbb => 0x59a1, 0xbc => 0x5b8e, 0xbd => 0x5b92,
    0xbe => 0x5c28, 0xbf => 0x5c2a, 0xc0 => 0x5c8d, 0xc1 => 0x5c8f,
    0xc2 => 0x5c88, 0xc3 => 0x5c8b, 0xc4 => 0x5c89, 0xc5 => 0x5c92,
    0xc6 => 0x5c8a, 0xc7 => 0x5c86, 0xc8 => 0x5c93, 0xc9 => 0x5c95,
    0xca => 0x5de0, 0xcb => 0x5e0a, 0xcc => 0x5e0e, 0xcd => 0x5e8b,
    0xce => 0x5e89, 0xcf => 0x5e8c, 0xd0 => 0x5e88, 0xd1 => 0x5e8d,
    0xd2 => 0x5f05, 0xd3 => 0x5f1d, 0xd4 => 0x5f78, 0xd5 => 0x5f76,
    0xd6 => 0x5fd2, 0xd7 => 0x5fd1, 0xd8 => 0x5fd0, 0xd9 => 0x5fed,
    0xda => 0x5fe8, 0xdb => 0x5fee, 0xdc => 0x5ff3, 0xdd => 0x5fe1,
    0xde => 0x5fe4, 0xdf => 0x5fe3, 0xe0 => 0x5ffa, 0xe1 => 0x5fef,
    0xe2 => 0x5ff7, 0xe3 => 0x5ffb, 0xe4 => 0x6000, 0xe5 => 0x5ff4,
    0xe6 => 0x623a, 0xe7 => 0x6283, 0xe8 => 0x628c, 0xe9 => 0x628e,
    0xea => 0x628f, 0xeb => 0x6294, 0xec => 0x6287, 0xed => 0x6271,
    0xee => 0x627b, 0xef => 0x627a, 0xf0 => 0x6270, 0xf1 => 0x6281,
    0xf2 => 0x6288, 0xf3 => 0x6277, 0xf4 => 0x627d, 0xf5 => 0x6272,
    0xf6 => 0x6274, 0xf7 => 0x6537, 0xf8 => 0x65f0, 0xf9 => 0x65f4,
    0xfa => 0x65f3, 0xfb => 0x65f2, 0xfc => 0x65f5, 0xfd => 0x6745,
    0xfe => 0x6747,
  },
  0xcb => {
    0x40 => 0x6759, 0x41 => 0x6755, 0x42 => 0x674c, 0x43 => 0x6748,
    0x44 => 0x675d, 0x45 => 0x674d, 0x46 => 0x675a, 0x47 => 0x674b,
    0x48 => 0x6bd0, 0x49 => 0x6c19, 0x4a => 0x6c1a, 0x4b => 0x6c78,
    0x4c => 0x6c67, 0x4d => 0x6c6b, 0x4e => 0x6c84, 0x4f => 0x6c8b,
    0x50 => 0x6c8f, 0x51 => 0x6c71, 0x52 => 0x6c6f, 0x53 => 0x6c69,
    0x54 => 0x6c9a, 0x55 => 0x6c6d, 0x56 => 0x6c87, 0x57 => 0x6c95,
    0x58 => 0x6c9c, 0x59 => 0x6c66, 0x5a => 0x6c73, 0x5b => 0x6c65,
    0x5c => 0x6c7b, 0x5d => 0x6c8e, 0x5e => 0x7074, 0x5f => 0x707a,
    0x60 => 0x7263, 0x61 => 0x72bf, 0x62 => 0x72bd, 0x63 => 0x72c3,
    0x64 => 0x72c6, 0x65 => 0x72c1, 0x66 => 0x72ba, 0x67 => 0x72c5,
    0x68 => 0x7395, 0x69 => 0x7397, 0x6a => 0x7393, 0x6b => 0x7394,
    0x6c => 0x7392, 0x6d => 0x753a, 0x6e => 0x7539, 0x6f => 0x7594,
    0x70 => 0x7595, 0x71 => 0x7681, 0x72 => 0x793d, 0x73 => 0x8034,
    0x74 => 0x8095, 0x75 => 0x8099, 0x76 => 0x8090, 0x77 => 0x8092,
    0x78 => 0x809c, 0x79 => 0x8290, 0x7a => 0x828f, 0x7b => 0x8285,
    0x7c => 0x828e, 0x7d => 0x8291, 0x7e => 0x8293, 0xa1 => 0x828a,
    0xa2 => 0x8283, 0xa3 => 0x8284, 0xa4 => 0x8c78, 0xa5 => 0x8fc9,
    0xa6 => 0x8fbf, 0xa7 => 0x909f, 0xa8 => 0x90a1, 0xa9 => 0x90a5,
    0xaa => 0x909e, 0xab => 0x90a7, 0xac => 0x90a0, 0xad => 0x9630,
    0xae => 0x9628, 0xaf => 0x962f, 0xb0 => 0x962d, 0xb1 => 0x4e33,
    0xb2 => 0x4f98, 0xb3 => 0x4f7c, 0xb4 => 0x4f85, 0xb5 => 0x4f7d,
    0xb6 => 0x4f80, 0xb7 => 0x4f87, 0xb8 => 0x4f76, 0xb9 => 0x4f74,
    0xba => 0x4f89, 0xbb => 0x4f84, 0xbc => 0x4f77, 0xbd => 0x4f4c,
    0xbe => 0x4f97, 0xbf => 0x4f6a, 0xc0 => 0x4f9a, 0xc1 => 0x4f79,
    0xc2 => 0x4f81, 0xc3 => 0x4f78, 0xc4 => 0x4f90, 0xc5 => 0x4f9c,
    0xc6 => 0x4f94, 0xc7 => 0x4f9e, 0xc8 => 0x4f92, 0xc9 => 0x4f82,
    0xca => 0x4f95, 0xcb => 0x4f6b, 0xcc => 0x4f6e, 0xcd => 0x519e,
    0xce => 0x51bc, 0xcf => 0x51be, 0xd0 => 0x5235, 0xd1 => 0x5232,
    0xd2 => 0x5233, 0xd3 => 0x5246, 0xd4 => 0x5231, 0xd5 => 0x52bc,
    0xd6 => 0x530a, 0xd7 => 0x530b, 0xd8 => 0x533c, 0xd9 => 0x5392,
    0xda => 0x5394, 0xdb => 0x5487, 0xdc => 0x547f, 0xdd => 0x5481,
    0xde => 0x5491, 0xdf => 0x5482, 0xe0 => 0x5488, 0xe1 => 0x546b,
    0xe2 => 0x547a, 0xe3 => 0x547e, 0xe4 => 0x5465, 0xe5 => 0x546c,
    0xe6 => 0x5474, 0xe7 => 0x5466, 0xe8 => 0x548d, 0xe9 => 0x546f,
    0xea => 0x5461, 0xeb => 0x5460, 0xec => 0x5498, 0xed => 0x5463,
    0xee => 0x5467, 0xef => 0x5464, 0xf0 => 0x56f7, 0xf1 => 0x56f9,
    0xf2 => 0x576f, 0xf3 => 0x5772, 0xf4 => 0x576d, 0xf5 => 0x576b,
    0xf6 => 0x5771, 0xf7 => 0x5770, 0xf8 => 0x5776, 0xf9 => 0x5780,
    0xfa => 0x5775, 0xfb => 0x577b, 0xfc => 0x5773, 0xfd => 0x5774,
    0xfe => 0x5762,
  },
  0xcc => {
    0x40 => 0x5768, 0x41 => 0x577d, 0x42 => 0x590c, 0x43 => 0x5945,
    0x44 => 0x59b5, 0x45 => 0x59ba, 0x46 => 0x59cf, 0x47 => 0x59ce,
    0x48 => 0x59b2, 0x49 => 0x59cc, 0x4a => 0x59c1, 0x4b => 0x59b6,
    0x4c => 0x59bc, 0x4d => 0x59c3, 0x4e => 0x59d6, 0x4f => 0x59b1,
    0x50 => 0x59bd, 0x51 => 0x59c0, 0x52 => 0x59c8, 0x53 => 0x59b4,
    0x54 => 0x59c7, 0x55 => 0x5b62, 0x56 => 0x5b65, 0x57 => 0x5b93,
    0x58 => 0x5b95, 0x59 => 0x5c44, 0x5a => 0x5c47, 0x5b => 0x5cae,
    0x5c => 0x5ca4, 0x5d => 0x5ca0, 0x5e => 0x5cb5, 0x5f => 0x5caf,
    0x60 => 0x5ca8, 0x61 => 0x5cac, 0x62 => 0x5c9f, 0x63 => 0x5ca3,
    0x64 => 0x5cad, 0x65 => 0x5ca2, 0x66 => 0x5caa, 0x67 => 0x5ca7,
    0x68 => 0x5c9d, 0x69 => 0x5ca5, 0x6a => 0x5cb6, 0x6b => 0x5cb0,
    0x6c => 0x5ca6, 0x6d => 0x5e17, 0x6e => 0x5e14, 0x6f => 0x5e19,
    0x70 => 0x5f28, 0x71 => 0x5f22, 0x72 => 0x5f23, 0x73 => 0x5f24,
    0x74 => 0x5f54, 0x75 => 0x5f82, 0x76 => 0x5f7e, 0x77 => 0x5f7d,
    0x78 => 0x5fde, 0x79 => 0x5fe5, 0x7a => 0x602d, 0x7b => 0x6026,
    0x7c => 0x6019, 0x7d => 0x6032, 0x7e => 0x600b, 0xa1 => 0x6034,
    0xa2 => 0x600a, 0xa3 => 0x6017, 0xa4 => 0x6033, 0xa5 => 0x601a,
    0xa6 => 0x601e, 0xa7 => 0x602c, 0xa8 => 0x6022, 0xa9 => 0x600d,
    0xaa => 0x6010, 0xab => 0x602e, 0xac => 0x6013, 0xad => 0x6011,
    0xae => 0x600c, 0xaf => 0x6009, 0xb0 => 0x601c, 0xb1 => 0x6214,
    0xb2 => 0x623d, 0xb3 => 0x62ad, 0xb4 => 0x62b4, 0xb5 => 0x62d1,
    0xb6 => 0x62be, 0xb7 => 0x62aa, 0xb8 => 0x62b6, 0xb9 => 0x62ca,
    0xba => 0x62ae, 0xbb => 0x62b3, 0xbc => 0x62af, 0xbd => 0x62bb,
    0xbe => 0x62a9, 0xbf => 0x62b0, 0xc0 => 0x62b8, 0xc1 => 0x653d,
    0xc2 => 0x65a8, 0xc3 => 0x65bb, 0xc4 => 0x6609, 0xc5 => 0x65fc,
    0xc6 => 0x6604, 0xc7 => 0x6612, 0xc8 => 0x6608, 0xc9 => 0x65fb,
    0xca => 0x6603, 0xcb => 0x660b, 0xcc => 0x660d, 0xcd => 0x6605,
    0xce => 0x65fd, 0xcf => 0x6611, 0xd0 => 0x6610, 0xd1 => 0x66f6,
    0xd2 => 0x670a, 0xd3 => 0x6785, 0xd4 => 0x676c, 0xd5 => 0x678e,
    0xd6 => 0x6792, 0xd7 => 0x6776, 0xd8 => 0x677b, 0xd9 => 0x6798,
    0xda => 0x6786, 0xdb => 0x6784, 0xdc => 0x6774, 0xdd => 0x678d,
    0xde => 0x678c, 0xdf => 0x677a, 0xe0 => 0x679f, 0xe1 => 0x6791,
    0xe2 => 0x6799, 0xe3 => 0x6783, 0xe4 => 0x677d, 0xe5 => 0x6781,
    0xe6 => 0x6778, 0xe7 => 0x6779, 0xe8 => 0x6794, 0xe9 => 0x6b25,
    0xea => 0x6b80, 0xeb => 0x6b7e, 0xec => 0x6bde, 0xed => 0x6c1d,
    0xee => 0x6c93, 0xef => 0x6cec, 0xf0 => 0x6ceb, 0xf1 => 0x6cee,
    0xf2 => 0x6cd9, 0xf3 => 0x6cb6, 0xf4 => 0x6cd4, 0xf5 => 0x6cad,
    0xf6 => 0x6ce7, 0xf7 => 0x6cb7, 0xf8 => 0x6cd0, 0xf9 => 0x6cc2,
    0xfa => 0x6cba, 0xfb => 0x6cc3, 0xfc => 0x6cc6, 0xfd => 0x6ced,
    0xfe => 0x6cf2,
  },
  0xcd => {
    0x40 => 0x6cd2, 0x41 => 0x6cdd, 0x42 => 0x6cb4, 0x43 => 0x6c8a,
    0x44 => 0x6c9d, 0x45 => 0x6c80, 0x46 => 0x6cde, 0x47 => 0x6cc0,
    0x48 => 0x6d30, 0x49 => 0x6ccd, 0x4a => 0x6cc7, 0x4b => 0x6cb0,
    0x4c => 0x6cf9, 0x4d => 0x6ccf, 0x4e => 0x6ce9, 0x4f => 0x6cd1,
    0x50 => 0x7094, 0x51 => 0x7098, 0x52 => 0x7085, 0x53 => 0x7093,
    0x54 => 0x7086, 0x55 => 0x7084, 0x56 => 0x7091, 0x57 => 0x7096,
    0x58 => 0x7082, 0x59 => 0x709a, 0x5a => 0x7083, 0x5b => 0x726a,
    0x5c => 0x72d6, 0x5d => 0x72cb, 0x5e => 0x72d8, 0x5f => 0x72c9,
    0x60 => 0x72dc, 0x61 => 0x72d2, 0x62 => 0x72d4, 0x63 => 0x72da,
    0x64 => 0x72cc, 0x65 => 0x72d1, 0x66 => 0x73a4, 0x67 => 0x73a1,
    0x68 => 0x73ad, 0x69 => 0x73a6, 0x6a => 0x73a2, 0x6b => 0x73a0,
    0x6c => 0x73ac, 0x6d => 0x739d, 0x6e => 0x74dd, 0x6f => 0x74e8,
    0x70 => 0x753f, 0x71 => 0x7540, 0x72 => 0x753e, 0x73 => 0x758c,
    0x74 => 0x7598, 0x75 => 0x76af, 0x76 => 0x76f3, 0x77 => 0x76f1,
    0x78 => 0x76f0, 0x79 => 0x76f5, 0x7a => 0x77f8, 0x7b => 0x77fc,
    0x7c => 0x77f9, 0x7d => 0x77fb, 0x7e => 0x77fa, 0xa1 => 0x77f7,
    0xa2 => 0x7942, 0xa3 => 0x793f, 0xa4 => 0x79c5, 0xa5 => 0x7a78,
    0xa6 => 0x7a7b, 0xa7 => 0x7afb, 0xa8 => 0x7c75, 0xa9 => 0x7cfd,
    0xaa => 0x8035, 0xab => 0x808f, 0xac => 0x80ae, 0xad => 0x80a3,
    0xae => 0x80b8, 0xaf => 0x80b5, 0xb0 => 0x80ad, 0xb1 => 0x8220,
    0xb2 => 0x82a0, 0xb3 => 0x82c0, 0xb4 => 0x82ab, 0xb5 => 0x829a,
    0xb6 => 0x8298, 0xb7 => 0x829b, 0xb8 => 0x82b5, 0xb9 => 0x82a7,
    0xba => 0x82ae, 0xbb => 0x82bc, 0xbc => 0x829e, 0xbd => 0x82ba,
    0xbe => 0x82b4, 0xbf => 0x82a8, 0xc0 => 0x82a1, 0xc1 => 0x82a9,
    0xc2 => 0x82c2, 0xc3 => 0x82a4, 0xc4 => 0x82c3, 0xc5 => 0x82b6,
    0xc6 => 0x82a2, 0xc7 => 0x8670, 0xc8 => 0x866f, 0xc9 => 0x866d,
    0xca => 0x866e, 0xcb => 0x8c56, 0xcc => 0x8fd2, 0xcd => 0x8fcb,
    0xce => 0x8fd3, 0xcf => 0x8fcd, 0xd0 => 0x8fd6, 0xd1 => 0x8fd5,
    0xd2 => 0x8fd7, 0xd3 => 0x90b2, 0xd4 => 0x90b4, 0xd5 => 0x90af,
    0xd6 => 0x90b3, 0xd7 => 0x90b0, 0xd8 => 0x9639, 0xd9 => 0x963d,
    0xda => 0x963c, 0xdb => 0x963a, 0xdc => 0x9643, 0xdd => 0x4fcd,
    0xde => 0x4fc5, 0xdf => 0x4fd3, 0xe0 => 0x4fb2, 0xe1 => 0x4fc9,
    0xe2 => 0x4fcb, 0xe3 => 0x4fc1, 0xe4 => 0x4fd4, 0xe5 => 0x4fdc,
    0xe6 => 0x4fd9, 0xe7 => 0x4fbb, 0xe8 => 0x4fb3, 0xe9 => 0x4fdb,
    0xea => 0x4fc7, 0xeb => 0x4fd6, 0xec => 0x4fba, 0xed => 0x4fc0,
    0xee => 0x4fb9, 0xef => 0x4fec, 0xf0 => 0x5244, 0xf1 => 0x5249,
    0xf2 => 0x52c0, 0xf3 => 0x52c2, 0xf4 => 0x533d, 0xf5 => 0x537c,
    0xf6 => 0x5397, 0xf7 => 0x5396, 0xf8 => 0x5399, 0xf9 => 0x5398,
    0xfa => 0x54ba, 0xfb => 0x54a1, 0xfc => 0x54ad, 0xfd => 0x54a5,
    0xfe => 0x54cf,
  },
  0xce => {
    0x40 => 0x54c3, 0x41 => 0x830d, 0x42 => 0x54b7, 0x43 => 0x54ae,
    0x44 => 0x54d6, 0x45 => 0x54b6, 0x46 => 0x54c5, 0x47 => 0x54c6,
    0x48 => 0x54a0, 0x49 => 0x5470, 0x4a => 0x54bc, 0x4b => 0x54a2,
    0x4c => 0x54be, 0x4d => 0x5472, 0x4e => 0x54de, 0x4f => 0x54b0,
    0x50 => 0x57b5, 0x51 => 0x579e, 0x52 => 0x579f, 0x53 => 0x57a4,
    0x54 => 0x578c, 0x55 => 0x5797, 0x56 => 0x579d, 0x57 => 0x579b,
    0x58 => 0x5794, 0x59 => 0x5798, 0x5a => 0x578f, 0x5b => 0x5799,
    0x5c => 0x57a5, 0x5d => 0x579a, 0x5e => 0x5795, 0x5f => 0x58f4,
    0x60 => 0x590d, 0x61 => 0x5953, 0x62 => 0x59e1, 0x63 => 0x59de,
    0x64 => 0x59ee, 0x65 => 0x5a00, 0x66 => 0x59f1, 0x67 => 0x59dd,
    0x68 => 0x59fa, 0x69 => 0x59fd, 0x6a => 0x59fc, 0x6b => 0x59f6,
    0x6c => 0x59e4, 0x6d => 0x59f2, 0x6e => 0x59f7, 0x6f => 0x59db,
    0x70 => 0x59e9, 0x71 => 0x59f3, 0x72 => 0x59f5, 0x73 => 0x59e0,
    0x74 => 0x59fe, 0x75 => 0x59f4, 0x76 => 0x59ed, 0x77 => 0x5ba8,
    0x78 => 0x5c4c, 0x79 => 0x5cd0, 0x7a => 0x5cd8, 0x7b => 0x5ccc,
    0x7c => 0x5cd7, 0x7d => 0x5ccb, 0x7e => 0x5cdb, 0xa1 => 0x5cde,
    0xa2 => 0x5cda, 0xa3 => 0x5cc9, 0xa4 => 0x5cc7, 0xa5 => 0x5cca,
    0xa6 => 0x5cd6, 0xa7 => 0x5cd3, 0xa8 => 0x5cd4, 0xa9 => 0x5ccf,
    0xaa => 0x5cc8, 0xab => 0x5cc6, 0xac => 0x5cce, 0xad => 0x5cdf,
    0xae => 0x5cf8, 0xaf => 0x5df9, 0xb0 => 0x5e21, 0xb1 => 0x5e22,
    0xb2 => 0x5e23, 0xb3 => 0x5e20, 0xb4 => 0x5e24, 0xb5 => 0x5eb0,
    0xb6 => 0x5ea4, 0xb7 => 0x5ea2, 0xb8 => 0x5e9b, 0xb9 => 0x5ea3,
    0xba => 0x5ea5, 0xbb => 0x5f07, 0xbc => 0x5f2e, 0xbd => 0x5f56,
    0xbe => 0x5f86, 0xbf => 0x6037, 0xc0 => 0x6039, 0xc1 => 0x6054,
    0xc2 => 0x6072, 0xc3 => 0x605e, 0xc4 => 0x6045, 0xc5 => 0x6053,
    0xc6 => 0x6047, 0xc7 => 0x6049, 0xc8 => 0x605b, 0xc9 => 0x604c,
    0xca => 0x6040, 0xcb => 0x6042, 0xcc => 0x605f, 0xcd => 0x6024,
    0xce => 0x6044, 0xcf => 0x6058, 0xd0 => 0x6066, 0xd1 => 0x606e,
    0xd2 => 0x6242, 0xd3 => 0x6243, 0xd4 => 0x62cf, 0xd5 => 0x630d,
    0xd6 => 0x630b, 0xd7 => 0x62f5, 0xd8 => 0x630e, 0xd9 => 0x6303,
    0xda => 0x62eb, 0xdb => 0x62f9, 0xdc => 0x630f, 0xdd => 0x630c,
    0xde => 0x62f8, 0xdf => 0x62f6, 0xe0 => 0x6300, 0xe1 => 0x6313,
    0xe2 => 0x6314, 0xe3 => 0x62fa, 0xe4 => 0x6315, 0xe5 => 0x62fb,
    0xe6 => 0x62f0, 0xe7 => 0x6541, 0xe8 => 0x6543, 0xe9 => 0x65aa,
    0xea => 0x65bf, 0xeb => 0x6636, 0xec => 0x6621, 0xed => 0x6632,
    0xee => 0x6635, 0xef => 0x661c, 0xf0 => 0x6626, 0xf1 => 0x6622,
    0xf2 => 0x6633, 0xf3 => 0x662b, 0xf4 => 0x663a, 0xf5 => 0x661d,
    0xf6 => 0x6634, 0xf7 => 0x6639, 0xf8 => 0x662e, 0xf9 => 0x670f,
    0xfa => 0x6710, 0xfb => 0x67c1, 0xfc => 0x67f2, 0xfd => 0x67c8,
    0xfe => 0x67ba,
  },
  0xcf => {
    0x40 => 0x67dc, 0x41 => 0x67bb, 0x42 => 0x67f8, 0x43 => 0x67d8,
    0x44 => 0x67c0, 0x45 => 0x67b7, 0x46 => 0x67c5, 0x47 => 0x67eb,
    0x48 => 0x67e4, 0x49 => 0x67df, 0x4a => 0x67b5, 0x4b => 0x67cd,
    0x4c => 0x67b3, 0x4d => 0x67f7, 0x4e => 0x67f6, 0x4f => 0x67ee,
    0x50 => 0x67e3, 0x51 => 0x67c2, 0x52 => 0x67b9, 0x53 => 0x67ce,
    0x54 => 0x67e7, 0x55 => 0x67f0, 0x56 => 0x67b2, 0x57 => 0x67fc,
    0x58 => 0x67c6, 0x59 => 0x67ed, 0x5a => 0x67cc, 0x5b => 0x67ae,
    0x5c => 0x67e6, 0x5d => 0x67db, 0x5e => 0x67fa, 0x5f => 0x67c9,
    0x60 => 0x67ca, 0x61 => 0x67c3, 0x62 => 0x67ea, 0x63 => 0x67cb,
    0x64 => 0x6b28, 0x65 => 0x6b82, 0x66 => 0x6b84, 0x67 => 0x6bb6,
    0x68 => 0x6bd6, 0x69 => 0x6bd8, 0x6a => 0x6be0, 0x6b => 0x6c20,
    0x6c => 0x6c21, 0x6d => 0x6d28, 0x6e => 0x6d34, 0x6f => 0x6d2d,
    0x70 => 0x6d1f, 0x71 => 0x6d3c, 0x72 => 0x6d3f, 0x73 => 0x6d12,
    0x74 => 0x6d0a, 0x75 => 0x6cda, 0x76 => 0x6d33, 0x77 => 0x6d04,
    0x78 => 0x6d19, 0x79 => 0x6d3a, 0x7a => 0x6d1a, 0x7b => 0x6d11,
    0x7c => 0x6d00, 0x7d => 0x6d1d, 0x7e => 0x6d42, 0xa1 => 0x6d01,
    0xa2 => 0x6d18, 0xa3 => 0x6d37, 0xa4 => 0x6d03, 0xa5 => 0x6d0f,
    0xa6 => 0x6d40, 0xa7 => 0x6d07, 0xa8 => 0x6d20, 0xa9 => 0x6d2c,
    0xaa => 0x6d08, 0xab => 0x6d22, 0xac => 0x6d09, 0xad => 0x6d10,
    0xae => 0x70b7, 0xaf => 0x709f, 0xb0 => 0x70be, 0xb1 => 0x70b1,
    0xb2 => 0x70b0, 0xb3 => 0x70a1, 0xb4 => 0x70b4, 0xb5 => 0x70b5,
    0xb6 => 0x70a9, 0xb7 => 0x7241, 0xb8 => 0x7249, 0xb9 => 0x724a,
    0xba => 0x726c, 0xbb => 0x7270, 0xbc => 0x7273, 0xbd => 0x726e,
    0xbe => 0x72ca, 0xbf => 0x72e4, 0xc0 => 0x72e8, 0xc1 => 0x72eb,
    0xc2 => 0x72df, 0xc3 => 0x72ea, 0xc4 => 0x72e6, 0xc5 => 0x72e3,
    0xc6 => 0x7385, 0xc7 => 0x73cc, 0xc8 => 0x73c2, 0xc9 => 0x73c8,
    0xca => 0x73c5, 0xcb => 0x73b9, 0xcc => 0x73b6, 0xcd => 0x73b5,
    0xce => 0x73b4, 0xcf => 0x73eb, 0xd0 => 0x73bf, 0xd1 => 0x73c7,
    0xd2 => 0x73be, 0xd3 => 0x73c3, 0xd4 => 0x73c6, 0xd5 => 0x73b8,
    0xd6 => 0x73cb, 0xd7 => 0x74ec, 0xd8 => 0x74ee, 0xd9 => 0x752e,
    0xda => 0x7547, 0xdb => 0x7548, 0xdc => 0x75a7, 0xdd => 0x75aa,
    0xde => 0x7679, 0xdf => 0x76c4, 0xe0 => 0x7708, 0xe1 => 0x7703,
    0xe2 => 0x7704, 0xe3 => 0x7705, 0xe4 => 0x770a, 0xe5 => 0x76f7,
    0xe6 => 0x76fb, 0xe7 => 0x76fa, 0xe8 => 0x77e7, 0xe9 => 0x77e8,
    0xea => 0x7806, 0xeb => 0x7811, 0xec => 0x7812, 0xed => 0x7805,
    0xee => 0x7810, 0xef => 0x780f, 0xf0 => 0x780e, 0xf1 => 0x7809,
    0xf2 => 0x7803, 0xf3 => 0x7813, 0xf4 => 0x794a, 0xf5 => 0x794c,
    0xf6 => 0x794b, 0xf7 => 0x7945, 0xf8 => 0x7944, 0xf9 => 0x79d5,
    0xfa => 0x79cd, 0xfb => 0x79cf, 0xfc => 0x79d6, 0xfd => 0x79ce,
    0xfe => 0x7a80,
  },
  0xd0 => {
    0x40 => 0x7a7e, 0x41 => 0x7ad1, 0x42 => 0x7b00, 0x43 => 0x7b01,
    0x44 => 0x7c7a, 0x45 => 0x7c78, 0x46 => 0x7c79, 0x47 => 0x7c7f,
    0x48 => 0x7c80, 0x49 => 0x7c81, 0x4a => 0x7d03, 0x4b => 0x7d08,
    0x4c => 0x7d01, 0x4d => 0x7f58, 0x4e => 0x7f91, 0x4f => 0x7f8d,
    0x50 => 0x7fbe, 0x51 => 0x8007, 0x52 => 0x800e, 0x53 => 0x800f,
    0x54 => 0x8014, 0x55 => 0x8037, 0x56 => 0x80d8, 0x57 => 0x80c7,
    0x58 => 0x80e0, 0x59 => 0x80d1, 0x5a => 0x80c8, 0x5b => 0x80c2,
    0x5c => 0x80d0, 0x5d => 0x80c5, 0x5e => 0x80e3, 0x5f => 0x80d9,
    0x60 => 0x80dc, 0x61 => 0x80ca, 0x62 => 0x80d5, 0x63 => 0x80c9,
    0x64 => 0x80cf, 0x65 => 0x80d7, 0x66 => 0x80e6, 0x67 => 0x80cd,
    0x68 => 0x81ff, 0x69 => 0x8221, 0x6a => 0x8294, 0x6b => 0x82d9,
    0x6c => 0x82fe, 0x6d => 0x82f9, 0x6e => 0x8307, 0x6f => 0x82e8,
    0x70 => 0x8300, 0x71 => 0x82d5, 0x72 => 0x833a, 0x73 => 0x82eb,
    0x74 => 0x82d6, 0x75 => 0x82f4, 0x76 => 0x82ec, 0x77 => 0x82e1,
    0x78 => 0x82f2, 0x79 => 0x82f5, 0x7a => 0x830c, 0x7b => 0x82fb,
    0x7c => 0x82f6, 0x7d => 0x82f0, 0x7e => 0x82ea, 0xa1 => 0x82e4,
    0xa2 => 0x82e0, 0xa3 => 0x82fa, 0xa4 => 0x82f3, 0xa5 => 0x82ed,
    0xa6 => 0x8677, 0xa7 => 0x8674, 0xa8 => 0x867c, 0xa9 => 0x8673,
    0xaa => 0x8841, 0xab => 0x884e, 0xac => 0x8867, 0xad => 0x886a,
    0xae => 0x8869, 0xaf => 0x89d3, 0xb0 => 0x8a04, 0xb1 => 0x8a07,
    0xb2 => 0x8d72, 0xb3 => 0x8fe3, 0xb4 => 0x8fe1, 0xb5 => 0x8fee,
    0xb6 => 0x8fe0, 0xb7 => 0x90f1, 0xb8 => 0x90bd, 0xb9 => 0x90bf,
    0xba => 0x90d5, 0xbb => 0x90c5, 0xbc => 0x90be, 0xbd => 0x90c7,
    0xbe => 0x90cb, 0xbf => 0x90c8, 0xc0 => 0x91d4, 0xc1 => 0x91d3,
    0xc2 => 0x9654, 0xc3 => 0x964f, 0xc4 => 0x9651, 0xc5 => 0x9653,
    0xc6 => 0x964a, 0xc7 => 0x964e, 0xc8 => 0x501e, 0xc9 => 0x5005,
    0xca => 0x5007, 0xcb => 0x5013, 0xcc => 0x5022, 0xcd => 0x5030,
    0xce => 0x501b, 0xcf => 0x4ff5, 0xd0 => 0x4ff4, 0xd1 => 0x5033,
    0xd2 => 0x5037, 0xd3 => 0x502c, 0xd4 => 0x4ff6, 0xd5 => 0x4ff7,
    0xd6 => 0x5017, 0xd7 => 0x501c, 0xd8 => 0x5020, 0xd9 => 0x5027,
    0xda => 0x5035, 0xdb => 0x502f, 0xdc => 0x5031, 0xdd => 0x500e,
    0xde => 0x515a, 0xdf => 0x5194, 0xe0 => 0x5193, 0xe1 => 0x51ca,
    0xe2 => 0x51c4, 0xe3 => 0x51c5, 0xe4 => 0x51c8, 0xe5 => 0x51ce,
    0xe6 => 0x5261, 0xe7 => 0x525a, 0xe8 => 0x5252, 0xe9 => 0x525e,
    0xea => 0x525f, 0xeb => 0x5255, 0xec => 0x5262, 0xed => 0x52cd,
    0xee => 0x530e, 0xef => 0x539e, 0xf0 => 0x5526, 0xf1 => 0x54e2,
    0xf2 => 0x5517, 0xf3 => 0x5512, 0xf4 => 0x54e7, 0xf5 => 0x54f3,
    0xf6 => 0x54e4, 0xf7 => 0x551a, 0xf8 => 0x54ff, 0xf9 => 0x5504,
    0xfa => 0x5508, 0xfb => 0x54eb, 0xfc => 0x5511, 0xfd => 0x5505,
    0xfe => 0x54f1,
  },
  0xd1 => {
    0x40 => 0x550a, 0x41 => 0x54fb, 0x42 => 0x54f7, 0x43 => 0x54f8,
    0x44 => 0x54e0, 0x45 => 0x550e, 0x46 => 0x5503, 0x47 => 0x550b,
    0x48 => 0x5701, 0x49 => 0x5702, 0x4a => 0x57cc, 0x4b => 0x5832,
    0x4c => 0x57d5, 0x4d => 0x57d2, 0x4e => 0x57ba, 0x4f => 0x57c6,
    0x50 => 0x57bd, 0x51 => 0x57bc, 0x52 => 0x57b8, 0x53 => 0x57b6,
    0x54 => 0x57bf, 0x55 => 0x57c7, 0x56 => 0x57d0, 0x57 => 0x57b9,
    0x58 => 0x57c1, 0x59 => 0x590e, 0x5a => 0x594a, 0x5b => 0x5a19,
    0x5c => 0x5a16, 0x5d => 0x5a2d, 0x5e => 0x5a2e, 0x5f => 0x5a15,
    0x60 => 0x5a0f, 0x61 => 0x5a17, 0x62 => 0x5a0a, 0x63 => 0x5a1e,
    0x64 => 0x5a33, 0x65 => 0x5b6c, 0x66 => 0x5ba7, 0x67 => 0x5bad,
    0x68 => 0x5bac, 0x69 => 0x5c03, 0x6a => 0x5c56, 0x6b => 0x5c54,
    0x6c => 0x5cec, 0x6d => 0x5cff, 0x6e => 0x5cee, 0x6f => 0x5cf1,
    0x70 => 0x5cf7, 0x71 => 0x5d00, 0x72 => 0x5cf9, 0x73 => 0x5e29,
    0x74 => 0x5e28, 0x75 => 0x5ea8, 0x76 => 0x5eae, 0x77 => 0x5eaa,
    0x78 => 0x5eac, 0x79 => 0x5f33, 0x7a => 0x5f30, 0x7b => 0x5f67,
    0x7c => 0x605d, 0x7d => 0x605a, 0x7e => 0x6067, 0xa1 => 0x6041,
    0xa2 => 0x60a2, 0xa3 => 0x6088, 0xa4 => 0x6080, 0xa5 => 0x6092,
    0xa6 => 0x6081, 0xa7 => 0x609d, 0xa8 => 0x6083, 0xa9 => 0x6095,
    0xaa => 0x609b, 0xab => 0x6097, 0xac => 0x6087, 0xad => 0x609c,
    0xae => 0x608e, 0xaf => 0x6219, 0xb0 => 0x6246, 0xb1 => 0x62f2,
    0xb2 => 0x6310, 0xb3 => 0x6356, 0xb4 => 0x632c, 0xb5 => 0x6344,
    0xb6 => 0x6345, 0xb7 => 0x6336, 0xb8 => 0x6343, 0xb9 => 0x63e4,
    0xba => 0x6339, 0xbb => 0x634b, 0xbc => 0x634a, 0xbd => 0x633c,
    0xbe => 0x6329, 0xbf => 0x6341, 0xc0 => 0x6334, 0xc1 => 0x6358,
    0xc2 => 0x6354, 0xc3 => 0x6359, 0xc4 => 0x632d, 0xc5 => 0x6347,
    0xc6 => 0x6333, 0xc7 => 0x635a, 0xc8 => 0x6351, 0xc9 => 0x6338,
    0xca => 0x6357, 0xcb => 0x6340, 0xcc => 0x6348, 0xcd => 0x654a,
    0xce => 0x6546, 0xcf => 0x65c6, 0xd0 => 0x65c3, 0xd1 => 0x65c4,
    0xd2 => 0x65c2, 0xd3 => 0x664a, 0xd4 => 0x665f, 0xd5 => 0x6647,
    0xd6 => 0x6651, 0xd7 => 0x6712, 0xd8 => 0x6713, 0xd9 => 0x681f,
    0xda => 0x681a, 0xdb => 0x6849, 0xdc => 0x6832, 0xdd => 0x6833,
    0xde => 0x683b, 0xdf => 0x684b, 0xe0 => 0x684f, 0xe1 => 0x6816,
    0xe2 => 0x6831, 0xe3 => 0x681c, 0xe4 => 0x6835, 0xe5 => 0x682b,
    0xe6 => 0x682d, 0xe7 => 0x682f, 0xe8 => 0x684e, 0xe9 => 0x6844,
    0xea => 0x6834, 0xeb => 0x681d, 0xec => 0x6812, 0xed => 0x6814,
    0xee => 0x6826, 0xef => 0x6828, 0xf0 => 0x682e, 0xf1 => 0x684d,
    0xf2 => 0x683a, 0xf3 => 0x6825, 0xf4 => 0x6820, 0xf5 => 0x6b2c,
    0xf6 => 0x6b2f, 0xf7 => 0x6b2d, 0xf8 => 0x6b31, 0xf9 => 0x6b34,
    0xfa => 0x6b6d, 0xfb => 0x8082, 0xfc => 0x6b88, 0xfd => 0x6be6,
    0xfe => 0x6be4,
  },
  0xd2 => {
    0x40 => 0x6be8, 0x41 => 0x6be3, 0x42 => 0x6be2, 0x43 => 0x6be7,
    0x44 => 0x6c25, 0x45 => 0x6d7a, 0x46 => 0x6d63, 0x47 => 0x6d64,
    0x48 => 0x6d76, 0x49 => 0x6d0d, 0x4a => 0x6d61, 0x4b => 0x6d92,
    0x4c => 0x6d58, 0x4d => 0x6d62, 0x4e => 0x6d6d, 0x4f => 0x6d6f,
    0x50 => 0x6d91, 0x51 => 0x6d8d, 0x52 => 0x6def, 0x53 => 0x6d7f,
    0x54 => 0x6d86, 0x55 => 0x6d5e, 0x56 => 0x6d67, 0x57 => 0x6d60,
    0x58 => 0x6d97, 0x59 => 0x6d70, 0x5a => 0x6d7c, 0x5b => 0x6d5f,
    0x5c => 0x6d82, 0x5d => 0x6d98, 0x5e => 0x6d2f, 0x5f => 0x6d68,
    0x60 => 0x6d8b, 0x61 => 0x6d7e, 0x62 => 0x6d80, 0x63 => 0x6d84,
    0x64 => 0x6d16, 0x65 => 0x6d83, 0x66 => 0x6d7b, 0x67 => 0x6d7d,
    0x68 => 0x6d75, 0x69 => 0x6d90, 0x6a => 0x70dc, 0x6b => 0x70d3,
    0x6c => 0x70d1, 0x6d => 0x70dd, 0x6e => 0x70cb, 0x6f => 0x7f39,
    0x70 => 0x70e2, 0x71 => 0x70d7, 0x72 => 0x70d2, 0x73 => 0x70de,
    0x74 => 0x70e0, 0x75 => 0x70d4, 0x76 => 0x70cd, 0x77 => 0x70c5,
    0x78 => 0x70c6, 0x79 => 0x70c7, 0x7a => 0x70da, 0x7b => 0x70ce,
    0x7c => 0x70e1, 0x7d => 0x7242, 0x7e => 0x7278, 0xa1 => 0x7277,
    0xa2 => 0x7276, 0xa3 => 0x7300, 0xa4 => 0x72fa, 0xa5 => 0x72f4,
    0xa6 => 0x72fe, 0xa7 => 0x72f6, 0xa8 => 0x72f3, 0xa9 => 0x72fb,
    0xaa => 0x7301, 0xab => 0x73d3, 0xac => 0x73d9, 0xad => 0x73e5,
    0xae => 0x73d6, 0xaf => 0x73bc, 0xb0 => 0x73e7, 0xb1 => 0x73e3,
    0xb2 => 0x73e9, 0xb3 => 0x73dc, 0xb4 => 0x73d2, 0xb5 => 0x73db,
    0xb6 => 0x73d4, 0xb7 => 0x73dd, 0xb8 => 0x73da, 0xb9 => 0x73d7,
    0xba => 0x73d8, 0xbb => 0x73e8, 0xbc => 0x74de, 0xbd => 0x74df,
    0xbe => 0x74f4, 0xbf => 0x74f5, 0xc0 => 0x7521, 0xc1 => 0x755b,
    0xc2 => 0x755f, 0xc3 => 0x75b0, 0xc4 => 0x75c1, 0xc5 => 0x75bb,
    0xc6 => 0x75c4, 0xc7 => 0x75c0, 0xc8 => 0x75bf, 0xc9 => 0x75b6,
    0xca => 0x75ba, 0xcb => 0x768a, 0xcc => 0x76c9, 0xcd => 0x771d,
    0xce => 0x771b, 0xcf => 0x7710, 0xd0 => 0x7713, 0xd1 => 0x7712,
    0xd2 => 0x7723, 0xd3 => 0x7711, 0xd4 => 0x7715, 0xd5 => 0x7719,
    0xd6 => 0x771a, 0xd7 => 0x7722, 0xd8 => 0x7727, 0xd9 => 0x7823,
    0xda => 0x782c, 0xdb => 0x7822, 0xdc => 0x7835, 0xdd => 0x782f,
    0xde => 0x7828, 0xdf => 0x782e, 0xe0 => 0x782b, 0xe1 => 0x7821,
    0xe2 => 0x7829, 0xe3 => 0x7833, 0xe4 => 0x782a, 0xe5 => 0x7831,
    0xe6 => 0x7954, 0xe7 => 0x795b, 0xe8 => 0x794f, 0xe9 => 0x795c,
    0xea => 0x7953, 0xeb => 0x7952, 0xec => 0x7951, 0xed => 0x79eb,
    0xee => 0x79ec, 0xef => 0x79e0, 0xf0 => 0x79ee, 0xf1 => 0x79ed,
    0xf2 => 0x79ea, 0xf3 => 0x79dc, 0xf4 => 0x79de, 0xf5 => 0x79dd,
    0xf6 => 0x7a86, 0xf7 => 0x7a89, 0xf8 => 0x7a85, 0xf9 => 0x7a8b,
    0xfa => 0x7a8c, 0xfb => 0x7a8a, 0xfc => 0x7a87, 0xfd => 0x7ad8,
    0xfe => 0x7b10,
  },
  0xd3 => {
    0x40 => 0x7b04, 0x41 => 0x7b13, 0x42 => 0x7b05, 0x43 => 0x7b0f,
    0x44 => 0x7b08, 0x45 => 0x7b0a, 0x46 => 0x7b0e, 0x47 => 0x7b09,
    0x48 => 0x7b12, 0x49 => 0x7c84, 0x4a => 0x7c91, 0x4b => 0x7c8a,
    0x4c => 0x7c8c, 0x4d => 0x7c88, 0x4e => 0x7c8d, 0x4f => 0x7c85,
    0x50 => 0x7d1e, 0x51 => 0x7d1d, 0x52 => 0x7d11, 0x53 => 0x7d0e,
    0x54 => 0x7d18, 0x55 => 0x7d16, 0x56 => 0x7d13, 0x57 => 0x7d1f,
    0x58 => 0x7d12, 0x59 => 0x7d0f, 0x5a => 0x7d0c, 0x5b => 0x7f5c,
    0x5c => 0x7f61, 0x5d => 0x7f5e, 0x5e => 0x7f60, 0x5f => 0x7f5d,
    0x60 => 0x7f5b, 0x61 => 0x7f96, 0x62 => 0x7f92, 0x63 => 0x7fc3,
    0x64 => 0x7fc2, 0x65 => 0x7fc0, 0x66 => 0x8016, 0x67 => 0x803e,
    0x68 => 0x8039, 0x69 => 0x80fa, 0x6a => 0x80f2, 0x6b => 0x80f9,
    0x6c => 0x80f5, 0x6d => 0x8101, 0x6e => 0x80fb, 0x6f => 0x8100,
    0x70 => 0x8201, 0x71 => 0x822f, 0x72 => 0x8225, 0x73 => 0x8333,
    0x74 => 0x832d, 0x75 => 0x8344, 0x76 => 0x8319, 0x77 => 0x8351,
    0x78 => 0x8325, 0x79 => 0x8356, 0x7a => 0x833f, 0x7b => 0x8341,
    0x7c => 0x8326, 0x7d => 0x831c, 0x7e => 0x8322, 0xa1 => 0x8342,
    0xa2 => 0x834e, 0xa3 => 0x831b, 0xa4 => 0x832a, 0xa5 => 0x8308,
    0xa6 => 0x833c, 0xa7 => 0x834d, 0xa8 => 0x8316, 0xa9 => 0x8324,
    0xaa => 0x8320, 0xab => 0x8337, 0xac => 0x832f, 0xad => 0x8329,
    0xae => 0x8347, 0xaf => 0x8345, 0xb0 => 0x834c, 0xb1 => 0x8353,
    0xb2 => 0x831e, 0xb3 => 0x832c, 0xb4 => 0x834b, 0xb5 => 0x8327,
    0xb6 => 0x8348, 0xb7 => 0x8653, 0xb8 => 0x8652, 0xb9 => 0x86a2,
    0xba => 0x86a8, 0xbb => 0x8696, 0xbc => 0x868d, 0xbd => 0x8691,
    0xbe => 0x869e, 0xbf => 0x8687, 0xc0 => 0x8697, 0xc1 => 0x8686,
    0xc2 => 0x868b, 0xc3 => 0x869a, 0xc4 => 0x8685, 0xc5 => 0x86a5,
    0xc6 => 0x8699, 0xc7 => 0x86a1, 0xc8 => 0x86a7, 0xc9 => 0x8695,
    0xca => 0x8698, 0xcb => 0x868e, 0xcc => 0x869d, 0xcd => 0x8690,
    0xce => 0x8694, 0xcf => 0x8843, 0xd0 => 0x8844, 0xd1 => 0x886d,
    0xd2 => 0x8875, 0xd3 => 0x8876, 0xd4 => 0x8872, 0xd5 => 0x8880,
    0xd6 => 0x8871, 0xd7 => 0x887f, 0xd8 => 0x886f, 0xd9 => 0x8883,
    0xda => 0x887e, 0xdb => 0x8874, 0xdc => 0x887c, 0xdd => 0x8a12,
    0xde => 0x8c47, 0xdf => 0x8c57, 0xe0 => 0x8c7b, 0xe1 => 0x8ca4,
    0xe2 => 0x8ca3, 0xe3 => 0x8d76, 0xe4 => 0x8d78, 0xe5 => 0x8db5,
    0xe6 => 0x8db7, 0xe7 => 0x8db6, 0xe8 => 0x8ed1, 0xe9 => 0x8ed3,
    0xea => 0x8ffe, 0xeb => 0x8ff5, 0xec => 0x9002, 0xed => 0x8fff,
    0xee => 0x8ffb, 0xef => 0x9004, 0xf0 => 0x8ffc, 0xf1 => 0x8ff6,
    0xf2 => 0x90d6, 0xf3 => 0x90e0, 0xf4 => 0x90d9, 0xf5 => 0x90da,
    0xf6 => 0x90e3, 0xf7 => 0x90df, 0xf8 => 0x90e5, 0xf9 => 0x90d8,
    0xfa => 0x90db, 0xfb => 0x90d7, 0xfc => 0x90dc, 0xfd => 0x90e4,
    0xfe => 0x9150,
  },
  0xd4 => {
    0x40 => 0x914e, 0x41 => 0x914f, 0x42 => 0x91d5, 0x43 => 0x91e2,
    0x44 => 0x91da, 0x45 => 0x965c, 0x46 => 0x965f, 0x47 => 0x96bc,
    0x48 => 0x98e3, 0x49 => 0x9adf, 0x4a => 0x9b2f, 0x4b => 0x4e7f,
    0x4c => 0x5070, 0x4d => 0x506a, 0x4e => 0x5061, 0x4f => 0x505e,
    0x50 => 0x5060, 0x51 => 0x5053, 0x52 => 0x504b, 0x53 => 0x505d,
    0x54 => 0x5072, 0x55 => 0x5048, 0x56 => 0x504d, 0x57 => 0x5041,
    0x58 => 0x505b, 0x59 => 0x504a, 0x5a => 0x5062, 0x5b => 0x5015,
    0x5c => 0x5045, 0x5d => 0x505f, 0x5e => 0x5069, 0x5f => 0x506b,
    0x60 => 0x5063, 0x61 => 0x5064, 0x62 => 0x5046, 0x63 => 0x5040,
    0x64 => 0x506e, 0x65 => 0x5073, 0x66 => 0x5057, 0x67 => 0x5051,
    0x68 => 0x51d0, 0x69 => 0x526b, 0x6a => 0x526d, 0x6b => 0x526c,
    0x6c => 0x526e, 0x6d => 0x52d6, 0x6e => 0x52d3, 0x6f => 0x532d,
    0x70 => 0x539c, 0x71 => 0x5575, 0x72 => 0x5576, 0x73 => 0x553c,
    0x74 => 0x554d, 0x75 => 0x5550, 0x76 => 0x5534, 0x77 => 0x552a,
    0x78 => 0x5551, 0x79 => 0x5562, 0x7a => 0x5536, 0x7b => 0x5535,
    0x7c => 0x5530, 0x7d => 0x5552, 0x7e => 0x5545, 0xa1 => 0x550c,
    0xa2 => 0x5532, 0xa3 => 0x5565, 0xa4 => 0x554e, 0xa5 => 0x5539,
    0xa6 => 0x5548, 0xa7 => 0x552d, 0xa8 => 0x553b, 0xa9 => 0x5540,
    0xaa => 0x554b, 0xab => 0x570a, 0xac => 0x5707, 0xad => 0x57fb,
    0xae => 0x5814, 0xaf => 0x57e2, 0xb0 => 0x57f6, 0xb1 => 0x57dc,
    0xb2 => 0x57f4, 0xb3 => 0x5800, 0xb4 => 0x57ed, 0xb5 => 0x57fd,
    0xb6 => 0x5808, 0xb7 => 0x57f8, 0xb8 => 0x580b, 0xb9 => 0x57f3,
    0xba => 0x57cf, 0xbb => 0x5807, 0xbc => 0x57ee, 0xbd => 0x57e3,
    0xbe => 0x57f2, 0xbf => 0x57e5, 0xc0 => 0x57ec, 0xc1 => 0x57e1,
    0xc2 => 0x580e, 0xc3 => 0x57fc, 0xc4 => 0x5810, 0xc5 => 0x57e7,
    0xc6 => 0x5801, 0xc7 => 0x580c, 0xc8 => 0x57f1, 0xc9 => 0x57e9,
    0xca => 0x57f0, 0xcb => 0x580d, 0xcc => 0x5804, 0xcd => 0x595c,
    0xce => 0x5a60, 0xcf => 0x5a58, 0xd0 => 0x5a55, 0xd1 => 0x5a67,
    0xd2 => 0x5a5e, 0xd3 => 0x5a38, 0xd4 => 0x5a35, 0xd5 => 0x5a6d,
    0xd6 => 0x5a50, 0xd7 => 0x5a5f, 0xd8 => 0x5a65, 0xd9 => 0x5a6c,
    0xda => 0x5a53, 0xdb => 0x5a64, 0xdc => 0x5a57, 0xdd => 0x5a43,
    0xde => 0x5a5d, 0xdf => 0x5a52, 0xe0 => 0x5a44, 0xe1 => 0x5a5b,
    0xe2 => 0x5a48, 0xe3 => 0x5a8e, 0xe4 => 0x5a3e, 0xe5 => 0x5a4d,
    0xe6 => 0x5a39, 0xe7 => 0x5a4c, 0xe8 => 0x5a70, 0xe9 => 0x5a69,
    0xea => 0x5a47, 0xeb => 0x5a51, 0xec => 0x5a56, 0xed => 0x5a42,
    0xee => 0x5a5c, 0xef => 0x5b72, 0xf0 => 0x5b6e, 0xf1 => 0x5bc1,
    0xf2 => 0x5bc0, 0xf3 => 0x5c59, 0xf4 => 0x5d1e, 0xf5 => 0x5d0b,
    0xf6 => 0x5d1d, 0xf7 => 0x5d1a, 0xf8 => 0x5d20, 0xf9 => 0x5d0c,
    0xfa => 0x5d28, 0xfb => 0x5d0d, 0xfc => 0x5d26, 0xfd => 0x5d25,
    0xfe => 0x5d0f,
  },
  0xd5 => {
    0x40 => 0x5d30, 0x41 => 0x5d12, 0x42 => 0x5d23, 0x43 => 0x5d1f,
    0x44 => 0x5d2e, 0x45 => 0x5e3e, 0x46 => 0x5e34, 0x47 => 0x5eb1,
    0x48 => 0x5eb4, 0x49 => 0x5eb9, 0x4a => 0x5eb2, 0x4b => 0x5eb3,
    0x4c => 0x5f36, 0x4d => 0x5f38, 0x4e => 0x5f9b, 0x4f => 0x5f96,
    0x50 => 0x5f9f, 0x51 => 0x608a, 0x52 => 0x6090, 0x53 => 0x6086,
    0x54 => 0x60be, 0x55 => 0x60b0, 0x56 => 0x60ba, 0x57 => 0x60d3,
    0x58 => 0x60d4, 0x59 => 0x60cf, 0x5a => 0x60e4, 0x5b => 0x60d9,
    0x5c => 0x60dd, 0x5d => 0x60c8, 0x5e => 0x60b1, 0x5f => 0x60db,
    0x60 => 0x60b7, 0x61 => 0x60ca, 0x62 => 0x60bf, 0x63 => 0x60c3,
    0x64 => 0x60cd, 0x65 => 0x60c0, 0x66 => 0x6332, 0x67 => 0x6365,
    0x68 => 0x638a, 0x69 => 0x6382, 0x6a => 0x637d, 0x6b => 0x63bd,
    0x6c => 0x639e, 0x6d => 0x63ad, 0x6e => 0x639d, 0x6f => 0x6397,
    0x70 => 0x63ab, 0x71 => 0x638e, 0x72 => 0x636f, 0x73 => 0x6387,
    0x74 => 0x6390, 0x75 => 0x636e, 0x76 => 0x63af, 0x77 => 0x6375,
    0x78 => 0x639c, 0x79 => 0x636d, 0x7a => 0x63ae, 0x7b => 0x637c,
    0x7c => 0x63a4, 0x7d => 0x633b, 0x7e => 0x639f, 0xa1 => 0x6378,
    0xa2 => 0x6385, 0xa3 => 0x6381, 0xa4 => 0x6391, 0xa5 => 0x638d,
    0xa6 => 0x6370, 0xa7 => 0x6553, 0xa8 => 0x65cd, 0xa9 => 0x6665,
    0xaa => 0x6661, 0xab => 0x665b, 0xac => 0x6659, 0xad => 0x665c,
    0xae => 0x6662, 0xaf => 0x6718, 0xb0 => 0x6879, 0xb1 => 0x6887,
    0xb2 => 0x6890, 0xb3 => 0x689c, 0xb4 => 0x686d, 0xb5 => 0x686e,
    0xb6 => 0x68ae, 0xb7 => 0x68ab, 0xb8 => 0x6956, 0xb9 => 0x686f,
    0xba => 0x68a3, 0xbb => 0x68ac, 0xbc => 0x68a9, 0xbd => 0x6875,
    0xbe => 0x6874, 0xbf => 0x68b2, 0xc0 => 0x688f, 0xc1 => 0x6877,
    0xc2 => 0x6892, 0xc3 => 0x687c, 0xc4 => 0x686b, 0xc5 => 0x6872,
    0xc6 => 0x68aa, 0xc7 => 0x6880, 0xc8 => 0x6871, 0xc9 => 0x687e,
    0xca => 0x689b, 0xcb => 0x6896, 0xcc => 0x688b, 0xcd => 0x68a0,
    0xce => 0x6889, 0xcf => 0x68a4, 0xd0 => 0x6878, 0xd1 => 0x687b,
    0xd2 => 0x6891, 0xd3 => 0x688c, 0xd4 => 0x688a, 0xd5 => 0x687d,
    0xd6 => 0x6b36, 0xd7 => 0x6b33, 0xd8 => 0x6b37, 0xd9 => 0x6b38,
    0xda => 0x6b91, 0xdb => 0x6b8f, 0xdc => 0x6b8d, 0xdd => 0x6b8e,
    0xde => 0x6b8c, 0xdf => 0x6c2a, 0xe0 => 0x6dc0, 0xe1 => 0x6dab,
    0xe2 => 0x6db4, 0xe3 => 0x6db3, 0xe4 => 0x6e74, 0xe5 => 0x6dac,
    0xe6 => 0x6de9, 0xe7 => 0x6de2, 0xe8 => 0x6db7, 0xe9 => 0x6df6,
    0xea => 0x6dd4, 0xeb => 0x6e00, 0xec => 0x6dc8, 0xed => 0x6de0,
    0xee => 0x6ddf, 0xef => 0x6dd6, 0xf0 => 0x6dbe, 0xf1 => 0x6de5,
    0xf2 => 0x6ddc, 0xf3 => 0x6ddd, 0xf4 => 0x6ddb, 0xf5 => 0x6df4,
    0xf6 => 0x6dca, 0xf7 => 0x6dbd, 0xf8 => 0x6ded, 0xf9 => 0x6df0,
    0xfa => 0x6dba, 0xfb => 0x6dd5, 0xfc => 0x6dc2, 0xfd => 0x6dcf,
    0xfe => 0x6dc9,
  },
  0xd6 => {
    0x40 => 0x6dd0, 0x41 => 0x6df2, 0x42 => 0x6dd3, 0x43 => 0x6dfd,
    0x44 => 0x6dd7, 0x45 => 0x6dcd, 0x46 => 0x6de3, 0x47 => 0x6dbb,
    0x48 => 0x70fa, 0x49 => 0x710d, 0x4a => 0x70f7, 0x4b => 0x7117,
    0x4c => 0x70f4, 0x4d => 0x710c, 0x4e => 0x70f0, 0x4f => 0x7104,
    0x50 => 0x70f3, 0x51 => 0x7110, 0x52 => 0x70fc, 0x53 => 0x70ff,
    0x54 => 0x7106, 0x55 => 0x7113, 0x56 => 0x7100, 0x57 => 0x70f8,
    0x58 => 0x70f6, 0x59 => 0x710b, 0x5a => 0x7102, 0x5b => 0x710e,
    0x5c => 0x727e, 0x5d => 0x727b, 0x5e => 0x727c, 0x5f => 0x727f,
    0x60 => 0x731d, 0x61 => 0x7317, 0x62 => 0x7307, 0x63 => 0x7311,
    0x64 => 0x7318, 0x65 => 0x730a, 0x66 => 0x7308, 0x67 => 0x72ff,
    0x68 => 0x730f, 0x69 => 0x731e, 0x6a => 0x7388, 0x6b => 0x73f6,
    0x6c => 0x73f8, 0x6d => 0x73f5, 0x6e => 0x7404, 0x6f => 0x7401,
    0x70 => 0x73fd, 0x71 => 0x7407, 0x72 => 0x7400, 0x73 => 0x73fa,
    0x74 => 0x73fc, 0x75 => 0x73ff, 0x76 => 0x740c, 0x77 => 0x740b,
    0x78 => 0x73f4, 0x79 => 0x7408, 0x7a => 0x7564, 0x7b => 0x7563,
    0x7c => 0x75ce, 0x7d => 0x75d2, 0x7e => 0x75cf, 0xa1 => 0x75cb,
    0xa2 => 0x75cc, 0xa3 => 0x75d1, 0xa4 => 0x75d0, 0xa5 => 0x768f,
    0xa6 => 0x7689, 0xa7 => 0x76d3, 0xa8 => 0x7739, 0xa9 => 0x772f,
    0xaa => 0x772d, 0xab => 0x7731, 0xac => 0x7732, 0xad => 0x7734,
    0xae => 0x7733, 0xaf => 0x773d, 0xb0 => 0x7725, 0xb1 => 0x773b,
    0xb2 => 0x7735, 0xb3 => 0x7848, 0xb4 => 0x7852, 0xb5 => 0x7849,
    0xb6 => 0x784d, 0xb7 => 0x784a, 0xb8 => 0x784c, 0xb9 => 0x7826,
    0xba => 0x7845, 0xbb => 0x7850, 0xbc => 0x7964, 0xbd => 0x7967,
    0xbe => 0x7969, 0xbf => 0x796a, 0xc0 => 0x7963, 0xc1 => 0x796b,
    0xc2 => 0x7961, 0xc3 => 0x79bb, 0xc4 => 0x79fa, 0xc5 => 0x79f8,
    0xc6 => 0x79f6, 0xc7 => 0x79f7, 0xc8 => 0x7a8f, 0xc9 => 0x7a94,
    0xca => 0x7a90, 0xcb => 0x7b35, 0xcc => 0x7b47, 0xcd => 0x7b34,
    0xce => 0x7b25, 0xcf => 0x7b30, 0xd0 => 0x7b22, 0xd1 => 0x7b24,
    0xd2 => 0x7b33, 0xd3 => 0x7b18, 0xd4 => 0x7b2a, 0xd5 => 0x7b1d,
    0xd6 => 0x7b31, 0xd7 => 0x7b2b, 0xd8 => 0x7b2d, 0xd9 => 0x7b2f,
    0xda => 0x7b32, 0xdb => 0x7b38, 0xdc => 0x7b1a, 0xdd => 0x7b23,
    0xde => 0x7c94, 0xdf => 0x7c98, 0xe0 => 0x7c96, 0xe1 => 0x7ca3,
    0xe2 => 0x7d35, 0xe3 => 0x7d3d, 0xe4 => 0x7d38, 0xe5 => 0x7d36,
    0xe6 => 0x7d3a, 0xe7 => 0x7d45, 0xe8 => 0x7d2c, 0xe9 => 0x7d29,
    0xea => 0x7d41, 0xeb => 0x7d47, 0xec => 0x7d3e, 0xed => 0x7d3f,
    0xee => 0x7d4a, 0xef => 0x7d3b, 0xf0 => 0x7d28, 0xf1 => 0x7f63,
    0xf2 => 0x7f95, 0xf3 => 0x7f9c, 0xf4 => 0x7f9d, 0xf5 => 0x7f9b,
    0xf6 => 0x7fca, 0xf7 => 0x7fcb, 0xf8 => 0x7fcd, 0xf9 => 0x7fd0,
    0xfa => 0x7fd1, 0xfb => 0x7fc7, 0xfc => 0x7fcf, 0xfd => 0x7fc9,
    0xfe => 0x801f,
  },
  0xd7 => {
    0x40 => 0x801e, 0x41 => 0x801b, 0x42 => 0x8047, 0x43 => 0x8043,
    0x44 => 0x8048, 0x45 => 0x8118, 0x46 => 0x8125, 0x47 => 0x8119,
    0x48 => 0x811b, 0x49 => 0x812d, 0x4a => 0x811f, 0x4b => 0x812c,
    0x4c => 0x811e, 0x4d => 0x8121, 0x4e => 0x8115, 0x4f => 0x8127,
    0x50 => 0x811d, 0x51 => 0x8122, 0x52 => 0x8211, 0x53 => 0x8238,
    0x54 => 0x8233, 0x55 => 0x823a, 0x56 => 0x8234, 0x57 => 0x8232,
    0x58 => 0x8274, 0x59 => 0x8390, 0x5a => 0x83a3, 0x5b => 0x83a8,
    0x5c => 0x838d, 0x5d => 0x837a, 0x5e => 0x8373, 0x5f => 0x83a4,
    0x60 => 0x8374, 0x61 => 0x838f, 0x62 => 0x8381, 0x63 => 0x8395,
    0x64 => 0x8399, 0x65 => 0x8375, 0x66 => 0x8394, 0x67 => 0x83a9,
    0x68 => 0x837d, 0x69 => 0x8383, 0x6a => 0x838c, 0x6b => 0x839d,
    0x6c => 0x839b, 0x6d => 0x83aa, 0x6e => 0x838b, 0x6f => 0x837e,
    0x70 => 0x83a5, 0x71 => 0x83af, 0x72 => 0x8388, 0x73 => 0x8397,
    0x74 => 0x83b0, 0x75 => 0x837f, 0x76 => 0x83a6, 0x77 => 0x8387,
    0x78 => 0x83ae, 0x79 => 0x8376, 0x7a => 0x839a, 0x7b => 0x8659,
    0x7c => 0x8656, 0x7d => 0x86bf, 0x7e => 0x86b7, 0xa1 => 0x86c2,
    0xa2 => 0x86c1, 0xa3 => 0x86c5, 0xa4 => 0x86ba, 0xa5 => 0x86b0,
    0xa6 => 0x86c8, 0xa7 => 0x86b9, 0xa8 => 0x86b3, 0xa9 => 0x86b8,
    0xaa => 0x86cc, 0xab => 0x86b4, 0xac => 0x86bb, 0xad => 0x86bc,
    0xae => 0x86c3, 0xaf => 0x86bd, 0xb0 => 0x86be, 0xb1 => 0x8852,
    0xb2 => 0x8889, 0xb3 => 0x8895, 0xb4 => 0x88a8, 0xb5 => 0x88a2,
    0xb6 => 0x88aa, 0xb7 => 0x889a, 0xb8 => 0x8891, 0xb9 => 0x88a1,
    0xba => 0x889f, 0xbb => 0x8898, 0xbc => 0x88a7, 0xbd => 0x8899,
    0xbe => 0x889b, 0xbf => 0x8897, 0xc0 => 0x88a4, 0xc1 => 0x88ac,
    0xc2 => 0x888c, 0xc3 => 0x8893, 0xc4 => 0x888e, 0xc5 => 0x8982,
    0xc6 => 0x89d6, 0xc7 => 0x89d9, 0xc8 => 0x89d5, 0xc9 => 0x8a30,
    0xca => 0x8a27, 0xcb => 0x8a2c, 0xcc => 0x8a1e, 0xcd => 0x8c39,
    0xce => 0x8c3b, 0xcf => 0x8c5c, 0xd0 => 0x8c5d, 0xd1 => 0x8c7d,
    0xd2 => 0x8ca5, 0xd3 => 0x8d7d, 0xd4 => 0x8d7b, 0xd5 => 0x8d79,
    0xd6 => 0x8dbc, 0xd7 => 0x8dc2, 0xd8 => 0x8db9, 0xd9 => 0x8dbf,
    0xda => 0x8dc1, 0xdb => 0x8ed8, 0xdc => 0x8ede, 0xdd => 0x8edd,
    0xde => 0x8edc, 0xdf => 0x8ed7, 0xe0 => 0x8ee0, 0xe1 => 0x8ee1,
    0xe2 => 0x9024, 0xe3 => 0x900b, 0xe4 => 0x9011, 0xe5 => 0x901c,
    0xe6 => 0x900c, 0xe7 => 0x9021, 0xe8 => 0x90ef, 0xe9 => 0x90ea,
    0xea => 0x90f0, 0xeb => 0x90f4, 0xec => 0x90f2, 0xed => 0x90f3,
    0xee => 0x90d4, 0xef => 0x90eb, 0xf0 => 0x90ec, 0xf1 => 0x90e9,
    0xf2 => 0x9156, 0xf3 => 0x9158, 0xf4 => 0x915a, 0xf5 => 0x9153,
    0xf6 => 0x9155, 0xf7 => 0x91ec, 0xf8 => 0x91f4, 0xf9 => 0x91f1,
    0xfa => 0x91f3, 0xfb => 0x91f8, 0xfc => 0x91e4, 0xfd => 0x91f9,
    0xfe => 0x91ea,
  },
  0xd8 => {
    0x40 => 0x91eb, 0x41 => 0x91f7, 0x42 => 0x91e8, 0x43 => 0x91ee,
    0x44 => 0x957a, 0x45 => 0x9586, 0x46 => 0x9588, 0x47 => 0x967c,
    0x48 => 0x966d, 0x49 => 0x966b, 0x4a => 0x9671, 0x4b => 0x966f,
    0x4c => 0x96bf, 0x4d => 0x976a, 0x4e => 0x9804, 0x4f => 0x98e5,
    0x50 => 0x9997, 0x51 => 0x509b, 0x52 => 0x5095, 0x53 => 0x5094,
    0x54 => 0x509e, 0x55 => 0x508b, 0x56 => 0x50a3, 0x57 => 0x5083,
    0x58 => 0x508c, 0x59 => 0x508e, 0x5a => 0x509d, 0x5b => 0x5068,
    0x5c => 0x509c, 0x5d => 0x5092, 0x5e => 0x5082, 0x5f => 0x5087,
    0x60 => 0x515f, 0x61 => 0x51d4, 0x62 => 0x5312, 0x63 => 0x5311,
    0x64 => 0x53a4, 0x65 => 0x53a7, 0x66 => 0x5591, 0x67 => 0x55a8,
    0x68 => 0x55a5, 0x69 => 0x55ad, 0x6a => 0x5577, 0x6b => 0x5645,
    0x6c => 0x55a2, 0x6d => 0x5593, 0x6e => 0x5588, 0x6f => 0x558f,
    0x70 => 0x55b5, 0x71 => 0x5581, 0x72 => 0x55a3, 0x73 => 0x5592,
    0x74 => 0x55a4, 0x75 => 0x557d, 0x76 => 0x558c, 0x77 => 0x55a6,
    0x78 => 0x557f, 0x79 => 0x5595, 0x7a => 0x55a1, 0x7b => 0x558e,
    0x7c => 0x570c, 0x7d => 0x5829, 0x7e => 0x5837, 0xa1 => 0x5819,
    0xa2 => 0x581e, 0xa3 => 0x5827, 0xa4 => 0x5823, 0xa5 => 0x5828,
    0xa6 => 0x57f5, 0xa7 => 0x5848, 0xa8 => 0x5825, 0xa9 => 0x581c,
    0xaa => 0x581b, 0xab => 0x5833, 0xac => 0x583f, 0xad => 0x5836,
    0xae => 0x582e, 0xaf => 0x5839, 0xb0 => 0x5838, 0xb1 => 0x582d,
    0xb2 => 0x582c, 0xb3 => 0x583b, 0xb4 => 0x5961, 0xb5 => 0x5aaf,
    0xb6 => 0x5a94, 0xb7 => 0x5a9f, 0xb8 => 0x5a7a, 0xb9 => 0x5aa2,
    0xba => 0x5a9e, 0xbb => 0x5a78, 0xbc => 0x5aa6, 0xbd => 0x5a7c,
    0xbe => 0x5aa5, 0xbf => 0x5aac, 0xc0 => 0x5a95, 0xc1 => 0x5aae,
    0xc2 => 0x5a37, 0xc3 => 0x5a84, 0xc4 => 0x5a8a, 0xc5 => 0x5a97,
    0xc6 => 0x5a83, 0xc7 => 0x5a8b, 0xc8 => 0x5aa9, 0xc9 => 0x5a7b,
    0xca => 0x5a7d, 0xcb => 0x5a8c, 0xcc => 0x5a9c, 0xcd => 0x5a8f,
    0xce => 0x5a93, 0xcf => 0x5a9d, 0xd0 => 0x5bea, 0xd1 => 0x5bcd,
    0xd2 => 0x5bcb, 0xd3 => 0x5bd4, 0xd4 => 0x5bd1, 0xd5 => 0x5bca,
    0xd6 => 0x5bce, 0xd7 => 0x5c0c, 0xd8 => 0x5c30, 0xd9 => 0x5d37,
    0xda => 0x5d43, 0xdb => 0x5d6b, 0xdc => 0x5d41, 0xdd => 0x5d4b,
    0xde => 0x5d3f, 0xdf => 0x5d35, 0xe0 => 0x5d51, 0xe1 => 0x5d4e,
    0xe2 => 0x5d55, 0xe3 => 0x5d33, 0xe4 => 0x5d3a, 0xe5 => 0x5d52,
    0xe6 => 0x5d3d, 0xe7 => 0x5d31, 0xe8 => 0x5d59, 0xe9 => 0x5d42,
    0xea => 0x5d39, 0xeb => 0x5d49, 0xec => 0x5d38, 0xed => 0x5d3c,
    0xee => 0x5d32, 0xef => 0x5d36, 0xf0 => 0x5d40, 0xf1 => 0x5d45,
    0xf2 => 0x5e44, 0xf3 => 0x5e41, 0xf4 => 0x5f58, 0xf5 => 0x5fa6,
    0xf6 => 0x5fa5, 0xf7 => 0x5fab, 0xf8 => 0x60c9, 0xf9 => 0x60b9,
    0xfa => 0x60cc, 0xfb => 0x60e2, 0xfc => 0x60ce, 0xfd => 0x60c4,
    0xfe => 0x6114,
  },
  0xd9 => {
    0x40 => 0x60f2, 0x41 => 0x610a, 0x42 => 0x6116, 0x43 => 0x6105,
    0x44 => 0x60f5, 0x45 => 0x6113, 0x46 => 0x60f8, 0x47 => 0x60fc,
    0x48 => 0x60fe, 0x49 => 0x60c1, 0x4a => 0x6103, 0x4b => 0x6118,
    0x4c => 0x611d, 0x4d => 0x6110, 0x4e => 0x60ff, 0x4f => 0x6104,
    0x50 => 0x610b, 0x51 => 0x624a, 0x52 => 0x6394, 0x53 => 0x63b1,
    0x54 => 0x63b0, 0x55 => 0x63ce, 0x56 => 0x63e5, 0x57 => 0x63e8,
    0x58 => 0x63ef, 0x59 => 0x63c3, 0x5a => 0x649d, 0x5b => 0x63f3,
    0x5c => 0x63ca, 0x5d => 0x63e0, 0x5e => 0x63f6, 0x5f => 0x63d5,
    0x60 => 0x63f2, 0x61 => 0x63f5, 0x62 => 0x6461, 0x63 => 0x63df,
    0x64 => 0x63be, 0x65 => 0x63dd, 0x66 => 0x63dc, 0x67 => 0x63c4,
    0x68 => 0x63d8, 0x69 => 0x63d3, 0x6a => 0x63c2, 0x6b => 0x63c7,
    0x6c => 0x63cc, 0x6d => 0x63cb, 0x6e => 0x63c8, 0x6f => 0x63f0,
    0x70 => 0x63d7, 0x71 => 0x63d9, 0x72 => 0x6532, 0x73 => 0x6567,
    0x74 => 0x656a, 0x75 => 0x6564, 0x76 => 0x655c, 0x77 => 0x6568,
    0x78 => 0x6565, 0x79 => 0x658c, 0x7a => 0x659d, 0x7b => 0x659e,
    0x7c => 0x65ae, 0x7d => 0x65d0, 0x7e => 0x65d2, 0xa1 => 0x667c,
    0xa2 => 0x666c, 0xa3 => 0x667b, 0xa4 => 0x6680, 0xa5 => 0x6671,
    0xa6 => 0x6679, 0xa7 => 0x666a, 0xa8 => 0x6672, 0xa9 => 0x6701,
    0xaa => 0x690c, 0xab => 0x68d3, 0xac => 0x6904, 0xad => 0x68dc,
    0xae => 0x692a, 0xaf => 0x68ec, 0xb0 => 0x68ea, 0xb1 => 0x68f1,
    0xb2 => 0x690f, 0xb3 => 0x68d6, 0xb4 => 0x68f7, 0xb5 => 0x68eb,
    0xb6 => 0x68e4, 0xb7 => 0x68f6, 0xb8 => 0x6913, 0xb9 => 0x6910,
    0xba => 0x68f3, 0xbb => 0x68e1, 0xbc => 0x6907, 0xbd => 0x68cc,
    0xbe => 0x6908, 0xbf => 0x6970, 0xc0 => 0x68b4, 0xc1 => 0x6911,
    0xc2 => 0x68ef, 0xc3 => 0x68c6, 0xc4 => 0x6914, 0xc5 => 0x68f8,
    0xc6 => 0x68d0, 0xc7 => 0x68fd, 0xc8 => 0x68fc, 0xc9 => 0x68e8,
    0xca => 0x690b, 0xcb => 0x690a, 0xcc => 0x6917, 0xcd => 0x68ce,
    0xce => 0x68c8, 0xcf => 0x68dd, 0xd0 => 0x68de, 0xd1 => 0x68e6,
    0xd2 => 0x68f4, 0xd3 => 0x68d1, 0xd4 => 0x6906, 0xd5 => 0x68d4,
    0xd6 => 0x68e9, 0xd7 => 0x6915, 0xd8 => 0x6925, 0xd9 => 0x68c7,
    0xda => 0x6b39, 0xdb => 0x6b3b, 0xdc => 0x6b3f, 0xdd => 0x6b3c,
    0xde => 0x6b94, 0xdf => 0x6b97, 0xe0 => 0x6b99, 0xe1 => 0x6b95,
    0xe2 => 0x6bbd, 0xe3 => 0x6bf0, 0xe4 => 0x6bf2, 0xe5 => 0x6bf3,
    0xe6 => 0x6c30, 0xe7 => 0x6dfc, 0xe8 => 0x6e46, 0xe9 => 0x6e47,
    0xea => 0x6e1f, 0xeb => 0x6e49, 0xec => 0x6e88, 0xed => 0x6e3c,
    0xee => 0x6e3d, 0xef => 0x6e45, 0xf0 => 0x6e62, 0xf1 => 0x6e2b,
    0xf2 => 0x6e3f, 0xf3 => 0x6e41, 0xf4 => 0x6e5d, 0xf5 => 0x6e73,
    0xf6 => 0x6e1c, 0xf7 => 0x6e33, 0xf8 => 0x6e4b, 0xf9 => 0x6e40,
    0xfa => 0x6e51, 0xfb => 0x6e3b, 0xfc => 0x6e03, 0xfd => 0x6e2e,
    0xfe => 0x6e5e,
  },
  0xda => {
    0x40 => 0x6e68, 0x41 => 0x6e5c, 0x42 => 0x6e61, 0x43 => 0x6e31,
    0x44 => 0x6e28, 0x45 => 0x6e60, 0x46 => 0x6e71, 0x47 => 0x6e6b,
    0x48 => 0x6e39, 0x49 => 0x6e22, 0x4a => 0x6e30, 0x4b => 0x6e53,
    0x4c => 0x6e65, 0x4d => 0x6e27, 0x4e => 0x6e78, 0x4f => 0x6e64,
    0x50 => 0x6e77, 0x51 => 0x6e55, 0x52 => 0x6e79, 0x53 => 0x6e52,
    0x54 => 0x6e66, 0x55 => 0x6e35, 0x56 => 0x6e36, 0x57 => 0x6e5a,
    0x58 => 0x7120, 0x59 => 0x711e, 0x5a => 0x712f, 0x5b => 0x70fb,
    0x5c => 0x712e, 0x5d => 0x7131, 0x5e => 0x7123, 0x5f => 0x7125,
    0x60 => 0x7122, 0x61 => 0x7132, 0x62 => 0x711f, 0x63 => 0x7128,
    0x64 => 0x713a, 0x65 => 0x711b, 0x66 => 0x724b, 0x67 => 0x725a,
    0x68 => 0x7288, 0x69 => 0x7289, 0x6a => 0x7286, 0x6b => 0x7285,
    0x6c => 0x728b, 0x6d => 0x7312, 0x6e => 0x730b, 0x6f => 0x7330,
    0x70 => 0x7322, 0x71 => 0x7331, 0x72 => 0x7333, 0x73 => 0x7327,
    0x74 => 0x7332, 0x75 => 0x732d, 0x76 => 0x7326, 0x77 => 0x7323,
    0x78 => 0x7335, 0x79 => 0x730c, 0x7a => 0x742e, 0x7b => 0x742c,
    0x7c => 0x7430, 0x7d => 0x742b, 0x7e => 0x7416, 0xa1 => 0x741a,
    0xa2 => 0x7421, 0xa3 => 0x742d, 0xa4 => 0x7431, 0xa5 => 0x7424,
    0xa6 => 0x7423, 0xa7 => 0x741d, 0xa8 => 0x7429, 0xa9 => 0x7420,
    0xaa => 0x7432, 0xab => 0x74fb, 0xac => 0x752f, 0xad => 0x756f,
    0xae => 0x756c, 0xaf => 0x75e7, 0xb0 => 0x75da, 0xb1 => 0x75e1,
    0xb2 => 0x75e6, 0xb3 => 0x75dd, 0xb4 => 0x75df, 0xb5 => 0x75e4,
    0xb6 => 0x75d7, 0xb7 => 0x7695, 0xb8 => 0x7692, 0xb9 => 0x76da,
    0xba => 0x7746, 0xbb => 0x7747, 0xbc => 0x7744, 0xbd => 0x774d,
    0xbe => 0x7745, 0xbf => 0x774a, 0xc0 => 0x774e, 0xc1 => 0x774b,
    0xc2 => 0x774c, 0xc3 => 0x77de, 0xc4 => 0x77ec, 0xc5 => 0x7860,
    0xc6 => 0x7864, 0xc7 => 0x7865, 0xc8 => 0x785c, 0xc9 => 0x786d,
    0xca => 0x7871, 0xcb => 0x786a, 0xcc => 0x786e, 0xcd => 0x7870,
    0xce => 0x7869, 0xcf => 0x7868, 0xd0 => 0x785e, 0xd1 => 0x7862,
    0xd2 => 0x7974, 0xd3 => 0x7973, 0xd4 => 0x7972, 0xd5 => 0x7970,
    0xd6 => 0x7a02, 0xd7 => 0x7a0a, 0xd8 => 0x7a03, 0xd9 => 0x7a0c,
    0xda => 0x7a04, 0xdb => 0x7a99, 0xdc => 0x7ae6, 0xdd => 0x7ae4,
    0xde => 0x7b4a, 0xdf => 0x7b3b, 0xe0 => 0x7b44, 0xe1 => 0x7b48,
    0xe2 => 0x7b4c, 0xe3 => 0x7b4e, 0xe4 => 0x7b40, 0xe5 => 0x7b58,
    0xe6 => 0x7b45, 0xe7 => 0x7ca2, 0xe8 => 0x7c9e, 0xe9 => 0x7ca8,
    0xea => 0x7ca1, 0xeb => 0x7d58, 0xec => 0x7d6f, 0xed => 0x7d63,
    0xee => 0x7d53, 0xef => 0x7d56, 0xf0 => 0x7d67, 0xf1 => 0x7d6a,
    0xf2 => 0x7d4f, 0xf3 => 0x7d6d, 0xf4 => 0x7d5c, 0xf5 => 0x7d6b,
    0xf6 => 0x7d52, 0xf7 => 0x7d54, 0xf8 => 0x7d69, 0xf9 => 0x7d51,
    0xfa => 0x7d5f, 0xfb => 0x7d4e, 0xfc => 0x7f3e, 0xfd => 0x7f3f,
    0xfe => 0x7f65,
  },
  0xdb => {
    0x40 => 0x7f66, 0x41 => 0x7fa2, 0x42 => 0x7fa0, 0x43 => 0x7fa1,
    0x44 => 0x7fd7, 0x45 => 0x8051, 0x46 => 0x804f, 0x47 => 0x8050,
    0x48 => 0x80fe, 0x49 => 0x80d4, 0x4a => 0x8143, 0x4b => 0x814a,
    0x4c => 0x8152, 0x4d => 0x814f, 0x4e => 0x8147, 0x4f => 0x813d,
    0x50 => 0x814d, 0x51 => 0x813a, 0x52 => 0x81e6, 0x53 => 0x81ee,
    0x54 => 0x81f7, 0x55 => 0x81f8, 0x56 => 0x81f9, 0x57 => 0x8204,
    0x58 => 0x823c, 0x59 => 0x823d, 0x5a => 0x823f, 0x5b => 0x8275,
    0x5c => 0x833b, 0x5d => 0x83cf, 0x5e => 0x83f9, 0x5f => 0x8423,
    0x60 => 0x83c0, 0x61 => 0x83e8, 0x62 => 0x8412, 0x63 => 0x83e7,
    0x64 => 0x83e4, 0x65 => 0x83fc, 0x66 => 0x83f6, 0x67 => 0x8410,
    0x68 => 0x83c6, 0x69 => 0x83c8, 0x6a => 0x83eb, 0x6b => 0x83e3,
    0x6c => 0x83bf, 0x6d => 0x8401, 0x6e => 0x83dd, 0x6f => 0x83e5,
    0x70 => 0x83d8, 0x71 => 0x83ff, 0x72 => 0x83e1, 0x73 => 0x83cb,
    0x74 => 0x83ce, 0x75 => 0x83d6, 0x76 => 0x83f5, 0x77 => 0x83c9,
    0x78 => 0x8409, 0x79 => 0x840f, 0x7a => 0x83de, 0x7b => 0x8411,
    0x7c => 0x8406, 0x7d => 0x83c2, 0x7e => 0x83f3, 0xa1 => 0x83d5,
    0xa2 => 0x83fa, 0xa3 => 0x83c7, 0xa4 => 0x83d1, 0xa5 => 0x83ea,
    0xa6 => 0x8413, 0xa7 => 0x83c3, 0xa8 => 0x83ec, 0xa9 => 0x83ee,
    0xaa => 0x83c4, 0xab => 0x83fb, 0xac => 0x83d7, 0xad => 0x83e2,
    0xae => 0x841b, 0xaf => 0x83db, 0xb0 => 0x83fe, 0xb1 => 0x86d8,
    0xb2 => 0x86e2, 0xb3 => 0x86e6, 0xb4 => 0x86d3, 0xb5 => 0x86e3,
    0xb6 => 0x86da, 0xb7 => 0x86ea, 0xb8 => 0x86dd, 0xb9 => 0x86eb,
    0xba => 0x86dc, 0xbb => 0x86ec, 0xbc => 0x86e9, 0xbd => 0x86d7,
    0xbe => 0x86e8, 0xbf => 0x86d1, 0xc0 => 0x8848, 0xc1 => 0x8856,
    0xc2 => 0x8855, 0xc3 => 0x88ba, 0xc4 => 0x88d7, 0xc5 => 0x88b9,
    0xc6 => 0x88b8, 0xc7 => 0x88c0, 0xc8 => 0x88be, 0xc9 => 0x88b6,
    0xca => 0x88bc, 0xcb => 0x88b7, 0xcc => 0x88bd, 0xcd => 0x88b2,
    0xce => 0x8901, 0xcf => 0x88c9, 0xd0 => 0x8995, 0xd1 => 0x8998,
    0xd2 => 0x8997, 0xd3 => 0x89dd, 0xd4 => 0x89da, 0xd5 => 0x89db,
    0xd6 => 0x8a4e, 0xd7 => 0x8a4d, 0xd8 => 0x8a39, 0xd9 => 0x8a59,
    0xda => 0x8a40, 0xdb => 0x8a57, 0xdc => 0x8a58, 0xdd => 0x8a44,
    0xde => 0x8a45, 0xdf => 0x8a52, 0xe0 => 0x8a48, 0xe1 => 0x8a51,
    0xe2 => 0x8a4a, 0xe3 => 0x8a4c, 0xe4 => 0x8a4f, 0xe5 => 0x8c5f,
    0xe6 => 0x8c81, 0xe7 => 0x8c80, 0xe8 => 0x8cba, 0xe9 => 0x8cbe,
    0xea => 0x8cb0, 0xeb => 0x8cb9, 0xec => 0x8cb5, 0xed => 0x8d84,
    0xee => 0x8d80, 0xef => 0x8d89, 0xf0 => 0x8dd8, 0xf1 => 0x8dd3,
    0xf2 => 0x8dcd, 0xf3 => 0x8dc7, 0xf4 => 0x8dd6, 0xf5 => 0x8ddc,
    0xf6 => 0x8dcf, 0xf7 => 0x8dd5, 0xf8 => 0x8dd9, 0xf9 => 0x8dc8,
    0xfa => 0x8dd7, 0xfb => 0x8dc5, 0xfc => 0x8eef, 0xfd => 0x8ef7,
    0xfe => 0x8efa,
  },
  0xdc => {
    0x40 => 0x8ef9, 0x41 => 0x8ee6, 0x42 => 0x8eee, 0x43 => 0x8ee5,
    0x44 => 0x8ef5, 0x45 => 0x8ee7, 0x46 => 0x8ee8, 0x47 => 0x8ef6,
    0x48 => 0x8eeb, 0x49 => 0x8ef1, 0x4a => 0x8eec, 0x4b => 0x8ef4,
    0x4c => 0x8ee9, 0x4d => 0x902d, 0x4e => 0x9034, 0x4f => 0x902f,
    0x50 => 0x9106, 0x51 => 0x912c, 0x52 => 0x9104, 0x53 => 0x90ff,
    0x54 => 0x90fc, 0x55 => 0x9108, 0x56 => 0x90f9, 0x57 => 0x90fb,
    0x58 => 0x9101, 0x59 => 0x9100, 0x5a => 0x9107, 0x5b => 0x9105,
    0x5c => 0x9103, 0x5d => 0x9161, 0x5e => 0x9164, 0x5f => 0x915f,
    0x60 => 0x9162, 0x61 => 0x9160, 0x62 => 0x9201, 0x63 => 0x920a,
    0x64 => 0x9225, 0x65 => 0x9203, 0x66 => 0x921a, 0x67 => 0x9226,
    0x68 => 0x920f, 0x69 => 0x920c, 0x6a => 0x9200, 0x6b => 0x9212,
    0x6c => 0x91ff, 0x6d => 0x91fd, 0x6e => 0x9206, 0x6f => 0x9204,
    0x70 => 0x9227, 0x71 => 0x9202, 0x72 => 0x921c, 0x73 => 0x9224,
    0x74 => 0x9219, 0x75 => 0x9217, 0x76 => 0x9205, 0x77 => 0x9216,
    0x78 => 0x957b, 0x79 => 0x958d, 0x7a => 0x958c, 0x7b => 0x9590,
    0x7c => 0x9687, 0x7d => 0x967e, 0x7e => 0x9688, 0xa1 => 0x9689,
    0xa2 => 0x9683, 0xa3 => 0x9680, 0xa4 => 0x96c2, 0xa5 => 0x96c8,
    0xa6 => 0x96c3, 0xa7 => 0x96f1, 0xa8 => 0x96f0, 0xa9 => 0x976c,
    0xaa => 0x9770, 0xab => 0x976e, 0xac => 0x9807, 0xad => 0x98a9,
    0xae => 0x98eb, 0xaf => 0x9ce6, 0xb0 => 0x9ef9, 0xb1 => 0x4e83,
    0xb2 => 0x4e84, 0xb3 => 0x4eb6, 0xb4 => 0x50bd, 0xb5 => 0x50bf,
    0xb6 => 0x50c6, 0xb7 => 0x50ae, 0xb8 => 0x50c4, 0xb9 => 0x50ca,
    0xba => 0x50b4, 0xbb => 0x50c8, 0xbc => 0x50c2, 0xbd => 0x50b0,
    0xbe => 0x50c1, 0xbf => 0x50ba, 0xc0 => 0x50b1, 0xc1 => 0x50cb,
    0xc2 => 0x50c9, 0xc3 => 0x50b6, 0xc4 => 0x50b8, 0xc5 => 0x51d7,
    0xc6 => 0x527a, 0xc7 => 0x5278, 0xc8 => 0x527b, 0xc9 => 0x527c,
    0xca => 0x55c3, 0xcb => 0x55db, 0xcc => 0x55cc, 0xcd => 0x55d0,
    0xce => 0x55cb, 0xcf => 0x55ca, 0xd0 => 0x55dd, 0xd1 => 0x55c0,
    0xd2 => 0x55d4, 0xd3 => 0x55c4, 0xd4 => 0x55e9, 0xd5 => 0x55bf,
    0xd6 => 0x55d2, 0xd7 => 0x558d, 0xd8 => 0x55cf, 0xd9 => 0x55d5,
    0xda => 0x55e2, 0xdb => 0x55d6, 0xdc => 0x55c8, 0xdd => 0x55f2,
    0xde => 0x55cd, 0xdf => 0x55d9, 0xe0 => 0x55c2, 0xe1 => 0x5714,
    0xe2 => 0x5853, 0xe3 => 0x5868, 0xe4 => 0x5864, 0xe5 => 0x584f,
    0xe6 => 0x584d, 0xe7 => 0x5849, 0xe8 => 0x586f, 0xe9 => 0x5855,
    0xea => 0x584e, 0xeb => 0x585d, 0xec => 0x5859, 0xed => 0x5865,
    0xee => 0x585b, 0xef => 0x583d, 0xf0 => 0x5863, 0xf1 => 0x5871,
    0xf2 => 0x58fc, 0xf3 => 0x5ac7, 0xf4 => 0x5ac4, 0xf5 => 0x5acb,
    0xf6 => 0x5aba, 0xf7 => 0x5ab8, 0xf8 => 0x5ab1, 0xf9 => 0x5ab5,
    0xfa => 0x5ab0, 0xfb => 0x5abf, 0xfc => 0x5ac8, 0xfd => 0x5abb,
    0xfe => 0x5ac6,
  },
  0xdd => {
    0x40 => 0x5ab7, 0x41 => 0x5ac0, 0x42 => 0x5aca, 0x43 => 0x5ab4,
    0x44 => 0x5ab6, 0x45 => 0x5acd, 0x46 => 0x5ab9, 0x47 => 0x5a90,
    0x48 => 0x5bd6, 0x49 => 0x5bd8, 0x4a => 0x5bd9, 0x4b => 0x5c1f,
    0x4c => 0x5c33, 0x4d => 0x5d71, 0x4e => 0x5d63, 0x4f => 0x5d4a,
    0x50 => 0x5d65, 0x51 => 0x5d72, 0x52 => 0x5d6c, 0x53 => 0x5d5e,
    0x54 => 0x5d68, 0x55 => 0x5d67, 0x56 => 0x5d62, 0x57 => 0x5df0,
    0x58 => 0x5e4f, 0x59 => 0x5e4e, 0x5a => 0x5e4a, 0x5b => 0x5e4d,
    0x5c => 0x5e4b, 0x5d => 0x5ec5, 0x5e => 0x5ecc, 0x5f => 0x5ec6,
    0x60 => 0x5ecb, 0x61 => 0x5ec7, 0x62 => 0x5f40, 0x63 => 0x5faf,
    0x64 => 0x5fad, 0x65 => 0x60f7, 0x66 => 0x6149, 0x67 => 0x614a,
    0x68 => 0x612b, 0x69 => 0x6145, 0x6a => 0x6136, 0x6b => 0x6132,
    0x6c => 0x612e, 0x6d => 0x6146, 0x6e => 0x612f, 0x6f => 0x614f,
    0x70 => 0x6129, 0x71 => 0x6140, 0x72 => 0x6220, 0x73 => 0x9168,
    0x74 => 0x6223, 0x75 => 0x6225, 0x76 => 0x6224, 0x77 => 0x63c5,
    0x78 => 0x63f1, 0x79 => 0x63eb, 0x7a => 0x6410, 0x7b => 0x6412,
    0x7c => 0x6409, 0x7d => 0x6420, 0x7e => 0x6424, 0xa1 => 0x6433,
    0xa2 => 0x6443, 0xa3 => 0x641f, 0xa4 => 0x6415, 0xa5 => 0x6418,
    0xa6 => 0x6439, 0xa7 => 0x6437, 0xa8 => 0x6422, 0xa9 => 0x6423,
    0xaa => 0x640c, 0xab => 0x6426, 0xac => 0x6430, 0xad => 0x6428,
    0xae => 0x6441, 0xaf => 0x6435, 0xb0 => 0x642f, 0xb1 => 0x640a,
    0xb2 => 0x641a, 0xb3 => 0x6440, 0xb4 => 0x6425, 0xb5 => 0x6427,
    0xb6 => 0x640b, 0xb7 => 0x63e7, 0xb8 => 0x641b, 0xb9 => 0x642e,
    0xba => 0x6421, 0xbb => 0x640e, 0xbc => 0x656f, 0xbd => 0x6592,
    0xbe => 0x65d3, 0xbf => 0x6686, 0xc0 => 0x668c, 0xc1 => 0x6695,
    0xc2 => 0x6690, 0xc3 => 0x668b, 0xc4 => 0x668a, 0xc5 => 0x6699,
    0xc6 => 0x6694, 0xc7 => 0x6678, 0xc8 => 0x6720, 0xc9 => 0x6966,
    0xca => 0x695f, 0xcb => 0x6938, 0xcc => 0x694e, 0xcd => 0x6962,
    0xce => 0x6971, 0xcf => 0x693f, 0xd0 => 0x6945, 0xd1 => 0x696a,
    0xd2 => 0x6939, 0xd3 => 0x6942, 0xd4 => 0x6957, 0xd5 => 0x6959,
    0xd6 => 0x697a, 0xd7 => 0x6948, 0xd8 => 0x6949, 0xd9 => 0x6935,
    0xda => 0x696c, 0xdb => 0x6933, 0xdc => 0x693d, 0xdd => 0x6965,
    0xde => 0x68f0, 0xdf => 0x6978, 0xe0 => 0x6934, 0xe1 => 0x6969,
    0xe2 => 0x6940, 0xe3 => 0x696f, 0xe4 => 0x6944, 0xe5 => 0x6976,
    0xe6 => 0x6958, 0xe7 => 0x6941, 0xe8 => 0x6974, 0xe9 => 0x694c,
    0xea => 0x693b, 0xeb => 0x694b, 0xec => 0x6937, 0xed => 0x695c,
    0xee => 0x694f, 0xef => 0x6951, 0xf0 => 0x6932, 0xf1 => 0x6952,
    0xf2 => 0x692f, 0xf3 => 0x697b, 0xf4 => 0x693c, 0xf5 => 0x6b46,
    0xf6 => 0x6b45, 0xf7 => 0x6b43, 0xf8 => 0x6b42, 0xf9 => 0x6b48,
    0xfa => 0x6b41, 0xfb => 0x6b9b, 0xfc => 0xfa0d, 0xfd => 0x6bfb,
    0xfe => 0x6bfc,
  },
  0xde => {
    0x40 => 0x6bf9, 0x41 => 0x6bf7, 0x42 => 0x6bf8, 0x43 => 0x6e9b,
    0x44 => 0x6ed6, 0x45 => 0x6ec8, 0x46 => 0x6e8f, 0x47 => 0x6ec0,
    0x48 => 0x6e9f, 0x49 => 0x6e93, 0x4a => 0x6e94, 0x4b => 0x6ea0,
    0x4c => 0x6eb1, 0x4d => 0x6eb9, 0x4e => 0x6ec6, 0x4f => 0x6ed2,
    0x50 => 0x6ebd, 0x51 => 0x6ec1, 0x52 => 0x6e9e, 0x53 => 0x6ec9,
    0x54 => 0x6eb7, 0x55 => 0x6eb0, 0x56 => 0x6ecd, 0x57 => 0x6ea6,
    0x58 => 0x6ecf, 0x59 => 0x6eb2, 0x5a => 0x6ebe, 0x5b => 0x6ec3,
    0x5c => 0x6edc, 0x5d => 0x6ed8, 0x5e => 0x6e99, 0x5f => 0x6e92,
    0x60 => 0x6e8e, 0x61 => 0x6e8d, 0x62 => 0x6ea4, 0x63 => 0x6ea1,
    0x64 => 0x6ebf, 0x65 => 0x6eb3, 0x66 => 0x6ed0, 0x67 => 0x6eca,
    0x68 => 0x6e97, 0x69 => 0x6eae, 0x6a => 0x6ea3, 0x6b => 0x7147,
    0x6c => 0x7154, 0x6d => 0x7152, 0x6e => 0x7163, 0x6f => 0x7160,
    0x70 => 0x7141, 0x71 => 0x715d, 0x72 => 0x7162, 0x73 => 0x7172,
    0x74 => 0x7178, 0x75 => 0x716a, 0x76 => 0x7161, 0x77 => 0x7142,
    0x78 => 0x7158, 0x79 => 0x7143, 0x7a => 0x714b, 0x7b => 0x7170,
    0x7c => 0x715f, 0x7d => 0x7150, 0x7e => 0x7153, 0xa1 => 0x7144,
    0xa2 => 0x714d, 0xa3 => 0x715a, 0xa4 => 0x724f, 0xa5 => 0x728d,
    0xa6 => 0x728c, 0xa7 => 0x7291, 0xa8 => 0x7290, 0xa9 => 0x728e,
    0xaa => 0x733c, 0xab => 0x7342, 0xac => 0x733b, 0xad => 0x733a,
    0xae => 0x7340, 0xaf => 0x734a, 0xb0 => 0x7349, 0xb1 => 0x7444,
    0xb2 => 0x744a, 0xb3 => 0x744b, 0xb4 => 0x7452, 0xb5 => 0x7451,
    0xb6 => 0x7457, 0xb7 => 0x7440, 0xb8 => 0x744f, 0xb9 => 0x7450,
    0xba => 0x744e, 0xbb => 0x7442, 0xbc => 0x7446, 0xbd => 0x744d,
    0xbe => 0x7454, 0xbf => 0x74e1, 0xc0 => 0x74ff, 0xc1 => 0x74fe,
    0xc2 => 0x74fd, 0xc3 => 0x751d, 0xc4 => 0x7579, 0xc5 => 0x7577,
    0xc6 => 0x6983, 0xc7 => 0x75ef, 0xc8 => 0x760f, 0xc9 => 0x7603,
    0xca => 0x75f7, 0xcb => 0x75fe, 0xcc => 0x75fc, 0xcd => 0x75f9,
    0xce => 0x75f8, 0xcf => 0x7610, 0xd0 => 0x75fb, 0xd1 => 0x75f6,
    0xd2 => 0x75ed, 0xd3 => 0x75f5, 0xd4 => 0x75fd, 0xd5 => 0x7699,
    0xd6 => 0x76b5, 0xd7 => 0x76dd, 0xd8 => 0x7755, 0xd9 => 0x775f,
    0xda => 0x7760, 0xdb => 0x7752, 0xdc => 0x7756, 0xdd => 0x775a,
    0xde => 0x7769, 0xdf => 0x7767, 0xe0 => 0x7754, 0xe1 => 0x7759,
    0xe2 => 0x776d, 0xe3 => 0x77e0, 0xe4 => 0x7887, 0xe5 => 0x789a,
    0xe6 => 0x7894, 0xe7 => 0x788f, 0xe8 => 0x7884, 0xe9 => 0x7895,
    0xea => 0x7885, 0xeb => 0x7886, 0xec => 0x78a1, 0xed => 0x7883,
    0xee => 0x7879, 0xef => 0x7899, 0xf0 => 0x7880, 0xf1 => 0x7896,
    0xf2 => 0x787b, 0xf3 => 0x797c, 0xf4 => 0x7982, 0xf5 => 0x797d,
    0xf6 => 0x7979, 0xf7 => 0x7a11, 0xf8 => 0x7a18, 0xf9 => 0x7a19,
    0xfa => 0x7a12, 0xfb => 0x7a17, 0xfc => 0x7a15, 0xfd => 0x7a22,
    0xfe => 0x7a13,
  },
  0xdf => {
    0x40 => 0x7a1b, 0x41 => 0x7a10, 0x42 => 0x7aa3, 0x43 => 0x7aa2,
    0x44 => 0x7a9e, 0x45 => 0x7aeb, 0x46 => 0x7b66, 0x47 => 0x7b64,
    0x48 => 0x7b6d, 0x49 => 0x7b74, 0x4a => 0x7b69, 0x4b => 0x7b72,
    0x4c => 0x7b65, 0x4d => 0x7b73, 0x4e => 0x7b71, 0x4f => 0x7b70,
    0x50 => 0x7b61, 0x51 => 0x7b78, 0x52 => 0x7b76, 0x53 => 0x7b63,
    0x54 => 0x7cb2, 0x55 => 0x7cb4, 0x56 => 0x7caf, 0x57 => 0x7d88,
    0x58 => 0x7d86, 0x59 => 0x7d80, 0x5a => 0x7d8d, 0x5b => 0x7d7f,
    0x5c => 0x7d85, 0x5d => 0x7d7a, 0x5e => 0x7d8e, 0x5f => 0x7d7b,
    0x60 => 0x7d83, 0x61 => 0x7d7c, 0x62 => 0x7d8c, 0x63 => 0x7d94,
    0x64 => 0x7d84, 0x65 => 0x7d7d, 0x66 => 0x7d92, 0x67 => 0x7f6d,
    0x68 => 0x7f6b, 0x69 => 0x7f67, 0x6a => 0x7f68, 0x6b => 0x7f6c,
    0x6c => 0x7fa6, 0x6d => 0x7fa5, 0x6e => 0x7fa7, 0x6f => 0x7fdb,
    0x70 => 0x7fdc, 0x71 => 0x8021, 0x72 => 0x8164, 0x73 => 0x8160,
    0x74 => 0x8177, 0x75 => 0x815c, 0x76 => 0x8169, 0x77 => 0x815b,
    0x78 => 0x8162, 0x79 => 0x8172, 0x7a => 0x6721, 0x7b => 0x815e,
    0x7c => 0x8176, 0x7d => 0x8167, 0x7e => 0x816f, 0xa1 => 0x8144,
    0xa2 => 0x8161, 0xa3 => 0x821d, 0xa4 => 0x8249, 0xa5 => 0x8244,
    0xa6 => 0x8240, 0xa7 => 0x8242, 0xa8 => 0x8245, 0xa9 => 0x84f1,
    0xaa => 0x843f, 0xab => 0x8456, 0xac => 0x8476, 0xad => 0x8479,
    0xae => 0x848f, 0xaf => 0x848d, 0xb0 => 0x8465, 0xb1 => 0x8451,
    0xb2 => 0x8440, 0xb3 => 0x8486, 0xb4 => 0x8467, 0xb5 => 0x8430,
    0xb6 => 0x844d, 0xb7 => 0x847d, 0xb8 => 0x845a, 0xb9 => 0x8459,
    0xba => 0x8474, 0xbb => 0x8473, 0xbc => 0x845d, 0xbd => 0x8507,
    0xbe => 0x845e, 0xbf => 0x8437, 0xc0 => 0x843a, 0xc1 => 0x8434,
    0xc2 => 0x847a, 0xc3 => 0x8443, 0xc4 => 0x8478, 0xc5 => 0x8432,
    0xc6 => 0x8445, 0xc7 => 0x8429, 0xc8 => 0x83d9, 0xc9 => 0x844b,
    0xca => 0x842f, 0xcb => 0x8442, 0xcc => 0x842d, 0xcd => 0x845f,
    0xce => 0x8470, 0xcf => 0x8439, 0xd0 => 0x844e, 0xd1 => 0x844c,
    0xd2 => 0x8452, 0xd3 => 0x846f, 0xd4 => 0x84c5, 0xd5 => 0x848e,
    0xd6 => 0x843b, 0xd7 => 0x8447, 0xd8 => 0x8436, 0xd9 => 0x8433,
    0xda => 0x8468, 0xdb => 0x847e, 0xdc => 0x8444, 0xdd => 0x842b,
    0xde => 0x8460, 0xdf => 0x8454, 0xe0 => 0x846e, 0xe1 => 0x8450,
    0xe2 => 0x870b, 0xe3 => 0x8704, 0xe4 => 0x86f7, 0xe5 => 0x870c,
    0xe6 => 0x86fa, 0xe7 => 0x86d6, 0xe8 => 0x86f5, 0xe9 => 0x874d,
    0xea => 0x86f8, 0xeb => 0x870e, 0xec => 0x8709, 0xed => 0x8701,
    0xee => 0x86f6, 0xef => 0x870d, 0xf0 => 0x8705, 0xf1 => 0x88d6,
    0xf2 => 0x88cb, 0xf3 => 0x88cd, 0xf4 => 0x88ce, 0xf5 => 0x88de,
    0xf6 => 0x88db, 0xf7 => 0x88da, 0xf8 => 0x88cc, 0xf9 => 0x88d0,
    0xfa => 0x8985, 0xfb => 0x899b, 0xfc => 0x89df, 0xfd => 0x89e5,
    0xfe => 0x89e4,
  },
  0xe0 => {
    0x40 => 0x89e1, 0x41 => 0x89e0, 0x42 => 0x89e2, 0x43 => 0x89dc,
    0x44 => 0x89e6, 0x45 => 0x8a76, 0x46 => 0x8a86, 0x47 => 0x8a7f,
    0x48 => 0x8a61, 0x49 => 0x8a3f, 0x4a => 0x8a77, 0x4b => 0x8a82,
    0x4c => 0x8a84, 0x4d => 0x8a75, 0x4e => 0x8a83, 0x4f => 0x8a81,
    0x50 => 0x8a74, 0x51 => 0x8a7a, 0x52 => 0x8c3c, 0x53 => 0x8c4b,
    0x54 => 0x8c4a, 0x55 => 0x8c65, 0x56 => 0x8c64, 0x57 => 0x8c66,
    0x58 => 0x8c86, 0x59 => 0x8c84, 0x5a => 0x8c85, 0x5b => 0x8ccc,
    0x5c => 0x8d68, 0x5d => 0x8d69, 0x5e => 0x8d91, 0x5f => 0x8d8c,
    0x60 => 0x8d8e, 0x61 => 0x8d8f, 0x62 => 0x8d8d, 0x63 => 0x8d93,
    0x64 => 0x8d94, 0x65 => 0x8d90, 0x66 => 0x8d92, 0x67 => 0x8df0,
    0x68 => 0x8de0, 0x69 => 0x8dec, 0x6a => 0x8df1, 0x6b => 0x8dee,
    0x6c => 0x8dd0, 0x6d => 0x8de9, 0x6e => 0x8de3, 0x6f => 0x8de2,
    0x70 => 0x8de7, 0x71 => 0x8df2, 0x72 => 0x8deb, 0x73 => 0x8df4,
    0x74 => 0x8f06, 0x75 => 0x8eff, 0x76 => 0x8f01, 0x77 => 0x8f00,
    0x78 => 0x8f05, 0x79 => 0x8f07, 0x7a => 0x8f08, 0x7b => 0x8f02,
    0x7c => 0x8f0b, 0x7d => 0x9052, 0x7e => 0x903f, 0xa1 => 0x9044,
    0xa2 => 0x9049, 0xa3 => 0x903d, 0xa4 => 0x9110, 0xa5 => 0x910d,
    0xa6 => 0x910f, 0xa7 => 0x9111, 0xa8 => 0x9116, 0xa9 => 0x9114,
    0xaa => 0x910b, 0xab => 0x910e, 0xac => 0x916e, 0xad => 0x916f,
    0xae => 0x9248, 0xaf => 0x9252, 0xb0 => 0x9230, 0xb1 => 0x923a,
    0xb2 => 0x9266, 0xb3 => 0x9233, 0xb4 => 0x9265, 0xb5 => 0x925e,
    0xb6 => 0x9283, 0xb7 => 0x922e, 0xb8 => 0x924a, 0xb9 => 0x9246,
    0xba => 0x926d, 0xbb => 0x926c, 0xbc => 0x924f, 0xbd => 0x9260,
    0xbe => 0x9267, 0xbf => 0x926f, 0xc0 => 0x9236, 0xc1 => 0x9261,
    0xc2 => 0x9270, 0xc3 => 0x9231, 0xc4 => 0x9254, 0xc5 => 0x9263,
    0xc6 => 0x9250, 0xc7 => 0x9272, 0xc8 => 0x924e, 0xc9 => 0x9253,
    0xca => 0x924c, 0xcb => 0x9256, 0xcc => 0x9232, 0xcd => 0x959f,
    0xce => 0x959c, 0xcf => 0x959e, 0xd0 => 0x959b, 0xd1 => 0x9692,
    0xd2 => 0x9693, 0xd3 => 0x9691, 0xd4 => 0x9697, 0xd5 => 0x96ce,
    0xd6 => 0x96fa, 0xd7 => 0x96fd, 0xd8 => 0x96f8, 0xd9 => 0x96f5,
    0xda => 0x9773, 0xdb => 0x9777, 0xdc => 0x9778, 0xdd => 0x9772,
    0xde => 0x980f, 0xdf => 0x980d, 0xe0 => 0x980e, 0xe1 => 0x98ac,
    0xe2 => 0x98f6, 0xe3 => 0x98f9, 0xe4 => 0x99af, 0xe5 => 0x99b2,
    0xe6 => 0x99b0, 0xe7 => 0x99b5, 0xe8 => 0x9aad, 0xe9 => 0x9aab,
    0xea => 0x9b5b, 0xeb => 0x9cea, 0xec => 0x9ced, 0xed => 0x9ce7,
    0xee => 0x9e80, 0xef => 0x9efd, 0xf0 => 0x50e6, 0xf1 => 0x50d4,
    0xf2 => 0x50d7, 0xf3 => 0x50e8, 0xf4 => 0x50f3, 0xf5 => 0x50db,
    0xf6 => 0x50ea, 0xf7 => 0x50dd, 0xf8 => 0x50e4, 0xf9 => 0x50d3,
    0xfa => 0x50ec, 0xfb => 0x50f0, 0xfc => 0x50ef, 0xfd => 0x50e3,
    0xfe => 0x50e0,
  },
  0xe1 => {
    0x40 => 0x51d8, 0x41 => 0x5280, 0x42 => 0x5281, 0x43 => 0x52e9,
    0x44 => 0x52eb, 0x45 => 0x5330, 0x46 => 0x53ac, 0x47 => 0x5627,
    0x48 => 0x5615, 0x49 => 0x560c, 0x4a => 0x5612, 0x4b => 0x55fc,
    0x4c => 0x560f, 0x4d => 0x561c, 0x4e => 0x5601, 0x4f => 0x5613,
    0x50 => 0x5602, 0x51 => 0x55fa, 0x52 => 0x561d, 0x53 => 0x5604,
    0x54 => 0x55ff, 0x55 => 0x55f9, 0x56 => 0x5889, 0x57 => 0x587c,
    0x58 => 0x5890, 0x59 => 0x5898, 0x5a => 0x5886, 0x5b => 0x5881,
    0x5c => 0x587f, 0x5d => 0x5874, 0x5e => 0x588b, 0x5f => 0x587a,
    0x60 => 0x5887, 0x61 => 0x5891, 0x62 => 0x588e, 0x63 => 0x5876,
    0x64 => 0x5882, 0x65 => 0x5888, 0x66 => 0x587b, 0x67 => 0x5894,
    0x68 => 0x588f, 0x69 => 0x58fe, 0x6a => 0x596b, 0x6b => 0x5adc,
    0x6c => 0x5aee, 0x6d => 0x5ae5, 0x6e => 0x5ad5, 0x6f => 0x5aea,
    0x70 => 0x5ada, 0x71 => 0x5aed, 0x72 => 0x5aeb, 0x73 => 0x5af3,
    0x74 => 0x5ae2, 0x75 => 0x5ae0, 0x76 => 0x5adb, 0x77 => 0x5aec,
    0x78 => 0x5ade, 0x79 => 0x5add, 0x7a => 0x5ad9, 0x7b => 0x5ae8,
    0x7c => 0x5adf, 0x7d => 0x5b77, 0x7e => 0x5be0, 0xa1 => 0x5be3,
    0xa2 => 0x5c63, 0xa3 => 0x5d82, 0xa4 => 0x5d80, 0xa5 => 0x5d7d,
    0xa6 => 0x5d86, 0xa7 => 0x5d7a, 0xa8 => 0x5d81, 0xa9 => 0x5d77,
    0xaa => 0x5d8a, 0xab => 0x5d89, 0xac => 0x5d88, 0xad => 0x5d7e,
    0xae => 0x5d7c, 0xaf => 0x5d8d, 0xb0 => 0x5d79, 0xb1 => 0x5d7f,
    0xb2 => 0x5e58, 0xb3 => 0x5e59, 0xb4 => 0x5e53, 0xb5 => 0x5ed8,
    0xb6 => 0x5ed1, 0xb7 => 0x5ed7, 0xb8 => 0x5ece, 0xb9 => 0x5edc,
    0xba => 0x5ed5, 0xbb => 0x5ed9, 0xbc => 0x5ed2, 0xbd => 0x5ed4,
    0xbe => 0x5f44, 0xbf => 0x5f43, 0xc0 => 0x5f6f, 0xc1 => 0x5fb6,
    0xc2 => 0x612c, 0xc3 => 0x6128, 0xc4 => 0x6141, 0xc5 => 0x615e,
    0xc6 => 0x6171, 0xc7 => 0x6173, 0xc8 => 0x6152, 0xc9 => 0x6153,
    0xca => 0x6172, 0xcb => 0x616c, 0xcc => 0x6180, 0xcd => 0x6174,
    0xce => 0x6154, 0xcf => 0x617a, 0xd0 => 0x615b, 0xd1 => 0x6165,
    0xd2 => 0x613b, 0xd3 => 0x616a, 0xd4 => 0x6161, 0xd5 => 0x6156,
    0xd6 => 0x6229, 0xd7 => 0x6227, 0xd8 => 0x622b, 0xd9 => 0x642b,
    0xda => 0x644d, 0xdb => 0x645b, 0xdc => 0x645d, 0xdd => 0x6474,
    0xde => 0x6476, 0xdf => 0x6472, 0xe0 => 0x6473, 0xe1 => 0x647d,
    0xe2 => 0x6475, 0xe3 => 0x6466, 0xe4 => 0x64a6, 0xe5 => 0x644e,
    0xe6 => 0x6482, 0xe7 => 0x645e, 0xe8 => 0x645c, 0xe9 => 0x644b,
    0xea => 0x6453, 0xeb => 0x6460, 0xec => 0x6450, 0xed => 0x647f,
    0xee => 0x643f, 0xef => 0x646c, 0xf0 => 0x646b, 0xf1 => 0x6459,
    0xf2 => 0x6465, 0xf3 => 0x6477, 0xf4 => 0x6573, 0xf5 => 0x65a0,
    0xf6 => 0x66a1, 0xf7 => 0x66a0, 0xf8 => 0x669f, 0xf9 => 0x6705,
    0xfa => 0x6704, 0xfb => 0x6722, 0xfc => 0x69b1, 0xfd => 0x69b6,
    0xfe => 0x69c9,
  },
  0xe2 => {
    0x40 => 0x69a0, 0x41 => 0x69ce, 0x42 => 0x6996, 0x43 => 0x69b0,
    0x44 => 0x69ac, 0x45 => 0x69bc, 0x46 => 0x6991, 0x47 => 0x6999,
    0x48 => 0x698e, 0x49 => 0x69a7, 0x4a => 0x698d, 0x4b => 0x69a9,
    0x4c => 0x69be, 0x4d => 0x69af, 0x4e => 0x69bf, 0x4f => 0x69c4,
    0x50 => 0x69bd, 0x51 => 0x69a4, 0x52 => 0x69d4, 0x53 => 0x69b9,
    0x54 => 0x69ca, 0x55 => 0x699a, 0x56 => 0x69cf, 0x57 => 0x69b3,
    0x58 => 0x6993, 0x59 => 0x69aa, 0x5a => 0x69a1, 0x5b => 0x699e,
    0x5c => 0x69d9, 0x5d => 0x6997, 0x5e => 0x6990, 0x5f => 0x69c2,
    0x60 => 0x69b5, 0x61 => 0x69a5, 0x62 => 0x69c6, 0x63 => 0x6b4a,
    0x64 => 0x6b4d, 0x65 => 0x6b4b, 0x66 => 0x6b9e, 0x67 => 0x6b9f,
    0x68 => 0x6ba0, 0x69 => 0x6bc3, 0x6a => 0x6bc4, 0x6b => 0x6bfe,
    0x6c => 0x6ece, 0x6d => 0x6ef5, 0x6e => 0x6ef1, 0x6f => 0x6f03,
    0x70 => 0x6f25, 0x71 => 0x6ef8, 0x72 => 0x6f37, 0x73 => 0x6efb,
    0x74 => 0x6f2e, 0x75 => 0x6f09, 0x76 => 0x6f4e, 0x77 => 0x6f19,
    0x78 => 0x6f1a, 0x79 => 0x6f27, 0x7a => 0x6f18, 0x7b => 0x6f3b,
    0x7c => 0x6f12, 0x7d => 0x6eed, 0x7e => 0x6f0a, 0xa1 => 0x6f36,
    0xa2 => 0x6f73, 0xa3 => 0x6ef9, 0xa4 => 0x6eee, 0xa5 => 0x6f2d,
    0xa6 => 0x6f40, 0xa7 => 0x6f30, 0xa8 => 0x6f3c, 0xa9 => 0x6f35,
    0xaa => 0x6eeb, 0xab => 0x6f07, 0xac => 0x6f0e, 0xad => 0x6f43,
    0xae => 0x6f05, 0xaf => 0x6efd, 0xb0 => 0x6ef6, 0xb1 => 0x6f39,
    0xb2 => 0x6f1c, 0xb3 => 0x6efc, 0xb4 => 0x6f3a, 0xb5 => 0x6f1f,
    0xb6 => 0x6f0d, 0xb7 => 0x6f1e, 0xb8 => 0x6f08, 0xb9 => 0x6f21,
    0xba => 0x7187, 0xbb => 0x7190, 0xbc => 0x7189, 0xbd => 0x7180,
    0xbe => 0x7185, 0xbf => 0x7182, 0xc0 => 0x718f, 0xc1 => 0x717b,
    0xc2 => 0x7186, 0xc3 => 0x7181, 0xc4 => 0x7197, 0xc5 => 0x7244,
    0xc6 => 0x7253, 0xc7 => 0x7297, 0xc8 => 0x7295, 0xc9 => 0x7293,
    0xca => 0x7343, 0xcb => 0x734d, 0xcc => 0x7351, 0xcd => 0x734c,
    0xce => 0x7462, 0xcf => 0x7473, 0xd0 => 0x7471, 0xd1 => 0x7475,
    0xd2 => 0x7472, 0xd3 => 0x7467, 0xd4 => 0x746e, 0xd5 => 0x7500,
    0xd6 => 0x7502, 0xd7 => 0x7503, 0xd8 => 0x757d, 0xd9 => 0x7590,
    0xda => 0x7616, 0xdb => 0x7608, 0xdc => 0x760c, 0xdd => 0x7615,
    0xde => 0x7611, 0xdf => 0x760a, 0xe0 => 0x7614, 0xe1 => 0x76b8,
    0xe2 => 0x7781, 0xe3 => 0x777c, 0xe4 => 0x7785, 0xe5 => 0x7782,
    0xe6 => 0x776e, 0xe7 => 0x7780, 0xe8 => 0x776f, 0xe9 => 0x777e,
    0xea => 0x7783, 0xeb => 0x78b2, 0xec => 0x78aa, 0xed => 0x78b4,
    0xee => 0x78ad, 0xef => 0x78a8, 0xf0 => 0x787e, 0xf1 => 0x78ab,
    0xf2 => 0x789e, 0xf3 => 0x78a5, 0xf4 => 0x78a0, 0xf5 => 0x78ac,
    0xf6 => 0x78a2, 0xf7 => 0x78a4, 0xf8 => 0x7998, 0xf9 => 0x798a,
    0xfa => 0x798b, 0xfb => 0x7996, 0xfc => 0x7995, 0xfd => 0x7994,
    0xfe => 0x7993,
  },
  0xe3 => {
    0x40 => 0x7997, 0x41 => 0x7988, 0x42 => 0x7992, 0x43 => 0x7990,
    0x44 => 0x7a2b, 0x45 => 0x7a4a, 0x46 => 0x7a30, 0x47 => 0x7a2f,
    0x48 => 0x7a28, 0x49 => 0x7a26, 0x4a => 0x7aa8, 0x4b => 0x7aab,
    0x4c => 0x7aac, 0x4d => 0x7aee, 0x4e => 0x7b88, 0x4f => 0x7b9c,
    0x50 => 0x7b8a, 0x51 => 0x7b91, 0x52 => 0x7b90, 0x53 => 0x7b96,
    0x54 => 0x7b8d, 0x55 => 0x7b8c, 0x56 => 0x7b9b, 0x57 => 0x7b8e,
    0x58 => 0x7b85, 0x59 => 0x7b98, 0x5a => 0x5284, 0x5b => 0x7b99,
    0x5c => 0x7ba4, 0x5d => 0x7b82, 0x5e => 0x7cbb, 0x5f => 0x7cbf,
    0x60 => 0x7cbc, 0x61 => 0x7cba, 0x62 => 0x7da7, 0x63 => 0x7db7,
    0x64 => 0x7dc2, 0x65 => 0x7da3, 0x66 => 0x7daa, 0x67 => 0x7dc1,
    0x68 => 0x7dc0, 0x69 => 0x7dc5, 0x6a => 0x7d9d, 0x6b => 0x7dce,
    0x6c => 0x7dc4, 0x6d => 0x7dc6, 0x6e => 0x7dcb, 0x6f => 0x7dcc,
    0x70 => 0x7daf, 0x71 => 0x7db9, 0x72 => 0x7d96, 0x73 => 0x7dbc,
    0x74 => 0x7d9f, 0x75 => 0x7da6, 0x76 => 0x7dae, 0x77 => 0x7da9,
    0x78 => 0x7da1, 0x79 => 0x7dc9, 0x7a => 0x7f73, 0x7b => 0x7fe2,
    0x7c => 0x7fe3, 0x7d => 0x7fe5, 0x7e => 0x7fde, 0xa1 => 0x8024,
    0xa2 => 0x805d, 0xa3 => 0x805c, 0xa4 => 0x8189, 0xa5 => 0x8186,
    0xa6 => 0x8183, 0xa7 => 0x8187, 0xa8 => 0x818d, 0xa9 => 0x818c,
    0xaa => 0x818b, 0xab => 0x8215, 0xac => 0x8497, 0xad => 0x84a4,
    0xae => 0x84a1, 0xaf => 0x849f, 0xb0 => 0x84ba, 0xb1 => 0x84ce,
    0xb2 => 0x84c2, 0xb3 => 0x84ac, 0xb4 => 0x84ae, 0xb5 => 0x84ab,
    0xb6 => 0x84b9, 0xb7 => 0x84b4, 0xb8 => 0x84c1, 0xb9 => 0x84cd,
    0xba => 0x84aa, 0xbb => 0x849a, 0xbc => 0x84b1, 0xbd => 0x84d0,
    0xbe => 0x849d, 0xbf => 0x84a7, 0xc0 => 0x84bb, 0xc1 => 0x84a2,
    0xc2 => 0x8494, 0xc3 => 0x84c7, 0xc4 => 0x84cc, 0xc5 => 0x849b,
    0xc6 => 0x84a9, 0xc7 => 0x84af, 0xc8 => 0x84a8, 0xc9 => 0x84d6,
    0xca => 0x8498, 0xcb => 0x84b6, 0xcc => 0x84cf, 0xcd => 0x84a0,
    0xce => 0x84d7, 0xcf => 0x84d4, 0xd0 => 0x84d2, 0xd1 => 0x84db,
    0xd2 => 0x84b0, 0xd3 => 0x8491, 0xd4 => 0x8661, 0xd5 => 0x8733,
    0xd6 => 0x8723, 0xd7 => 0x8728, 0xd8 => 0x876b, 0xd9 => 0x8740,
    0xda => 0x872e, 0xdb => 0x871e, 0xdc => 0x8721, 0xdd => 0x8719,
    0xde => 0x871b, 0xdf => 0x8743, 0xe0 => 0x872c, 0xe1 => 0x8741,
    0xe2 => 0x873e, 0xe3 => 0x8746, 0xe4 => 0x8720, 0xe5 => 0x8732,
    0xe6 => 0x872a, 0xe7 => 0x872d, 0xe8 => 0x873c, 0xe9 => 0x8712,
    0xea => 0x873a, 0xeb => 0x8731, 0xec => 0x8735, 0xed => 0x8742,
    0xee => 0x8726, 0xef => 0x8727, 0xf0 => 0x8738, 0xf1 => 0x8724,
    0xf2 => 0x871a, 0xf3 => 0x8730, 0xf4 => 0x8711, 0xf5 => 0x88f7,
    0xf6 => 0x88e7, 0xf7 => 0x88f1, 0xf8 => 0x88f2, 0xf9 => 0x88fa,
    0xfa => 0x88fe, 0xfb => 0x88ee, 0xfc => 0x88fc, 0xfd => 0x88f6,
    0xfe => 0x88fb,
  },
  0xe4 => {
    0x40 => 0x88f0, 0x41 => 0x88ec, 0x42 => 0x88eb, 0x43 => 0x899d,
    0x44 => 0x89a1, 0x45 => 0x899f, 0x46 => 0x899e, 0x47 => 0x89e9,
    0x48 => 0x89eb, 0x49 => 0x89e8, 0x4a => 0x8aab, 0x4b => 0x8a99,
    0x4c => 0x8a8b, 0x4d => 0x8a92, 0x4e => 0x8a8f, 0x4f => 0x8a96,
    0x50 => 0x8c3d, 0x51 => 0x8c68, 0x52 => 0x8c69, 0x53 => 0x8cd5,
    0x54 => 0x8ccf, 0x55 => 0x8cd7, 0x56 => 0x8d96, 0x57 => 0x8e09,
    0x58 => 0x8e02, 0x59 => 0x8dff, 0x5a => 0x8e0d, 0x5b => 0x8dfd,
    0x5c => 0x8e0a, 0x5d => 0x8e03, 0x5e => 0x8e07, 0x5f => 0x8e06,
    0x60 => 0x8e05, 0x61 => 0x8dfe, 0x62 => 0x8e00, 0x63 => 0x8e04,
    0x64 => 0x8f10, 0x65 => 0x8f11, 0x66 => 0x8f0e, 0x67 => 0x8f0d,
    0x68 => 0x9123, 0x69 => 0x911c, 0x6a => 0x9120, 0x6b => 0x9122,
    0x6c => 0x911f, 0x6d => 0x911d, 0x6e => 0x911a, 0x6f => 0x9124,
    0x70 => 0x9121, 0x71 => 0x911b, 0x72 => 0x917a, 0x73 => 0x9172,
    0x74 => 0x9179, 0x75 => 0x9173, 0x76 => 0x92a5, 0x77 => 0x92a4,
    0x78 => 0x9276, 0x79 => 0x929b, 0x7a => 0x927a, 0x7b => 0x92a0,
    0x7c => 0x9294, 0x7d => 0x92aa, 0x7e => 0x928d, 0xa1 => 0x92a6,
    0xa2 => 0x929a, 0xa3 => 0x92ab, 0xa4 => 0x9279, 0xa5 => 0x9297,
    0xa6 => 0x927f, 0xa7 => 0x92a3, 0xa8 => 0x92ee, 0xa9 => 0x928e,
    0xaa => 0x9282, 0xab => 0x9295, 0xac => 0x92a2, 0xad => 0x927d,
    0xae => 0x9288, 0xaf => 0x92a1, 0xb0 => 0x928a, 0xb1 => 0x9286,
    0xb2 => 0x928c, 0xb3 => 0x9299, 0xb4 => 0x92a7, 0xb5 => 0x927e,
    0xb6 => 0x9287, 0xb7 => 0x92a9, 0xb8 => 0x929d, 0xb9 => 0x928b,
    0xba => 0x922d, 0xbb => 0x969e, 0xbc => 0x96a1, 0xbd => 0x96ff,
    0xbe => 0x9758, 0xbf => 0x977d, 0xc0 => 0x977a, 0xc1 => 0x977e,
    0xc2 => 0x9783, 0xc3 => 0x9780, 0xc4 => 0x9782, 0xc5 => 0x977b,
    0xc6 => 0x9784, 0xc7 => 0x9781, 0xc8 => 0x977f, 0xc9 => 0x97ce,
    0xca => 0x97cd, 0xcb => 0x9816, 0xcc => 0x98ad, 0xcd => 0x98ae,
    0xce => 0x9902, 0xcf => 0x9900, 0xd0 => 0x9907, 0xd1 => 0x999d,
    0xd2 => 0x999c, 0xd3 => 0x99c3, 0xd4 => 0x99b9, 0xd5 => 0x99bb,
    0xd6 => 0x99ba, 0xd7 => 0x99c2, 0xd8 => 0x99bd, 0xd9 => 0x99c7,
    0xda => 0x9ab1, 0xdb => 0x9ae3, 0xdc => 0x9ae7, 0xdd => 0x9b3e,
    0xde => 0x9b3f, 0xdf => 0x9b60, 0xe0 => 0x9b61, 0xe1 => 0x9b5f,
    0xe2 => 0x9cf1, 0xe3 => 0x9cf2, 0xe4 => 0x9cf5, 0xe5 => 0x9ea7,
    0xe6 => 0x50ff, 0xe7 => 0x5103, 0xe8 => 0x5130, 0xe9 => 0x50f8,
    0xea => 0x5106, 0xeb => 0x5107, 0xec => 0x50f6, 0xed => 0x50fe,
    0xee => 0x510b, 0xef => 0x510c, 0xf0 => 0x50fd, 0xf1 => 0x510a,
    0xf2 => 0x528b, 0xf3 => 0x528c, 0xf4 => 0x52f1, 0xf5 => 0x52ef,
    0xf6 => 0x5648, 0xf7 => 0x5642, 0xf8 => 0x564c, 0xf9 => 0x5635,
    0xfa => 0x5641, 0xfb => 0x564a, 0xfc => 0x5649, 0xfd => 0x5646,
    0xfe => 0x5658,
  },
  0xe5 => {
    0x40 => 0x565a, 0x41 => 0x5640, 0x42 => 0x5633, 0x43 => 0x563d,
    0x44 => 0x562c, 0x45 => 0x563e, 0x46 => 0x5638, 0x47 => 0x562a,
    0x48 => 0x563a, 0x49 => 0x571a, 0x4a => 0x58ab, 0x4b => 0x589d,
    0x4c => 0x58b1, 0x4d => 0x58a0, 0x4e => 0x58a3, 0x4f => 0x58af,
    0x50 => 0x58ac, 0x51 => 0x58a5, 0x52 => 0x58a1, 0x53 => 0x58ff,
    0x54 => 0x5aff, 0x55 => 0x5af4, 0x56 => 0x5afd, 0x57 => 0x5af7,
    0x58 => 0x5af6, 0x59 => 0x5b03, 0x5a => 0x5af8, 0x5b => 0x5b02,
    0x5c => 0x5af9, 0x5d => 0x5b01, 0x5e => 0x5b07, 0x5f => 0x5b05,
    0x60 => 0x5b0f, 0x61 => 0x5c67, 0x62 => 0x5d99, 0x63 => 0x5d97,
    0x64 => 0x5d9f, 0x65 => 0x5d92, 0x66 => 0x5da2, 0x67 => 0x5d93,
    0x68 => 0x5d95, 0x69 => 0x5da0, 0x6a => 0x5d9c, 0x6b => 0x5da1,
    0x6c => 0x5d9a, 0x6d => 0x5d9e, 0x6e => 0x5e69, 0x6f => 0x5e5d,
    0x70 => 0x5e60, 0x71 => 0x5e5c, 0x72 => 0x7df3, 0x73 => 0x5edb,
    0x74 => 0x5ede, 0x75 => 0x5ee1, 0x76 => 0x5f49, 0x77 => 0x5fb2,
    0x78 => 0x618b, 0x79 => 0x6183, 0x7a => 0x6179, 0x7b => 0x61b1,
    0x7c => 0x61b0, 0x7d => 0x61a2, 0x7e => 0x6189, 0xa1 => 0x619b,
    0xa2 => 0x6193, 0xa3 => 0x61af, 0xa4 => 0x61ad, 0xa5 => 0x619f,
    0xa6 => 0x6192, 0xa7 => 0x61aa, 0xa8 => 0x61a1, 0xa9 => 0x618d,
    0xaa => 0x6166, 0xab => 0x61b3, 0xac => 0x622d, 0xad => 0x646e,
    0xae => 0x6470, 0xaf => 0x6496, 0xb0 => 0x64a0, 0xb1 => 0x6485,
    0xb2 => 0x6497, 0xb3 => 0x649c, 0xb4 => 0x648f, 0xb5 => 0x648b,
    0xb6 => 0x648a, 0xb7 => 0x648c, 0xb8 => 0x64a3, 0xb9 => 0x649f,
    0xba => 0x6468, 0xbb => 0x64b1, 0xbc => 0x6498, 0xbd => 0x6576,
    0xbe => 0x657a, 0xbf => 0x6579, 0xc0 => 0x657b, 0xc1 => 0x65b2,
    0xc2 => 0x65b3, 0xc3 => 0x66b5, 0xc4 => 0x66b0, 0xc5 => 0x66a9,
    0xc6 => 0x66b2, 0xc7 => 0x66b7, 0xc8 => 0x66aa, 0xc9 => 0x66af,
    0xca => 0x6a00, 0xcb => 0x6a06, 0xcc => 0x6a17, 0xcd => 0x69e5,
    0xce => 0x69f8, 0xcf => 0x6a15, 0xd0 => 0x69f1, 0xd1 => 0x69e4,
    0xd2 => 0x6a20, 0xd3 => 0x69ff, 0xd4 => 0x69ec, 0xd5 => 0x69e2,
    0xd6 => 0x6a1b, 0xd7 => 0x6a1d, 0xd8 => 0x69fe, 0xd9 => 0x6a27,
    0xda => 0x69f2, 0xdb => 0x69ee, 0xdc => 0x6a14, 0xdd => 0x69f7,
    0xde => 0x69e7, 0xdf => 0x6a40, 0xe0 => 0x6a08, 0xe1 => 0x69e6,
    0xe2 => 0x69fb, 0xe3 => 0x6a0d, 0xe4 => 0x69fc, 0xe5 => 0x69eb,
    0xe6 => 0x6a09, 0xe7 => 0x6a04, 0xe8 => 0x6a18, 0xe9 => 0x6a25,
    0xea => 0x6a0f, 0xeb => 0x69f6, 0xec => 0x6a26, 0xed => 0x6a07,
    0xee => 0x69f4, 0xef => 0x6a16, 0xf0 => 0x6b51, 0xf1 => 0x6ba5,
    0xf2 => 0x6ba3, 0xf3 => 0x6ba2, 0xf4 => 0x6ba6, 0xf5 => 0x6c01,
    0xf6 => 0x6c00, 0xf7 => 0x6bff, 0xf8 => 0x6c02, 0xf9 => 0x6f41,
    0xfa => 0x6f26, 0xfb => 0x6f7e, 0xfc => 0x6f87, 0xfd => 0x6fc6,
    0xfe => 0x6f92,
  },
  0xe6 => {
    0x40 => 0x6f8d, 0x41 => 0x6f89, 0x42 => 0x6f8c, 0x43 => 0x6f62,
    0x44 => 0x6f4f, 0x45 => 0x6f85, 0x46 => 0x6f5a, 0x47 => 0x6f96,
    0x48 => 0x6f76, 0x49 => 0x6f6c, 0x4a => 0x6f82, 0x4b => 0x6f55,
    0x4c => 0x6f72, 0x4d => 0x6f52, 0x4e => 0x6f50, 0x4f => 0x6f57,
    0x50 => 0x6f94, 0x51 => 0x6f93, 0x52 => 0x6f5d, 0x53 => 0x6f00,
    0x54 => 0x6f61, 0x55 => 0x6f6b, 0x56 => 0x6f7d, 0x57 => 0x6f67,
    0x58 => 0x6f90, 0x59 => 0x6f53, 0x5a => 0x6f8b, 0x5b => 0x6f69,
    0x5c => 0x6f7f, 0x5d => 0x6f95, 0x5e => 0x6f63, 0x5f => 0x6f77,
    0x60 => 0x6f6a, 0x61 => 0x6f7b, 0x62 => 0x71b2, 0x63 => 0x71af,
    0x64 => 0x719b, 0x65 => 0x71b0, 0x66 => 0x71a0, 0x67 => 0x719a,
    0x68 => 0x71a9, 0x69 => 0x71b5, 0x6a => 0x719d, 0x6b => 0x71a5,
    0x6c => 0x719e, 0x6d => 0x71a4, 0x6e => 0x71a1, 0x6f => 0x71aa,
    0x70 => 0x719c, 0x71 => 0x71a7, 0x72 => 0x71b3, 0x73 => 0x7298,
    0x74 => 0x729a, 0x75 => 0x7358, 0x76 => 0x7352, 0x77 => 0x735e,
    0x78 => 0x735f, 0x79 => 0x7360, 0x7a => 0x735d, 0x7b => 0x735b,
    0x7c => 0x7361, 0x7d => 0x735a, 0x7e => 0x7359, 0xa1 => 0x7362,
    0xa2 => 0x7487, 0xa3 => 0x7489, 0xa4 => 0x748a, 0xa5 => 0x7486,
    0xa6 => 0x7481, 0xa7 => 0x747d, 0xa8 => 0x7485, 0xa9 => 0x7488,
    0xaa => 0x747c, 0xab => 0x7479, 0xac => 0x7508, 0xad => 0x7507,
    0xae => 0x757e, 0xaf => 0x7625, 0xb0 => 0x761e, 0xb1 => 0x7619,
    0xb2 => 0x761d, 0xb3 => 0x761c, 0xb4 => 0x7623, 0xb5 => 0x761a,
    0xb6 => 0x7628, 0xb7 => 0x761b, 0xb8 => 0x769c, 0xb9 => 0x769d,
    0xba => 0x769e, 0xbb => 0x769b, 0xbc => 0x778d, 0xbd => 0x778f,
    0xbe => 0x7789, 0xbf => 0x7788, 0xc0 => 0x78cd, 0xc1 => 0x78bb,
    0xc2 => 0x78cf, 0xc3 => 0x78cc, 0xc4 => 0x78d1, 0xc5 => 0x78ce,
    0xc6 => 0x78d4, 0xc7 => 0x78c8, 0xc8 => 0x78c3, 0xc9 => 0x78c4,
    0xca => 0x78c9, 0xcb => 0x799a, 0xcc => 0x79a1, 0xcd => 0x79a0,
    0xce => 0x799c, 0xcf => 0x79a2, 0xd0 => 0x799b, 0xd1 => 0x6b76,
    0xd2 => 0x7a39, 0xd3 => 0x7ab2, 0xd4 => 0x7ab4, 0xd5 => 0x7ab3,
    0xd6 => 0x7bb7, 0xd7 => 0x7bcb, 0xd8 => 0x7bbe, 0xd9 => 0x7bac,
    0xda => 0x7bce, 0xdb => 0x7baf, 0xdc => 0x7bb9, 0xdd => 0x7bca,
    0xde => 0x7bb5, 0xdf => 0x7cc5, 0xe0 => 0x7cc8, 0xe1 => 0x7ccc,
    0xe2 => 0x7ccb, 0xe3 => 0x7df7, 0xe4 => 0x7ddb, 0xe5 => 0x7dea,
    0xe6 => 0x7de7, 0xe7 => 0x7dd7, 0xe8 => 0x7de1, 0xe9 => 0x7e03,
    0xea => 0x7dfa, 0xeb => 0x7de6, 0xec => 0x7df6, 0xed => 0x7df1,
    0xee => 0x7df0, 0xef => 0x7dee, 0xf0 => 0x7ddf, 0xf1 => 0x7f76,
    0xf2 => 0x7fac, 0xf3 => 0x7fb0, 0xf4 => 0x7fad, 0xf5 => 0x7fed,
    0xf6 => 0x7feb, 0xf7 => 0x7fea, 0xf8 => 0x7fec, 0xf9 => 0x7fe6,
    0xfa => 0x7fe8, 0xfb => 0x8064, 0xfc => 0x8067, 0xfd => 0x81a3,
    0xfe => 0x819f,
  },
  0xe7 => {
    0x40 => 0x819e, 0x41 => 0x8195, 0x42 => 0x81a2, 0x43 => 0x8199,
    0x44 => 0x8197, 0x45 => 0x8216, 0x46 => 0x824f, 0x47 => 0x8253,
    0x48 => 0x8252, 0x49 => 0x8250, 0x4a => 0x824e, 0x4b => 0x8251,
    0x4c => 0x8524, 0x4d => 0x853b, 0x4e => 0x850f, 0x4f => 0x8500,
    0x50 => 0x8529, 0x51 => 0x850e, 0x52 => 0x8509, 0x53 => 0x850d,
    0x54 => 0x851f, 0x55 => 0x850a, 0x56 => 0x8527, 0x57 => 0x851c,
    0x58 => 0x84fb, 0x59 => 0x852b, 0x5a => 0x84fa, 0x5b => 0x8508,
    0x5c => 0x850c, 0x5d => 0x84f4, 0x5e => 0x852a, 0x5f => 0x84f2,
    0x60 => 0x8515, 0x61 => 0x84f7, 0x62 => 0x84eb, 0x63 => 0x84f3,
    0x64 => 0x84fc, 0x65 => 0x8512, 0x66 => 0x84ea, 0x67 => 0x84e9,
    0x68 => 0x8516, 0x69 => 0x84fe, 0x6a => 0x8528, 0x6b => 0x851d,
    0x6c => 0x852e, 0x6d => 0x8502, 0x6e => 0x84fd, 0x6f => 0x851e,
    0x70 => 0x84f6, 0x71 => 0x8531, 0x72 => 0x8526, 0x73 => 0x84e7,
    0x74 => 0x84e8, 0x75 => 0x84f0, 0x76 => 0x84ef, 0x77 => 0x84f9,
    0x78 => 0x8518, 0x79 => 0x8520, 0x7a => 0x8530, 0x7b => 0x850b,
    0x7c => 0x8519, 0x7d => 0x852f, 0x7e => 0x8662, 0xa1 => 0x8756,
    0xa2 => 0x8763, 0xa3 => 0x8764, 0xa4 => 0x8777, 0xa5 => 0x87e1,
    0xa6 => 0x8773, 0xa7 => 0x8758, 0xa8 => 0x8754, 0xa9 => 0x875b,
    0xaa => 0x8752, 0xab => 0x8761, 0xac => 0x875a, 0xad => 0x8751,
    0xae => 0x875e, 0xaf => 0x876d, 0xb0 => 0x876a, 0xb1 => 0x8750,
    0xb2 => 0x874e, 0xb3 => 0x875f, 0xb4 => 0x875d, 0xb5 => 0x876f,
    0xb6 => 0x876c, 0xb7 => 0x877a, 0xb8 => 0x876e, 0xb9 => 0x875c,
    0xba => 0x8765, 0xbb => 0x874f, 0xbc => 0x877b, 0xbd => 0x8775,
    0xbe => 0x8762, 0xbf => 0x8767, 0xc0 => 0x8769, 0xc1 => 0x885a,
    0xc2 => 0x8905, 0xc3 => 0x890c, 0xc4 => 0x8914, 0xc5 => 0x890b,
    0xc6 => 0x8917, 0xc7 => 0x8918, 0xc8 => 0x8919, 0xc9 => 0x8906,
    0xca => 0x8916, 0xcb => 0x8911, 0xcc => 0x890e, 0xcd => 0x8909,
    0xce => 0x89a2, 0xcf => 0x89a4, 0xd0 => 0x89a3, 0xd1 => 0x89ed,
    0xd2 => 0x89f0, 0xd3 => 0x89ec, 0xd4 => 0x8acf, 0xd5 => 0x8ac6,
    0xd6 => 0x8ab8, 0xd7 => 0x8ad3, 0xd8 => 0x8ad1, 0xd9 => 0x8ad4,
    0xda => 0x8ad5, 0xdb => 0x8abb, 0xdc => 0x8ad7, 0xdd => 0x8abe,
    0xde => 0x8ac0, 0xdf => 0x8ac5, 0xe0 => 0x8ad8, 0xe1 => 0x8ac3,
    0xe2 => 0x8aba, 0xe3 => 0x8abd, 0xe4 => 0x8ad9, 0xe5 => 0x8c3e,
    0xe6 => 0x8c4d, 0xe7 => 0x8c8f, 0xe8 => 0x8ce5, 0xe9 => 0x8cdf,
    0xea => 0x8cd9, 0xeb => 0x8ce8, 0xec => 0x8cda, 0xed => 0x8cdd,
    0xee => 0x8ce7, 0xef => 0x8da0, 0xf0 => 0x8d9c, 0xf1 => 0x8da1,
    0xf2 => 0x8d9b, 0xf3 => 0x8e20, 0xf4 => 0x8e23, 0xf5 => 0x8e25,
    0xf6 => 0x8e24, 0xf7 => 0x8e2e, 0xf8 => 0x8e15, 0xf9 => 0x8e1b,
    0xfa => 0x8e16, 0xfb => 0x8e11, 0xfc => 0x8e19, 0xfd => 0x8e26,
    0xfe => 0x8e27,
  },
  0xe8 => {
    0x40 => 0x8e14, 0x41 => 0x8e12, 0x42 => 0x8e18, 0x43 => 0x8e13,
    0x44 => 0x8e1c, 0x45 => 0x8e17, 0x46 => 0x8e1a, 0x47 => 0x8f2c,
    0x48 => 0x8f24, 0x49 => 0x8f18, 0x4a => 0x8f1a, 0x4b => 0x8f20,
    0x4c => 0x8f23, 0x4d => 0x8f16, 0x4e => 0x8f17, 0x4f => 0x9073,
    0x50 => 0x9070, 0x51 => 0x906f, 0x52 => 0x9067, 0x53 => 0x906b,
    0x54 => 0x912f, 0x55 => 0x912b, 0x56 => 0x9129, 0x57 => 0x912a,
    0x58 => 0x9132, 0x59 => 0x9126, 0x5a => 0x912e, 0x5b => 0x9185,
    0x5c => 0x9186, 0x5d => 0x918a, 0x5e => 0x9181, 0x5f => 0x9182,
    0x60 => 0x9184, 0x61 => 0x9180, 0x62 => 0x92d0, 0x63 => 0x92c3,
    0x64 => 0x92c4, 0x65 => 0x92c0, 0x66 => 0x92d9, 0x67 => 0x92b6,
    0x68 => 0x92cf, 0x69 => 0x92f1, 0x6a => 0x92df, 0x6b => 0x92d8,
    0x6c => 0x92e9, 0x6d => 0x92d7, 0x6e => 0x92dd, 0x6f => 0x92cc,
    0x70 => 0x92ef, 0x71 => 0x92c2, 0x72 => 0x92e8, 0x73 => 0x92ca,
    0x74 => 0x92c8, 0x75 => 0x92ce, 0x76 => 0x92e6, 0x77 => 0x92cd,
    0x78 => 0x92d5, 0x79 => 0x92c9, 0x7a => 0x92e0, 0x7b => 0x92de,
    0x7c => 0x92e7, 0x7d => 0x92d1, 0x7e => 0x92d3, 0xa1 => 0x92b5,
    0xa2 => 0x92e1, 0xa3 => 0x92c6, 0xa4 => 0x92b4, 0xa5 => 0x957c,
    0xa6 => 0x95ac, 0xa7 => 0x95ab, 0xa8 => 0x95ae, 0xa9 => 0x95b0,
    0xaa => 0x96a4, 0xab => 0x96a2, 0xac => 0x96d3, 0xad => 0x9705,
    0xae => 0x9708, 0xaf => 0x9702, 0xb0 => 0x975a, 0xb1 => 0x978a,
    0xb2 => 0x978e, 0xb3 => 0x9788, 0xb4 => 0x97d0, 0xb5 => 0x97cf,
    0xb6 => 0x981e, 0xb7 => 0x981d, 0xb8 => 0x9826, 0xb9 => 0x9829,
    0xba => 0x9828, 0xbb => 0x9820, 0xbc => 0x981b, 0xbd => 0x9827,
    0xbe => 0x98b2, 0xbf => 0x9908, 0xc0 => 0x98fa, 0xc1 => 0x9911,
    0xc2 => 0x9914, 0xc3 => 0x9916, 0xc4 => 0x9917, 0xc5 => 0x9915,
    0xc6 => 0x99dc, 0xc7 => 0x99cd, 0xc8 => 0x99cf, 0xc9 => 0x99d3,
    0xca => 0x99d4, 0xcb => 0x99ce, 0xcc => 0x99c9, 0xcd => 0x99d6,
    0xce => 0x99d8, 0xcf => 0x99cb, 0xd0 => 0x99d7, 0xd1 => 0x99cc,
    0xd2 => 0x9ab3, 0xd3 => 0x9aec, 0xd4 => 0x9aeb, 0xd5 => 0x9af3,
    0xd6 => 0x9af2, 0xd7 => 0x9af1, 0xd8 => 0x9b46, 0xd9 => 0x9b43,
    0xda => 0x9b67, 0xdb => 0x9b74, 0xdc => 0x9b71, 0xdd => 0x9b66,
    0xde => 0x9b76, 0xdf => 0x9b75, 0xe0 => 0x9b70, 0xe1 => 0x9b68,
    0xe2 => 0x9b64, 0xe3 => 0x9b6c, 0xe4 => 0x9cfc, 0xe5 => 0x9cfa,
    0xe6 => 0x9cfd, 0xe7 => 0x9cff, 0xe8 => 0x9cf7, 0xe9 => 0x9d07,
    0xea => 0x9d00, 0xeb => 0x9cf9, 0xec => 0x9cfb, 0xed => 0x9d08,
    0xee => 0x9d05, 0xef => 0x9d04, 0xf0 => 0x9e83, 0xf1 => 0x9ed3,
    0xf2 => 0x9f0f, 0xf3 => 0x9f10, 0xf4 => 0x511c, 0xf5 => 0x5113,
    0xf6 => 0x5117, 0xf7 => 0x511a, 0xf8 => 0x5111, 0xf9 => 0x51de,
    0xfa => 0x5334, 0xfb => 0x53e1, 0xfc => 0x5670, 0xfd => 0x5660,
    0xfe => 0x566e,
  },
  0xe9 => {
    0x40 => 0x5673, 0x41 => 0x5666, 0x42 => 0x5663, 0x43 => 0x566d,
    0x44 => 0x5672, 0x45 => 0x565e, 0x46 => 0x5677, 0x47 => 0x571c,
    0x48 => 0x571b, 0x49 => 0x58c8, 0x4a => 0x58bd, 0x4b => 0x58c9,
    0x4c => 0x58bf, 0x4d => 0x58ba, 0x4e => 0x58c2, 0x4f => 0x58bc,
    0x50 => 0x58c6, 0x51 => 0x5b17, 0x52 => 0x5b19, 0x53 => 0x5b1b,
    0x54 => 0x5b21, 0x55 => 0x5b14, 0x56 => 0x5b13, 0x57 => 0x5b10,
    0x58 => 0x5b16, 0x59 => 0x5b28, 0x5a => 0x5b1a, 0x5b => 0x5b20,
    0x5c => 0x5b1e, 0x5d => 0x5bef, 0x5e => 0x5dac, 0x5f => 0x5db1,
    0x60 => 0x5da9, 0x61 => 0x5da7, 0x62 => 0x5db5, 0x63 => 0x5db0,
    0x64 => 0x5dae, 0x65 => 0x5daa, 0x66 => 0x5da8, 0x67 => 0x5db2,
    0x68 => 0x5dad, 0x69 => 0x5daf, 0x6a => 0x5db4, 0x6b => 0x5e67,
    0x6c => 0x5e68, 0x6d => 0x5e66, 0x6e => 0x5e6f, 0x6f => 0x5ee9,
    0x70 => 0x5ee7, 0x71 => 0x5ee6, 0x72 => 0x5ee8, 0x73 => 0x5ee5,
    0x74 => 0x5f4b, 0x75 => 0x5fbc, 0x76 => 0x619d, 0x77 => 0x61a8,
    0x78 => 0x6196, 0x79 => 0x61c5, 0x7a => 0x61b4, 0x7b => 0x61c6,
    0x7c => 0x61c1, 0x7d => 0x61cc, 0x7e => 0x61ba, 0xa1 => 0x61bf,
    0xa2 => 0x61b8, 0xa3 => 0x618c, 0xa4 => 0x64d7, 0xa5 => 0x64d6,
    0xa6 => 0x64d0, 0xa7 => 0x64cf, 0xa8 => 0x64c9, 0xa9 => 0x64bd,
    0xaa => 0x6489, 0xab => 0x64c3, 0xac => 0x64db, 0xad => 0x64f3,
    0xae => 0x64d9, 0xaf => 0x6533, 0xb0 => 0x657f, 0xb1 => 0x657c,
    0xb2 => 0x65a2, 0xb3 => 0x66c8, 0xb4 => 0x66be, 0xb5 => 0x66c0,
    0xb6 => 0x66ca, 0xb7 => 0x66cb, 0xb8 => 0x66cf, 0xb9 => 0x66bd,
    0xba => 0x66bb, 0xbb => 0x66ba, 0xbc => 0x66cc, 0xbd => 0x6723,
    0xbe => 0x6a34, 0xbf => 0x6a66, 0xc0 => 0x6a49, 0xc1 => 0x6a67,
    0xc2 => 0x6a32, 0xc3 => 0x6a68, 0xc4 => 0x6a3e, 0xc5 => 0x6a5d,
    0xc6 => 0x6a6d, 0xc7 => 0x6a76, 0xc8 => 0x6a5b, 0xc9 => 0x6a51,
    0xca => 0x6a28, 0xcb => 0x6a5a, 0xcc => 0x6a3b, 0xcd => 0x6a3f,
    0xce => 0x6a41, 0xcf => 0x6a6a, 0xd0 => 0x6a64, 0xd1 => 0x6a50,
    0xd2 => 0x6a4f, 0xd3 => 0x6a54, 0xd4 => 0x6a6f, 0xd5 => 0x6a69,
    0xd6 => 0x6a60, 0xd7 => 0x6a3c, 0xd8 => 0x6a5e, 0xd9 => 0x6a56,
    0xda => 0x6a55, 0xdb => 0x6a4d, 0xdc => 0x6a4e, 0xdd => 0x6a46,
    0xde => 0x6b55, 0xdf => 0x6b54, 0xe0 => 0x6b56, 0xe1 => 0x6ba7,
    0xe2 => 0x6baa, 0xe3 => 0x6bab, 0xe4 => 0x6bc8, 0xe5 => 0x6bc7,
    0xe6 => 0x6c04, 0xe7 => 0x6c03, 0xe8 => 0x6c06, 0xe9 => 0x6fad,
    0xea => 0x6fcb, 0xeb => 0x6fa3, 0xec => 0x6fc7, 0xed => 0x6fbc,
    0xee => 0x6fce, 0xef => 0x6fc8, 0xf0 => 0x6f5e, 0xf1 => 0x6fc4,
    0xf2 => 0x6fbd, 0xf3 => 0x6f9e, 0xf4 => 0x6fca, 0xf5 => 0x6fa8,
    0xf6 => 0x7004, 0xf7 => 0x6fa5, 0xf8 => 0x6fae, 0xf9 => 0x6fba,
    0xfa => 0x6fac, 0xfb => 0x6faa, 0xfc => 0x6fcf, 0xfd => 0x6fbf,
    0xfe => 0x6fb8,
  },
  0xea => {
    0x40 => 0x6fa2, 0x41 => 0x6fc9, 0x42 => 0x6fab, 0x43 => 0x6fcd,
    0x44 => 0x6faf, 0x45 => 0x6fb2, 0x46 => 0x6fb0, 0x47 => 0x71c5,
    0x48 => 0x71c2, 0x49 => 0x71bf, 0x4a => 0x71b8, 0x4b => 0x71d6,
    0x4c => 0x71c0, 0x4d => 0x71c1, 0x4e => 0x71cb, 0x4f => 0x71d4,
    0x50 => 0x71ca, 0x51 => 0x71c7, 0x52 => 0x71cf, 0x53 => 0x71bd,
    0x54 => 0x71d8, 0x55 => 0x71bc, 0x56 => 0x71c6, 0x57 => 0x71da,
    0x58 => 0x71db, 0x59 => 0x729d, 0x5a => 0x729e, 0x5b => 0x7369,
    0x5c => 0x7366, 0x5d => 0x7367, 0x5e => 0x736c, 0x5f => 0x7365,
    0x60 => 0x736b, 0x61 => 0x736a, 0x62 => 0x747f, 0x63 => 0x749a,
    0x64 => 0x74a0, 0x65 => 0x7494, 0x66 => 0x7492, 0x67 => 0x7495,
    0x68 => 0x74a1, 0x69 => 0x750b, 0x6a => 0x7580, 0x6b => 0x762f,
    0x6c => 0x762d, 0x6d => 0x7631, 0x6e => 0x763d, 0x6f => 0x7633,
    0x70 => 0x763c, 0x71 => 0x7635, 0x72 => 0x7632, 0x73 => 0x7630,
    0x74 => 0x76bb, 0x75 => 0x76e6, 0x76 => 0x779a, 0x77 => 0x779d,
    0x78 => 0x77a1, 0x79 => 0x779c, 0x7a => 0x779b, 0x7b => 0x77a2,
    0x7c => 0x77a3, 0x7d => 0x7795, 0x7e => 0x7799, 0xa1 => 0x7797,
    0xa2 => 0x78dd, 0xa3 => 0x78e9, 0xa4 => 0x78e5, 0xa5 => 0x78ea,
    0xa6 => 0x78de, 0xa7 => 0x78e3, 0xa8 => 0x78db, 0xa9 => 0x78e1,
    0xaa => 0x78e2, 0xab => 0x78ed, 0xac => 0x78df, 0xad => 0x78e0,
    0xae => 0x79a4, 0xaf => 0x7a44, 0xb0 => 0x7a48, 0xb1 => 0x7a47,
    0xb2 => 0x7ab6, 0xb3 => 0x7ab8, 0xb4 => 0x7ab5, 0xb5 => 0x7ab1,
    0xb6 => 0x7ab7, 0xb7 => 0x7bde, 0xb8 => 0x7be3, 0xb9 => 0x7be7,
    0xba => 0x7bdd, 0xbb => 0x7bd5, 0xbc => 0x7be5, 0xbd => 0x7bda,
    0xbe => 0x7be8, 0xbf => 0x7bf9, 0xc0 => 0x7bd4, 0xc1 => 0x7bea,
    0xc2 => 0x7be2, 0xc3 => 0x7bdc, 0xc4 => 0x7beb, 0xc5 => 0x7bd8,
    0xc6 => 0x7bdf, 0xc7 => 0x7cd2, 0xc8 => 0x7cd4, 0xc9 => 0x7cd7,
    0xca => 0x7cd0, 0xcb => 0x7cd1, 0xcc => 0x7e12, 0xcd => 0x7e21,
    0xce => 0x7e17, 0xcf => 0x7e0c, 0xd0 => 0x7e1f, 0xd1 => 0x7e20,
    0xd2 => 0x7e13, 0xd3 => 0x7e0e, 0xd4 => 0x7e1c, 0xd5 => 0x7e15,
    0xd6 => 0x7e1a, 0xd7 => 0x7e22, 0xd8 => 0x7e0b, 0xd9 => 0x7e0f,
    0xda => 0x7e16, 0xdb => 0x7e0d, 0xdc => 0x7e14, 0xdd => 0x7e25,
    0xde => 0x7e24, 0xdf => 0x7f43, 0xe0 => 0x7f7b, 0xe1 => 0x7f7c,
    0xe2 => 0x7f7a, 0xe3 => 0x7fb1, 0xe4 => 0x7fef, 0xe5 => 0x802a,
    0xe6 => 0x8029, 0xe7 => 0x806c, 0xe8 => 0x81b1, 0xe9 => 0x81a6,
    0xea => 0x81ae, 0xeb => 0x81b9, 0xec => 0x81b5, 0xed => 0x81ab,
    0xee => 0x81b0, 0xef => 0x81ac, 0xf0 => 0x81b4, 0xf1 => 0x81b2,
    0xf2 => 0x81b7, 0xf3 => 0x81a7, 0xf4 => 0x81f2, 0xf5 => 0x8255,
    0xf6 => 0x8256, 0xf7 => 0x8257, 0xf8 => 0x8556, 0xf9 => 0x8545,
    0xfa => 0x856b, 0xfb => 0x854d, 0xfc => 0x8553, 0xfd => 0x8561,
    0xfe => 0x8558,
  },
  0xeb => {
    0x40 => 0x8540, 0x41 => 0x8546, 0x42 => 0x8564, 0x43 => 0x8541,
    0x44 => 0x8562, 0x45 => 0x8544, 0x46 => 0x8551, 0x47 => 0x8547,
    0x48 => 0x8563, 0x49 => 0x853e, 0x4a => 0x855b, 0x4b => 0x8571,
    0x4c => 0x854e, 0x4d => 0x856e, 0x4e => 0x8575, 0x4f => 0x8555,
    0x50 => 0x8567, 0x51 => 0x8560, 0x52 => 0x858c, 0x53 => 0x8566,
    0x54 => 0x855d, 0x55 => 0x8554, 0x56 => 0x8565, 0x57 => 0x856c,
    0x58 => 0x8663, 0x59 => 0x8665, 0x5a => 0x8664, 0x5b => 0x879b,
    0x5c => 0x878f, 0x5d => 0x8797, 0x5e => 0x8793, 0x5f => 0x8792,
    0x60 => 0x8788, 0x61 => 0x8781, 0x62 => 0x8796, 0x63 => 0x8798,
    0x64 => 0x8779, 0x65 => 0x8787, 0x66 => 0x87a3, 0x67 => 0x8785,
    0x68 => 0x8790, 0x69 => 0x8791, 0x6a => 0x879d, 0x6b => 0x8784,
    0x6c => 0x8794, 0x6d => 0x879c, 0x6e => 0x879a, 0x6f => 0x8789,
    0x70 => 0x891e, 0x71 => 0x8926, 0x72 => 0x8930, 0x73 => 0x892d,
    0x74 => 0x892e, 0x75 => 0x8927, 0x76 => 0x8931, 0x77 => 0x8922,
    0x78 => 0x8929, 0x79 => 0x8923, 0x7a => 0x892f, 0x7b => 0x892c,
    0x7c => 0x891f, 0x7d => 0x89f1, 0x7e => 0x8ae0, 0xa1 => 0x8ae2,
    0xa2 => 0x8af2, 0xa3 => 0x8af4, 0xa4 => 0x8af5, 0xa5 => 0x8add,
    0xa6 => 0x8b14, 0xa7 => 0x8ae4, 0xa8 => 0x8adf, 0xa9 => 0x8af0,
    0xaa => 0x8ac8, 0xab => 0x8ade, 0xac => 0x8ae1, 0xad => 0x8ae8,
    0xae => 0x8aff, 0xaf => 0x8aef, 0xb0 => 0x8afb, 0xb1 => 0x8c91,
    0xb2 => 0x8c92, 0xb3 => 0x8c90, 0xb4 => 0x8cf5, 0xb5 => 0x8cee,
    0xb6 => 0x8cf1, 0xb7 => 0x8cf0, 0xb8 => 0x8cf3, 0xb9 => 0x8d6c,
    0xba => 0x8d6e, 0xbb => 0x8da5, 0xbc => 0x8da7, 0xbd => 0x8e33,
    0xbe => 0x8e3e, 0xbf => 0x8e38, 0xc0 => 0x8e40, 0xc1 => 0x8e45,
    0xc2 => 0x8e36, 0xc3 => 0x8e3c, 0xc4 => 0x8e3d, 0xc5 => 0x8e41,
    0xc6 => 0x8e30, 0xc7 => 0x8e3f, 0xc8 => 0x8ebd, 0xc9 => 0x8f36,
    0xca => 0x8f2e, 0xcb => 0x8f35, 0xcc => 0x8f32, 0xcd => 0x8f39,
    0xce => 0x8f37, 0xcf => 0x8f34, 0xd0 => 0x9076, 0xd1 => 0x9079,
    0xd2 => 0x907b, 0xd3 => 0x9086, 0xd4 => 0x90fa, 0xd5 => 0x9133,
    0xd6 => 0x9135, 0xd7 => 0x9136, 0xd8 => 0x9193, 0xd9 => 0x9190,
    0xda => 0x9191, 0xdb => 0x918d, 0xdc => 0x918f, 0xdd => 0x9327,
    0xde => 0x931e, 0xdf => 0x9308, 0xe0 => 0x931f, 0xe1 => 0x9306,
    0xe2 => 0x930f, 0xe3 => 0x937a, 0xe4 => 0x9338, 0xe5 => 0x933c,
    0xe6 => 0x931b, 0xe7 => 0x9323, 0xe8 => 0x9312, 0xe9 => 0x9301,
    0xea => 0x9346, 0xeb => 0x932d, 0xec => 0x930e, 0xed => 0x930d,
    0xee => 0x92cb, 0xef => 0x931d, 0xf0 => 0x92fa, 0xf1 => 0x9325,
    0xf2 => 0x9313, 0xf3 => 0x92f9, 0xf4 => 0x92f7, 0xf5 => 0x9334,
    0xf6 => 0x9302, 0xf7 => 0x9324, 0xf8 => 0x92ff, 0xf9 => 0x9329,
    0xfa => 0x9339, 0xfb => 0x9335, 0xfc => 0x932a, 0xfd => 0x9314,
    0xfe => 0x930c,
  },
  0xec => {
    0x40 => 0x930b, 0x41 => 0x92fe, 0x42 => 0x9309, 0x43 => 0x9300,
    0x44 => 0x92fb, 0x45 => 0x9316, 0x46 => 0x95bc, 0x47 => 0x95cd,
    0x48 => 0x95be, 0x49 => 0x95b9, 0x4a => 0x95ba, 0x4b => 0x95b6,
    0x4c => 0x95bf, 0x4d => 0x95b5, 0x4e => 0x95bd, 0x4f => 0x96a9,
    0x50 => 0x96d4, 0x51 => 0x970b, 0x52 => 0x9712, 0x53 => 0x9710,
    0x54 => 0x9799, 0x55 => 0x9797, 0x56 => 0x9794, 0x57 => 0x97f0,
    0x58 => 0x97f8, 0x59 => 0x9835, 0x5a => 0x982f, 0x5b => 0x9832,
    0x5c => 0x9924, 0x5d => 0x991f, 0x5e => 0x9927, 0x5f => 0x9929,
    0x60 => 0x999e, 0x61 => 0x99ee, 0x62 => 0x99ec, 0x63 => 0x99e5,
    0x64 => 0x99e4, 0x65 => 0x99f0, 0x66 => 0x99e3, 0x67 => 0x99ea,
    0x68 => 0x99e9, 0x69 => 0x99e7, 0x6a => 0x9ab9, 0x6b => 0x9abf,
    0x6c => 0x9ab4, 0x6d => 0x9abb, 0x6e => 0x9af6, 0x6f => 0x9afa,
    0x70 => 0x9af9, 0x71 => 0x9af7, 0x72 => 0x9b33, 0x73 => 0x9b80,
    0x74 => 0x9b85, 0x75 => 0x9b87, 0x76 => 0x9b7c, 0x77 => 0x9b7e,
    0x78 => 0x9b7b, 0x79 => 0x9b82, 0x7a => 0x9b93, 0x7b => 0x9b92,
    0x7c => 0x9b90, 0x7d => 0x9b7a, 0x7e => 0x9b95, 0xa1 => 0x9b7d,
    0xa2 => 0x9b88, 0xa3 => 0x9d25, 0xa4 => 0x9d17, 0xa5 => 0x9d20,
    0xa6 => 0x9d1e, 0xa7 => 0x9d14, 0xa8 => 0x9d29, 0xa9 => 0x9d1d,
    0xaa => 0x9d18, 0xab => 0x9d22, 0xac => 0x9d10, 0xad => 0x9d19,
    0xae => 0x9d1f, 0xaf => 0x9e88, 0xb0 => 0x9e86, 0xb1 => 0x9e87,
    0xb2 => 0x9eae, 0xb3 => 0x9ead, 0xb4 => 0x9ed5, 0xb5 => 0x9ed6,
    0xb6 => 0x9efa, 0xb7 => 0x9f12, 0xb8 => 0x9f3d, 0xb9 => 0x5126,
    0xba => 0x5125, 0xbb => 0x5122, 0xbc => 0x5124, 0xbd => 0x5120,
    0xbe => 0x5129, 0xbf => 0x52f4, 0xc0 => 0x5693, 0xc1 => 0x568c,
    0xc2 => 0x568d, 0xc3 => 0x5686, 0xc4 => 0x5684, 0xc5 => 0x5683,
    0xc6 => 0x567e, 0xc7 => 0x5682, 0xc8 => 0x567f, 0xc9 => 0x5681,
    0xca => 0x58d6, 0xcb => 0x58d4, 0xcc => 0x58cf, 0xcd => 0x58d2,
    0xce => 0x5b2d, 0xcf => 0x5b25, 0xd0 => 0x5b32, 0xd1 => 0x5b23,
    0xd2 => 0x5b2c, 0xd3 => 0x5b27, 0xd4 => 0x5b26, 0xd5 => 0x5b2f,
    0xd6 => 0x5b2e, 0xd7 => 0x5b7b, 0xd8 => 0x5bf1, 0xd9 => 0x5bf2,
    0xda => 0x5db7, 0xdb => 0x5e6c, 0xdc => 0x5e6a, 0xdd => 0x5fbe,
    0xde => 0x5fbb, 0xdf => 0x61c3, 0xe0 => 0x61b5, 0xe1 => 0x61bc,
    0xe2 => 0x61e7, 0xe3 => 0x61e0, 0xe4 => 0x61e5, 0xe5 => 0x61e4,
    0xe6 => 0x61e8, 0xe7 => 0x61de, 0xe8 => 0x64ef, 0xe9 => 0x64e9,
    0xea => 0x64e3, 0xeb => 0x64eb, 0xec => 0x64e4, 0xed => 0x64e8,
    0xee => 0x6581, 0xef => 0x6580, 0xf0 => 0x65b6, 0xf1 => 0x65da,
    0xf2 => 0x66d2, 0xf3 => 0x6a8d, 0xf4 => 0x6a96, 0xf5 => 0x6a81,
    0xf6 => 0x6aa5, 0xf7 => 0x6a89, 0xf8 => 0x6a9f, 0xf9 => 0x6a9b,
    0xfa => 0x6aa1, 0xfb => 0x6a9e, 0xfc => 0x6a87, 0xfd => 0x6a93,
    0xfe => 0x6a8e,
  },
  0xed => {
    0x40 => 0x6a95, 0x41 => 0x6a83, 0x42 => 0x6aa8, 0x43 => 0x6aa4,
    0x44 => 0x6a91, 0x45 => 0x6a7f, 0x46 => 0x6aa6, 0x47 => 0x6a9a,
    0x48 => 0x6a85, 0x49 => 0x6a8c, 0x4a => 0x6a92, 0x4b => 0x6b5b,
    0x4c => 0x6bad, 0x4d => 0x6c09, 0x4e => 0x6fcc, 0x4f => 0x6fa9,
    0x50 => 0x6ff4, 0x51 => 0x6fd4, 0x52 => 0x6fe3, 0x53 => 0x6fdc,
    0x54 => 0x6fed, 0x55 => 0x6fe7, 0x56 => 0x6fe6, 0x57 => 0x6fde,
    0x58 => 0x6ff2, 0x59 => 0x6fdd, 0x5a => 0x6fe2, 0x5b => 0x6fe8,
    0x5c => 0x71e1, 0x5d => 0x71f1, 0x5e => 0x71e8, 0x5f => 0x71f2,
    0x60 => 0x71e4, 0x61 => 0x71f0, 0x62 => 0x71e2, 0x63 => 0x7373,
    0x64 => 0x736e, 0x65 => 0x736f, 0x66 => 0x7497, 0x67 => 0x74b2,
    0x68 => 0x74ab, 0x69 => 0x7490, 0x6a => 0x74aa, 0x6b => 0x74ad,
    0x6c => 0x74b1, 0x6d => 0x74a5, 0x6e => 0x74af, 0x6f => 0x7510,
    0x70 => 0x7511, 0x71 => 0x7512, 0x72 => 0x750f, 0x73 => 0x7584,
    0x74 => 0x7643, 0x75 => 0x7648, 0x76 => 0x7649, 0x77 => 0x7647,
    0x78 => 0x76a4, 0x79 => 0x76e9, 0x7a => 0x77b5, 0x7b => 0x77ab,
    0x7c => 0x77b2, 0x7d => 0x77b7, 0x7e => 0x77b6, 0xa1 => 0x77b4,
    0xa2 => 0x77b1, 0xa3 => 0x77a8, 0xa4 => 0x77f0, 0xa5 => 0x78f3,
    0xa6 => 0x78fd, 0xa7 => 0x7902, 0xa8 => 0x78fb, 0xa9 => 0x78fc,
    0xaa => 0x78f2, 0xab => 0x7905, 0xac => 0x78f9, 0xad => 0x78fe,
    0xae => 0x7904, 0xaf => 0x79ab, 0xb0 => 0x79a8, 0xb1 => 0x7a5c,
    0xb2 => 0x7a5b, 0xb3 => 0x7a56, 0xb4 => 0x7a58, 0xb5 => 0x7a54,
    0xb6 => 0x7a5a, 0xb7 => 0x7abe, 0xb8 => 0x7ac0, 0xb9 => 0x7ac1,
    0xba => 0x7c05, 0xbb => 0x7c0f, 0xbc => 0x7bf2, 0xbd => 0x7c00,
    0xbe => 0x7bff, 0xbf => 0x7bfb, 0xc0 => 0x7c0e, 0xc1 => 0x7bf4,
    0xc2 => 0x7c0b, 0xc3 => 0x7bf3, 0xc4 => 0x7c02, 0xc5 => 0x7c09,
    0xc6 => 0x7c03, 0xc7 => 0x7c01, 0xc8 => 0x7bf8, 0xc9 => 0x7bfd,
    0xca => 0x7c06, 0xcb => 0x7bf0, 0xcc => 0x7bf1, 0xcd => 0x7c10,
    0xce => 0x7c0a, 0xcf => 0x7ce8, 0xd0 => 0x7e2d, 0xd1 => 0x7e3c,
    0xd2 => 0x7e42, 0xd3 => 0x7e33, 0xd4 => 0x9848, 0xd5 => 0x7e38,
    0xd6 => 0x7e2a, 0xd7 => 0x7e49, 0xd8 => 0x7e40, 0xd9 => 0x7e47,
    0xda => 0x7e29, 0xdb => 0x7e4c, 0xdc => 0x7e30, 0xdd => 0x7e3b,
    0xde => 0x7e36, 0xdf => 0x7e44, 0xe0 => 0x7e3a, 0xe1 => 0x7f45,
    0xe2 => 0x7f7f, 0xe3 => 0x7f7e, 0xe4 => 0x7f7d, 0xe5 => 0x7ff4,
    0xe6 => 0x7ff2, 0xe7 => 0x802c, 0xe8 => 0x81bb, 0xe9 => 0x81c4,
    0xea => 0x81cc, 0xeb => 0x81ca, 0xec => 0x81c5, 0xed => 0x81c7,
    0xee => 0x81bc, 0xef => 0x81e9, 0xf0 => 0x825b, 0xf1 => 0x825a,
    0xf2 => 0x825c, 0xf3 => 0x8583, 0xf4 => 0x8580, 0xf5 => 0x858f,
    0xf6 => 0x85a7, 0xf7 => 0x8595, 0xf8 => 0x85a0, 0xf9 => 0x858b,
    0xfa => 0x85a3, 0xfb => 0x857b, 0xfc => 0x85a4, 0xfd => 0x859a,
    0xfe => 0x859e,
  },
  0xee => {
    0x40 => 0x8577, 0x41 => 0x857c, 0x42 => 0x8589, 0x43 => 0x85a1,
    0x44 => 0x857a, 0x45 => 0x8578, 0x46 => 0x8557, 0x47 => 0x858e,
    0x48 => 0x8596, 0x49 => 0x8586, 0x4a => 0x858d, 0x4b => 0x8599,
    0x4c => 0x859d, 0x4d => 0x8581, 0x4e => 0x85a2, 0x4f => 0x8582,
    0x50 => 0x8588, 0x51 => 0x8585, 0x52 => 0x8579, 0x53 => 0x8576,
    0x54 => 0x8598, 0x55 => 0x8590, 0x56 => 0x859f, 0x57 => 0x8668,
    0x58 => 0x87be, 0x59 => 0x87aa, 0x5a => 0x87ad, 0x5b => 0x87c5,
    0x5c => 0x87b0, 0x5d => 0x87ac, 0x5e => 0x87b9, 0x5f => 0x87b5,
    0x60 => 0x87bc, 0x61 => 0x87ae, 0x62 => 0x87c9, 0x63 => 0x87c3,
    0x64 => 0x87c2, 0x65 => 0x87cc, 0x66 => 0x87b7, 0x67 => 0x87af,
    0x68 => 0x87c4, 0x69 => 0x87ca, 0x6a => 0x87b4, 0x6b => 0x87b6,
    0x6c => 0x87bf, 0x6d => 0x87b8, 0x6e => 0x87bd, 0x6f => 0x87de,
    0x70 => 0x87b2, 0x71 => 0x8935, 0x72 => 0x8933, 0x73 => 0x893c,
    0x74 => 0x893e, 0x75 => 0x8941, 0x76 => 0x8952, 0x77 => 0x8937,
    0x78 => 0x8942, 0x79 => 0x89ad, 0x7a => 0x89af, 0x7b => 0x89ae,
    0x7c => 0x89f2, 0x7d => 0x89f3, 0x7e => 0x8b1e, 0xa1 => 0x8b18,
    0xa2 => 0x8b16, 0xa3 => 0x8b11, 0xa4 => 0x8b05, 0xa5 => 0x8b0b,
    0xa6 => 0x8b22, 0xa7 => 0x8b0f, 0xa8 => 0x8b12, 0xa9 => 0x8b15,
    0xaa => 0x8b07, 0xab => 0x8b0d, 0xac => 0x8b08, 0xad => 0x8b06,
    0xae => 0x8b1c, 0xaf => 0x8b13, 0xb0 => 0x8b1a, 0xb1 => 0x8c4f,
    0xb2 => 0x8c70, 0xb3 => 0x8c72, 0xb4 => 0x8c71, 0xb5 => 0x8c6f,
    0xb6 => 0x8c95, 0xb7 => 0x8c94, 0xb8 => 0x8cf9, 0xb9 => 0x8d6f,
    0xba => 0x8e4e, 0xbb => 0x8e4d, 0xbc => 0x8e53, 0xbd => 0x8e50,
    0xbe => 0x8e4c, 0xbf => 0x8e47, 0xc0 => 0x8f43, 0xc1 => 0x8f40,
    0xc2 => 0x9085, 0xc3 => 0x907e, 0xc4 => 0x9138, 0xc5 => 0x919a,
    0xc6 => 0x91a2, 0xc7 => 0x919b, 0xc8 => 0x9199, 0xc9 => 0x919f,
    0xca => 0x91a1, 0xcb => 0x919d, 0xcc => 0x91a0, 0xcd => 0x93a1,
    0xce => 0x9383, 0xcf => 0x93af, 0xd0 => 0x9364, 0xd1 => 0x9356,
    0xd2 => 0x9347, 0xd3 => 0x937c, 0xd4 => 0x9358, 0xd5 => 0x935c,
    0xd6 => 0x9376, 0xd7 => 0x9349, 0xd8 => 0x9350, 0xd9 => 0x9351,
    0xda => 0x9360, 0xdb => 0x936d, 0xdc => 0x938f, 0xdd => 0x934c,
    0xde => 0x936a, 0xdf => 0x9379, 0xe0 => 0x9357, 0xe1 => 0x9355,
    0xe2 => 0x9352, 0xe3 => 0x934f, 0xe4 => 0x9371, 0xe5 => 0x9377,
    0xe6 => 0x937b, 0xe7 => 0x9361, 0xe8 => 0x935e, 0xe9 => 0x9363,
    0xea => 0x9367, 0xeb => 0x9380, 0xec => 0x934e, 0xed => 0x9359,
    0xee => 0x95c7, 0xef => 0x95c0, 0xf0 => 0x95c9, 0xf1 => 0x95c3,
    0xf2 => 0x95c5, 0xf3 => 0x95b7, 0xf4 => 0x96ae, 0xf5 => 0x96b0,
    0xf6 => 0x96ac, 0xf7 => 0x9720, 0xf8 => 0x971f, 0xf9 => 0x9718,
    0xfa => 0x971d, 0xfb => 0x9719, 0xfc => 0x979a, 0xfd => 0x97a1,
    0xfe => 0x979c,
  },
  0xef => {
    0x40 => 0x979e, 0x41 => 0x979d, 0x42 => 0x97d5, 0x43 => 0x97d4,
    0x44 => 0x97f1, 0x45 => 0x9841, 0x46 => 0x9844, 0x47 => 0x984a,
    0x48 => 0x9849, 0x49 => 0x9845, 0x4a => 0x9843, 0x4b => 0x9925,
    0x4c => 0x992b, 0x4d => 0x992c, 0x4e => 0x992a, 0x4f => 0x9933,
    0x50 => 0x9932, 0x51 => 0x992f, 0x52 => 0x992d, 0x53 => 0x9931,
    0x54 => 0x9930, 0x55 => 0x9998, 0x56 => 0x99a3, 0x57 => 0x99a1,
    0x58 => 0x9a02, 0x59 => 0x99fa, 0x5a => 0x99f4, 0x5b => 0x99f7,
    0x5c => 0x99f9, 0x5d => 0x99f8, 0x5e => 0x99f6, 0x5f => 0x99fb,
    0x60 => 0x99fd, 0x61 => 0x99fe, 0x62 => 0x99fc, 0x63 => 0x9a03,
    0x64 => 0x9abe, 0x65 => 0x9afe, 0x66 => 0x9afd, 0x67 => 0x9b01,
    0x68 => 0x9afc, 0x69 => 0x9b48, 0x6a => 0x9b9a, 0x6b => 0x9ba8,
    0x6c => 0x9b9e, 0x6d => 0x9b9b, 0x6e => 0x9ba6, 0x6f => 0x9ba1,
    0x70 => 0x9ba5, 0x71 => 0x9ba4, 0x72 => 0x9b86, 0x73 => 0x9ba2,
    0x74 => 0x9ba0, 0x75 => 0x9baf, 0x76 => 0x9d33, 0x77 => 0x9d41,
    0x78 => 0x9d67, 0x79 => 0x9d36, 0x7a => 0x9d2e, 0x7b => 0x9d2f,
    0x7c => 0x9d31, 0x7d => 0x9d38, 0x7e => 0x9d30, 0xa1 => 0x9d45,
    0xa2 => 0x9d42, 0xa3 => 0x9d43, 0xa4 => 0x9d3e, 0xa5 => 0x9d37,
    0xa6 => 0x9d40, 0xa7 => 0x9d3d, 0xa8 => 0x7ff5, 0xa9 => 0x9d2d,
    0xaa => 0x9e8a, 0xab => 0x9e89, 0xac => 0x9e8d, 0xad => 0x9eb0,
    0xae => 0x9ec8, 0xaf => 0x9eda, 0xb0 => 0x9efb, 0xb1 => 0x9eff,
    0xb2 => 0x9f24, 0xb3 => 0x9f23, 0xb4 => 0x9f22, 0xb5 => 0x9f54,
    0xb6 => 0x9fa0, 0xb7 => 0x5131, 0xb8 => 0x512d, 0xb9 => 0x512e,
    0xba => 0x5698, 0xbb => 0x569c, 0xbc => 0x5697, 0xbd => 0x569a,
    0xbe => 0x569d, 0xbf => 0x5699, 0xc0 => 0x5970, 0xc1 => 0x5b3c,
    0xc2 => 0x5c69, 0xc3 => 0x5c6a, 0xc4 => 0x5dc0, 0xc5 => 0x5e6d,
    0xc6 => 0x5e6e, 0xc7 => 0x61d8, 0xc8 => 0x61df, 0xc9 => 0x61ed,
    0xca => 0x61ee, 0xcb => 0x61f1, 0xcc => 0x61ea, 0xcd => 0x61f0,
    0xce => 0x61eb, 0xcf => 0x61d6, 0xd0 => 0x61e9, 0xd1 => 0x64ff,
    0xd2 => 0x6504, 0xd3 => 0x64fd, 0xd4 => 0x64f8, 0xd5 => 0x6501,
    0xd6 => 0x6503, 0xd7 => 0x64fc, 0xd8 => 0x6594, 0xd9 => 0x65db,
    0xda => 0x66da, 0xdb => 0x66db, 0xdc => 0x66d8, 0xdd => 0x6ac5,
    0xde => 0x6ab9, 0xdf => 0x6abd, 0xe0 => 0x6ae1, 0xe1 => 0x6ac6,
    0xe2 => 0x6aba, 0xe3 => 0x6ab6, 0xe4 => 0x6ab7, 0xe5 => 0x6ac7,
    0xe6 => 0x6ab4, 0xe7 => 0x6aad, 0xe8 => 0x6b5e, 0xe9 => 0x6bc9,
    0xea => 0x6c0b, 0xeb => 0x7007, 0xec => 0x700c, 0xed => 0x700d,
    0xee => 0x7001, 0xef => 0x7005, 0xf0 => 0x7014, 0xf1 => 0x700e,
    0xf2 => 0x6fff, 0xf3 => 0x7000, 0xf4 => 0x6ffb, 0xf5 => 0x7026,
    0xf6 => 0x6ffc, 0xf7 => 0x6ff7, 0xf8 => 0x700a, 0xf9 => 0x7201,
    0xfa => 0x71ff, 0xfb => 0x71f9, 0xfc => 0x7203, 0xfd => 0x71fd,
    0xfe => 0x7376,
  },
  0xf0 => {
    0x40 => 0x74b8, 0x41 => 0x74c0, 0x42 => 0x74b5, 0x43 => 0x74c1,
    0x44 => 0x74be, 0x45 => 0x74b6, 0x46 => 0x74bb, 0x47 => 0x74c2,
    0x48 => 0x7514, 0x49 => 0x7513, 0x4a => 0x765c, 0x4b => 0x7664,
    0x4c => 0x7659, 0x4d => 0x7650, 0x4e => 0x7653, 0x4f => 0x7657,
    0x50 => 0x765a, 0x51 => 0x76a6, 0x52 => 0x76bd, 0x53 => 0x76ec,
    0x54 => 0x77c2, 0x55 => 0x77ba, 0x56 => 0x78ff, 0x57 => 0x790c,
    0x58 => 0x7913, 0x59 => 0x7914, 0x5a => 0x7909, 0x5b => 0x7910,
    0x5c => 0x7912, 0x5d => 0x7911, 0x5e => 0x79ad, 0x5f => 0x79ac,
    0x60 => 0x7a5f, 0x61 => 0x7c1c, 0x62 => 0x7c29, 0x63 => 0x7c19,
    0x64 => 0x7c20, 0x65 => 0x7c1f, 0x66 => 0x7c2d, 0x67 => 0x7c1d,
    0x68 => 0x7c26, 0x69 => 0x7c28, 0x6a => 0x7c22, 0x6b => 0x7c25,
    0x6c => 0x7c30, 0x6d => 0x7e5c, 0x6e => 0x7e50, 0x6f => 0x7e56,
    0x70 => 0x7e63, 0x71 => 0x7e58, 0x72 => 0x7e62, 0x73 => 0x7e5f,
    0x74 => 0x7e51, 0x75 => 0x7e60, 0x76 => 0x7e57, 0x77 => 0x7e53,
    0x78 => 0x7fb5, 0x79 => 0x7fb3, 0x7a => 0x7ff7, 0x7b => 0x7ff8,
    0x7c => 0x8075, 0x7d => 0x81d1, 0x7e => 0x81d2, 0xa1 => 0x81d0,
    0xa2 => 0x825f, 0xa3 => 0x825e, 0xa4 => 0x85b4, 0xa5 => 0x85c6,
    0xa6 => 0x85c0, 0xa7 => 0x85c3, 0xa8 => 0x85c2, 0xa9 => 0x85b3,
    0xaa => 0x85b5, 0xab => 0x85bd, 0xac => 0x85c7, 0xad => 0x85c4,
    0xae => 0x85bf, 0xaf => 0x85cb, 0xb0 => 0x85ce, 0xb1 => 0x85c8,
    0xb2 => 0x85c5, 0xb3 => 0x85b1, 0xb4 => 0x85b6, 0xb5 => 0x85d2,
    0xb6 => 0x8624, 0xb7 => 0x85b8, 0xb8 => 0x85b7, 0xb9 => 0x85be,
    0xba => 0x8669, 0xbb => 0x87e7, 0xbc => 0x87e6, 0xbd => 0x87e2,
    0xbe => 0x87db, 0xbf => 0x87eb, 0xc0 => 0x87ea, 0xc1 => 0x87e5,
    0xc2 => 0x87df, 0xc3 => 0x87f3, 0xc4 => 0x87e4, 0xc5 => 0x87d4,
    0xc6 => 0x87dc, 0xc7 => 0x87d3, 0xc8 => 0x87ed, 0xc9 => 0x87d8,
    0xca => 0x87e3, 0xcb => 0x87a4, 0xcc => 0x87d7, 0xcd => 0x87d9,
    0xce => 0x8801, 0xcf => 0x87f4, 0xd0 => 0x87e8, 0xd1 => 0x87dd,
    0xd2 => 0x8953, 0xd3 => 0x894b, 0xd4 => 0x894f, 0xd5 => 0x894c,
    0xd6 => 0x8946, 0xd7 => 0x8950, 0xd8 => 0x8951, 0xd9 => 0x8949,
    0xda => 0x8b2a, 0xdb => 0x8b27, 0xdc => 0x8b23, 0xdd => 0x8b33,
    0xde => 0x8b30, 0xdf => 0x8b35, 0xe0 => 0x8b47, 0xe1 => 0x8b2f,
    0xe2 => 0x8b3c, 0xe3 => 0x8b3e, 0xe4 => 0x8b31, 0xe5 => 0x8b25,
    0xe6 => 0x8b37, 0xe7 => 0x8b26, 0xe8 => 0x8b36, 0xe9 => 0x8b2e,
    0xea => 0x8b24, 0xeb => 0x8b3b, 0xec => 0x8b3d, 0xed => 0x8b3a,
    0xee => 0x8c42, 0xef => 0x8c75, 0xf0 => 0x8c99, 0xf1 => 0x8c98,
    0xf2 => 0x8c97, 0xf3 => 0x8cfe, 0xf4 => 0x8d04, 0xf5 => 0x8d02,
    0xf6 => 0x8d00, 0xf7 => 0x8e5c, 0xf8 => 0x8e62, 0xf9 => 0x8e60,
    0xfa => 0x8e57, 0xfb => 0x8e56, 0xfc => 0x8e5e, 0xfd => 0x8e65,
    0xfe => 0x8e67,
  },
  0xf1 => {
    0x40 => 0x8e5b, 0x41 => 0x8e5a, 0x42 => 0x8e61, 0x43 => 0x8e5d,
    0x44 => 0x8e69, 0x45 => 0x8e54, 0x46 => 0x8f46, 0x47 => 0x8f47,
    0x48 => 0x8f48, 0x49 => 0x8f4b, 0x4a => 0x9128, 0x4b => 0x913a,
    0x4c => 0x913b, 0x4d => 0x913e, 0x4e => 0x91a8, 0x4f => 0x91a5,
    0x50 => 0x91a7, 0x51 => 0x91af, 0x52 => 0x91aa, 0x53 => 0x93b5,
    0x54 => 0x938c, 0x55 => 0x9392, 0x56 => 0x93b7, 0x57 => 0x939b,
    0x58 => 0x939d, 0x59 => 0x9389, 0x5a => 0x93a7, 0x5b => 0x938e,
    0x5c => 0x93aa, 0x5d => 0x939e, 0x5e => 0x93a6, 0x5f => 0x9395,
    0x60 => 0x9388, 0x61 => 0x9399, 0x62 => 0x939f, 0x63 => 0x938d,
    0x64 => 0x93b1, 0x65 => 0x9391, 0x66 => 0x93b2, 0x67 => 0x93a4,
    0x68 => 0x93a8, 0x69 => 0x93b4, 0x6a => 0x93a3, 0x6b => 0x93a5,
    0x6c => 0x95d2, 0x6d => 0x95d3, 0x6e => 0x95d1, 0x6f => 0x96b3,
    0x70 => 0x96d7, 0x71 => 0x96da, 0x72 => 0x5dc2, 0x73 => 0x96df,
    0x74 => 0x96d8, 0x75 => 0x96dd, 0x76 => 0x9723, 0x77 => 0x9722,
    0x78 => 0x9725, 0x79 => 0x97ac, 0x7a => 0x97ae, 0x7b => 0x97a8,
    0x7c => 0x97ab, 0x7d => 0x97a4, 0x7e => 0x97aa, 0xa1 => 0x97a2,
    0xa2 => 0x97a5, 0xa3 => 0x97d7, 0xa4 => 0x97d9, 0xa5 => 0x97d6,
    0xa6 => 0x97d8, 0xa7 => 0x97fa, 0xa8 => 0x9850, 0xa9 => 0x9851,
    0xaa => 0x9852, 0xab => 0x98b8, 0xac => 0x9941, 0xad => 0x993c,
    0xae => 0x993a, 0xaf => 0x9a0f, 0xb0 => 0x9a0b, 0xb1 => 0x9a09,
    0xb2 => 0x9a0d, 0xb3 => 0x9a04, 0xb4 => 0x9a11, 0xb5 => 0x9a0a,
    0xb6 => 0x9a05, 0xb7 => 0x9a07, 0xb8 => 0x9a06, 0xb9 => 0x9ac0,
    0xba => 0x9adc, 0xbb => 0x9b08, 0xbc => 0x9b04, 0xbd => 0x9b05,
    0xbe => 0x9b29, 0xbf => 0x9b35, 0xc0 => 0x9b4a, 0xc1 => 0x9b4c,
    0xc2 => 0x9b4b, 0xc3 => 0x9bc7, 0xc4 => 0x9bc6, 0xc5 => 0x9bc3,
    0xc6 => 0x9bbf, 0xc7 => 0x9bc1, 0xc8 => 0x9bb5, 0xc9 => 0x9bb8,
    0xca => 0x9bd3, 0xcb => 0x9bb6, 0xcc => 0x9bc4, 0xcd => 0x9bb9,
    0xce => 0x9bbd, 0xcf => 0x9d5c, 0xd0 => 0x9d53, 0xd1 => 0x9d4f,
    0xd2 => 0x9d4a, 0xd3 => 0x9d5b, 0xd4 => 0x9d4b, 0xd5 => 0x9d59,
    0xd6 => 0x9d56, 0xd7 => 0x9d4c, 0xd8 => 0x9d57, 0xd9 => 0x9d52,
    0xda => 0x9d54, 0xdb => 0x9d5f, 0xdc => 0x9d58, 0xdd => 0x9d5a,
    0xde => 0x9e8e, 0xdf => 0x9e8c, 0xe0 => 0x9edf, 0xe1 => 0x9f01,
    0xe2 => 0x9f00, 0xe3 => 0x9f16, 0xe4 => 0x9f25, 0xe5 => 0x9f2b,
    0xe6 => 0x9f2a, 0xe7 => 0x9f29, 0xe8 => 0x9f28, 0xe9 => 0x9f4c,
    0xea => 0x9f55, 0xeb => 0x5134, 0xec => 0x5135, 0xed => 0x5296,
    0xee => 0x52f7, 0xef => 0x53b4, 0xf0 => 0x56ab, 0xf1 => 0x56ad,
    0xf2 => 0x56a6, 0xf3 => 0x56a7, 0xf4 => 0x56aa, 0xf5 => 0x56ac,
    0xf6 => 0x58da, 0xf7 => 0x58dd, 0xf8 => 0x58db, 0xf9 => 0x5912,
    0xfa => 0x5b3d, 0xfb => 0x5b3e, 0xfc => 0x5b3f, 0xfd => 0x5dc3,
    0xfe => 0x5e70,
  },
  0xf2 => {
    0x40 => 0x5fbf, 0x41 => 0x61fb, 0x42 => 0x6507, 0x43 => 0x6510,
    0x44 => 0x650d, 0x45 => 0x6509, 0x46 => 0x650c, 0x47 => 0x650e,
    0x48 => 0x6584, 0x49 => 0x65de, 0x4a => 0x65dd, 0x4b => 0x66de,
    0x4c => 0x6ae7, 0x4d => 0x6ae0, 0x4e => 0x6acc, 0x4f => 0x6ad1,
    0x50 => 0x6ad9, 0x51 => 0x6acb, 0x52 => 0x6adf, 0x53 => 0x6adc,
    0x54 => 0x6ad0, 0x55 => 0x6aeb, 0x56 => 0x6acf, 0x57 => 0x6acd,
    0x58 => 0x6ade, 0x59 => 0x6b60, 0x5a => 0x6bb0, 0x5b => 0x6c0c,
    0x5c => 0x7019, 0x5d => 0x7027, 0x5e => 0x7020, 0x5f => 0x7016,
    0x60 => 0x702b, 0x61 => 0x7021, 0x62 => 0x7022, 0x63 => 0x7023,
    0x64 => 0x7029, 0x65 => 0x7017, 0x66 => 0x7024, 0x67 => 0x701c,
    0x68 => 0x702a, 0x69 => 0x720c, 0x6a => 0x720a, 0x6b => 0x7207,
    0x6c => 0x7202, 0x6d => 0x7205, 0x6e => 0x72a5, 0x6f => 0x72a6,
    0x70 => 0x72a4, 0x71 => 0x72a3, 0x72 => 0x72a1, 0x73 => 0x74cb,
    0x74 => 0x74c5, 0x75 => 0x74b7, 0x76 => 0x74c3, 0x77 => 0x7516,
    0x78 => 0x7660, 0x79 => 0x77c9, 0x7a => 0x77ca, 0x7b => 0x77c4,
    0x7c => 0x77f1, 0x7d => 0x791d, 0x7e => 0x791b, 0xa1 => 0x7921,
    0xa2 => 0x791c, 0xa3 => 0x7917, 0xa4 => 0x791e, 0xa5 => 0x79b0,
    0xa6 => 0x7a67, 0xa7 => 0x7a68, 0xa8 => 0x7c33, 0xa9 => 0x7c3c,
    0xaa => 0x7c39, 0xab => 0x7c2c, 0xac => 0x7c3b, 0xad => 0x7cec,
    0xae => 0x7cea, 0xaf => 0x7e76, 0xb0 => 0x7e75, 0xb1 => 0x7e78,
    0xb2 => 0x7e70, 0xb3 => 0x7e77, 0xb4 => 0x7e6f, 0xb5 => 0x7e7a,
    0xb6 => 0x7e72, 0xb7 => 0x7e74, 0xb8 => 0x7e68, 0xb9 => 0x7f4b,
    0xba => 0x7f4a, 0xbb => 0x7f83, 0xbc => 0x7f86, 0xbd => 0x7fb7,
    0xbe => 0x7ffd, 0xbf => 0x7ffe, 0xc0 => 0x8078, 0xc1 => 0x81d7,
    0xc2 => 0x81d5, 0xc3 => 0x8264, 0xc4 => 0x8261, 0xc5 => 0x8263,
    0xc6 => 0x85eb, 0xc7 => 0x85f1, 0xc8 => 0x85ed, 0xc9 => 0x85d9,
    0xca => 0x85e1, 0xcb => 0x85e8, 0xcc => 0x85da, 0xcd => 0x85d7,
    0xce => 0x85ec, 0xcf => 0x85f2, 0xd0 => 0x85f8, 0xd1 => 0x85d8,
    0xd2 => 0x85df, 0xd3 => 0x85e3, 0xd4 => 0x85dc, 0xd5 => 0x85d1,
    0xd6 => 0x85f0, 0xd7 => 0x85e6, 0xd8 => 0x85ef, 0xd9 => 0x85de,
    0xda => 0x85e2, 0xdb => 0x8800, 0xdc => 0x87fa, 0xdd => 0x8803,
    0xde => 0x87f6, 0xdf => 0x87f7, 0xe0 => 0x8809, 0xe1 => 0x880c,
    0xe2 => 0x880b, 0xe3 => 0x8806, 0xe4 => 0x87fc, 0xe5 => 0x8808,
    0xe6 => 0x87ff, 0xe7 => 0x880a, 0xe8 => 0x8802, 0xe9 => 0x8962,
    0xea => 0x895a, 0xeb => 0x895b, 0xec => 0x8957, 0xed => 0x8961,
    0xee => 0x895c, 0xef => 0x8958, 0xf0 => 0x895d, 0xf1 => 0x8959,
    0xf2 => 0x8988, 0xf3 => 0x89b7, 0xf4 => 0x89b6, 0xf5 => 0x89f6,
    0xf6 => 0x8b50, 0xf7 => 0x8b48, 0xf8 => 0x8b4a, 0xf9 => 0x8b40,
    0xfa => 0x8b53, 0xfb => 0x8b56, 0xfc => 0x8b54, 0xfd => 0x8b4b,
    0xfe => 0x8b55,
  },
  0xf3 => {
    0x40 => 0x8b51, 0x41 => 0x8b42, 0x42 => 0x8b52, 0x43 => 0x8b57,
    0x44 => 0x8c43, 0x45 => 0x8c77, 0x46 => 0x8c76, 0x47 => 0x8c9a,
    0x48 => 0x8d06, 0x49 => 0x8d07, 0x4a => 0x8d09, 0x4b => 0x8dac,
    0x4c => 0x8daa, 0x4d => 0x8dad, 0x4e => 0x8dab, 0x4f => 0x8e6d,
    0x50 => 0x8e78, 0x51 => 0x8e73, 0x52 => 0x8e6a, 0x53 => 0x8e6f,
    0x54 => 0x8e7b, 0x55 => 0x8ec2, 0x56 => 0x8f52, 0x57 => 0x8f51,
    0x58 => 0x8f4f, 0x59 => 0x8f50, 0x5a => 0x8f53, 0x5b => 0x8fb4,
    0x5c => 0x9140, 0x5d => 0x913f, 0x5e => 0x91b0, 0x5f => 0x91ad,
    0x60 => 0x93de, 0x61 => 0x93c7, 0x62 => 0x93cf, 0x63 => 0x93c2,
    0x64 => 0x93da, 0x65 => 0x93d0, 0x66 => 0x93f9, 0x67 => 0x93ec,
    0x68 => 0x93cc, 0x69 => 0x93d9, 0x6a => 0x93a9, 0x6b => 0x93e6,
    0x6c => 0x93ca, 0x6d => 0x93d4, 0x6e => 0x93ee, 0x6f => 0x93e3,
    0x70 => 0x93d5, 0x71 => 0x93c4, 0x72 => 0x93ce, 0x73 => 0x93c0,
    0x74 => 0x93d2, 0x75 => 0x93e7, 0x76 => 0x957d, 0x77 => 0x95da,
    0x78 => 0x95db, 0x79 => 0x96e1, 0x7a => 0x9729, 0x7b => 0x972b,
    0x7c => 0x972c, 0x7d => 0x9728, 0x7e => 0x9726, 0xa1 => 0x97b3,
    0xa2 => 0x97b7, 0xa3 => 0x97b6, 0xa4 => 0x97dd, 0xa5 => 0x97de,
    0xa6 => 0x97df, 0xa7 => 0x985c, 0xa8 => 0x9859, 0xa9 => 0x985d,
    0xaa => 0x9857, 0xab => 0x98bf, 0xac => 0x98bd, 0xad => 0x98bb,
    0xae => 0x98be, 0xaf => 0x9948, 0xb0 => 0x9947, 0xb1 => 0x9943,
    0xb2 => 0x99a6, 0xb3 => 0x99a7, 0xb4 => 0x9a1a, 0xb5 => 0x9a15,
    0xb6 => 0x9a25, 0xb7 => 0x9a1d, 0xb8 => 0x9a24, 0xb9 => 0x9a1b,
    0xba => 0x9a22, 0xbb => 0x9a20, 0xbc => 0x9a27, 0xbd => 0x9a23,
    0xbe => 0x9a1e, 0xbf => 0x9a1c, 0xc0 => 0x9a14, 0xc1 => 0x9ac2,
    0xc2 => 0x9b0b, 0xc3 => 0x9b0a, 0xc4 => 0x9b0e, 0xc5 => 0x9b0c,
    0xc6 => 0x9b37, 0xc7 => 0x9bea, 0xc8 => 0x9beb, 0xc9 => 0x9be0,
    0xca => 0x9bde, 0xcb => 0x9be4, 0xcc => 0x9be6, 0xcd => 0x9be2,
    0xce => 0x9bf0, 0xcf => 0x9bd4, 0xd0 => 0x9bd7, 0xd1 => 0x9bec,
    0xd2 => 0x9bdc, 0xd3 => 0x9bd9, 0xd4 => 0x9be5, 0xd5 => 0x9bd5,
    0xd6 => 0x9be1, 0xd7 => 0x9bda, 0xd8 => 0x9d77, 0xd9 => 0x9d81,
    0xda => 0x9d8a, 0xdb => 0x9d84, 0xdc => 0x9d88, 0xdd => 0x9d71,
    0xde => 0x9d80, 0xdf => 0x9d78, 0xe0 => 0x9d86, 0xe1 => 0x9d8b,
    0xe2 => 0x9d8c, 0xe3 => 0x9d7d, 0xe4 => 0x9d6b, 0xe5 => 0x9d74,
    0xe6 => 0x9d75, 0xe7 => 0x9d70, 0xe8 => 0x9d69, 0xe9 => 0x9d85,
    0xea => 0x9d73, 0xeb => 0x9d7b, 0xec => 0x9d82, 0xed => 0x9d6f,
    0xee => 0x9d79, 0xef => 0x9d7f, 0xf0 => 0x9d87, 0xf1 => 0x9d68,
    0xf2 => 0x9e94, 0xf3 => 0x9e91, 0xf4 => 0x9ec0, 0xf5 => 0x9efc,
    0xf6 => 0x9f2d, 0xf7 => 0x9f40, 0xf8 => 0x9f41, 0xf9 => 0x9f4d,
    0xfa => 0x9f56, 0xfb => 0x9f57, 0xfc => 0x9f58, 0xfd => 0x5337,
    0xfe => 0x56b2,
  },
  0xf4 => {
    0x40 => 0x56b5, 0x41 => 0x56b3, 0x42 => 0x58e3, 0x43 => 0x5b45,
    0x44 => 0x5dc6, 0x45 => 0x5dc7, 0x46 => 0x5eee, 0x47 => 0x5eef,
    0x48 => 0x5fc0, 0x49 => 0x5fc1, 0x4a => 0x61f9, 0x4b => 0x6517,
    0x4c => 0x6516, 0x4d => 0x6515, 0x4e => 0x6513, 0x4f => 0x65df,
    0x50 => 0x66e8, 0x51 => 0x66e3, 0x52 => 0x66e4, 0x53 => 0x6af3,
    0x54 => 0x6af0, 0x55 => 0x6aea, 0x56 => 0x6ae8, 0x57 => 0x6af9,
    0x58 => 0x6af1, 0x59 => 0x6aee, 0x5a => 0x6aef, 0x5b => 0x703c,
    0x5c => 0x7035, 0x5d => 0x702f, 0x5e => 0x7037, 0x5f => 0x7034,
    0x60 => 0x7031, 0x61 => 0x7042, 0x62 => 0x7038, 0x63 => 0x703f,
    0x64 => 0x703a, 0x65 => 0x7039, 0x66 => 0x7040, 0x67 => 0x703b,
    0x68 => 0x7033, 0x69 => 0x7041, 0x6a => 0x7213, 0x6b => 0x7214,
    0x6c => 0x72a8, 0x6d => 0x737d, 0x6e => 0x737c, 0x6f => 0x74ba,
    0x70 => 0x76ab, 0x71 => 0x76aa, 0x72 => 0x76be, 0x73 => 0x76ed,
    0x74 => 0x77cc, 0x75 => 0x77ce, 0x76 => 0x77cf, 0x77 => 0x77cd,
    0x78 => 0x77f2, 0x79 => 0x7925, 0x7a => 0x7923, 0x7b => 0x7927,
    0x7c => 0x7928, 0x7d => 0x7924, 0x7e => 0x7929, 0xa1 => 0x79b2,
    0xa2 => 0x7a6e, 0xa3 => 0x7a6c, 0xa4 => 0x7a6d, 0xa5 => 0x7af7,
    0xa6 => 0x7c49, 0xa7 => 0x7c48, 0xa8 => 0x7c4a, 0xa9 => 0x7c47,
    0xaa => 0x7c45, 0xab => 0x7cee, 0xac => 0x7e7b, 0xad => 0x7e7e,
    0xae => 0x7e81, 0xaf => 0x7e80, 0xb0 => 0x7fba, 0xb1 => 0x7fff,
    0xb2 => 0x8079, 0xb3 => 0x81db, 0xb4 => 0x81d9, 0xb5 => 0x820b,
    0xb6 => 0x8268, 0xb7 => 0x8269, 0xb8 => 0x8622, 0xb9 => 0x85ff,
    0xba => 0x8601, 0xbb => 0x85fe, 0xbc => 0x861b, 0xbd => 0x8600,
    0xbe => 0x85f6, 0xbf => 0x8604, 0xc0 => 0x8609, 0xc1 => 0x8605,
    0xc2 => 0x860c, 0xc3 => 0x85fd, 0xc4 => 0x8819, 0xc5 => 0x8810,
    0xc6 => 0x8811, 0xc7 => 0x8817, 0xc8 => 0x8813, 0xc9 => 0x8816,
    0xca => 0x8963, 0xcb => 0x8966, 0xcc => 0x89b9, 0xcd => 0x89f7,
    0xce => 0x8b60, 0xcf => 0x8b6a, 0xd0 => 0x8b5d, 0xd1 => 0x8b68,
    0xd2 => 0x8b63, 0xd3 => 0x8b65, 0xd4 => 0x8b67, 0xd5 => 0x8b6d,
    0xd6 => 0x8dae, 0xd7 => 0x8e86, 0xd8 => 0x8e88, 0xd9 => 0x8e84,
    0xda => 0x8f59, 0xdb => 0x8f56, 0xdc => 0x8f57, 0xdd => 0x8f55,
    0xde => 0x8f58, 0xdf => 0x8f5a, 0xe0 => 0x908d, 0xe1 => 0x9143,
    0xe2 => 0x9141, 0xe3 => 0x91b7, 0xe4 => 0x91b5, 0xe5 => 0x91b2,
    0xe6 => 0x91b3, 0xe7 => 0x940b, 0xe8 => 0x9413, 0xe9 => 0x93fb,
    0xea => 0x9420, 0xeb => 0x940f, 0xec => 0x9414, 0xed => 0x93fe,
    0xee => 0x9415, 0xef => 0x9410, 0xf0 => 0x9428, 0xf1 => 0x9419,
    0xf2 => 0x940d, 0xf3 => 0x93f5, 0xf4 => 0x9400, 0xf5 => 0x93f7,
    0xf6 => 0x9407, 0xf7 => 0x940e, 0xf8 => 0x9416, 0xf9 => 0x9412,
    0xfa => 0x93fa, 0xfb => 0x9409, 0xfc => 0x93f8, 0xfd => 0x940a,
    0xfe => 0x93ff,
  },
  0xf5 => {
    0x40 => 0x93fc, 0x41 => 0x940c, 0x42 => 0x93f6, 0x43 => 0x9411,
    0x44 => 0x9406, 0x45 => 0x95de, 0x46 => 0x95e0, 0x47 => 0x95df,
    0x48 => 0x972e, 0x49 => 0x972f, 0x4a => 0x97b9, 0x4b => 0x97bb,
    0x4c => 0x97fd, 0x4d => 0x97fe, 0x4e => 0x9860, 0x4f => 0x9862,
    0x50 => 0x9863, 0x51 => 0x985f, 0x52 => 0x98c1, 0x53 => 0x98c2,
    0x54 => 0x9950, 0x55 => 0x994e, 0x56 => 0x9959, 0x57 => 0x994c,
    0x58 => 0x994b, 0x59 => 0x9953, 0x5a => 0x9a32, 0x5b => 0x9a34,
    0x5c => 0x9a31, 0x5d => 0x9a2c, 0x5e => 0x9a2a, 0x5f => 0x9a36,
    0x60 => 0x9a29, 0x61 => 0x9a2e, 0x62 => 0x9a38, 0x63 => 0x9a2d,
    0x64 => 0x9ac7, 0x65 => 0x9aca, 0x66 => 0x9ac6, 0x67 => 0x9b10,
    0x68 => 0x9b12, 0x69 => 0x9b11, 0x6a => 0x9c0b, 0x6b => 0x9c08,
    0x6c => 0x9bf7, 0x6d => 0x9c05, 0x6e => 0x9c12, 0x6f => 0x9bf8,
    0x70 => 0x9c40, 0x71 => 0x9c07, 0x72 => 0x9c0e, 0x73 => 0x9c06,
    0x74 => 0x9c17, 0x75 => 0x9c14, 0x76 => 0x9c09, 0x77 => 0x9d9f,
    0x78 => 0x9d99, 0x79 => 0x9da4, 0x7a => 0x9d9d, 0x7b => 0x9d92,
    0x7c => 0x9d98, 0x7d => 0x9d90, 0x7e => 0x9d9b, 0xa1 => 0x9da0,
    0xa2 => 0x9d94, 0xa3 => 0x9d9c, 0xa4 => 0x9daa, 0xa5 => 0x9d97,
    0xa6 => 0x9da1, 0xa7 => 0x9d9a, 0xa8 => 0x9da2, 0xa9 => 0x9da8,
    0xaa => 0x9d9e, 0xab => 0x9da3, 0xac => 0x9dbf, 0xad => 0x9da9,
    0xae => 0x9d96, 0xaf => 0x9da6, 0xb0 => 0x9da7, 0xb1 => 0x9e99,
    0xb2 => 0x9e9b, 0xb3 => 0x9e9a, 0xb4 => 0x9ee5, 0xb5 => 0x9ee4,
    0xb6 => 0x9ee7, 0xb7 => 0x9ee6, 0xb8 => 0x9f30, 0xb9 => 0x9f2e,
    0xba => 0x9f5b, 0xbb => 0x9f60, 0xbc => 0x9f5e, 0xbd => 0x9f5d,
    0xbe => 0x9f59, 0xbf => 0x9f91, 0xc0 => 0x513a, 0xc1 => 0x5139,
    0xc2 => 0x5298, 0xc3 => 0x5297, 0xc4 => 0x56c3, 0xc5 => 0x56bd,
    0xc6 => 0x56be, 0xc7 => 0x5b48, 0xc8 => 0x5b47, 0xc9 => 0x5dcb,
    0xca => 0x5dcf, 0xcb => 0x5ef1, 0xcc => 0x61fd, 0xcd => 0x651b,
    0xce => 0x6b02, 0xcf => 0x6afc, 0xd0 => 0x6b03, 0xd1 => 0x6af8,
    0xd2 => 0x6b00, 0xd3 => 0x7043, 0xd4 => 0x7044, 0xd5 => 0x704a,
    0xd6 => 0x7048, 0xd7 => 0x7049, 0xd8 => 0x7045, 0xd9 => 0x7046,
    0xda => 0x721d, 0xdb => 0x721a, 0xdc => 0x7219, 0xdd => 0x737e,
    0xde => 0x7517, 0xdf => 0x766a, 0xe0 => 0x77d0, 0xe1 => 0x792d,
    0xe2 => 0x7931, 0xe3 => 0x792f, 0xe4 => 0x7c54, 0xe5 => 0x7c53,
    0xe6 => 0x7cf2, 0xe7 => 0x7e8a, 0xe8 => 0x7e87, 0xe9 => 0x7e88,
    0xea => 0x7e8b, 0xeb => 0x7e86, 0xec => 0x7e8d, 0xed => 0x7f4d,
    0xee => 0x7fbb, 0xef => 0x8030, 0xf0 => 0x81dd, 0xf1 => 0x8618,
    0xf2 => 0x862a, 0xf3 => 0x8626, 0xf4 => 0x861f, 0xf5 => 0x8623,
    0xf6 => 0x861c, 0xf7 => 0x8619, 0xf8 => 0x8627, 0xf9 => 0x862e,
    0xfa => 0x8621, 0xfb => 0x8620, 0xfc => 0x8629, 0xfd => 0x861e,
    0xfe => 0x8625,
  },
  0xf6 => {
    0x40 => 0x8829, 0x41 => 0x881d, 0x42 => 0x881b, 0x43 => 0x8820,
    0x44 => 0x8824, 0x45 => 0x881c, 0x46 => 0x882b, 0x47 => 0x884a,
    0x48 => 0x896d, 0x49 => 0x8969, 0x4a => 0x896e, 0x4b => 0x896b,
    0x4c => 0x89fa, 0x4d => 0x8b79, 0x4e => 0x8b78, 0x4f => 0x8b45,
    0x50 => 0x8b7a, 0x51 => 0x8b7b, 0x52 => 0x8d10, 0x53 => 0x8d14,
    0x54 => 0x8daf, 0x55 => 0x8e8e, 0x56 => 0x8e8c, 0x57 => 0x8f5e,
    0x58 => 0x8f5b, 0x59 => 0x8f5d, 0x5a => 0x9146, 0x5b => 0x9144,
    0x5c => 0x9145, 0x5d => 0x91b9, 0x5e => 0x943f, 0x5f => 0x943b,
    0x60 => 0x9436, 0x61 => 0x9429, 0x62 => 0x943d, 0x63 => 0x943c,
    0x64 => 0x9430, 0x65 => 0x9439, 0x66 => 0x942a, 0x67 => 0x9437,
    0x68 => 0x942c, 0x69 => 0x9440, 0x6a => 0x9431, 0x6b => 0x95e5,
    0x6c => 0x95e4, 0x6d => 0x95e3, 0x6e => 0x9735, 0x6f => 0x973a,
    0x70 => 0x97bf, 0x71 => 0x97e1, 0x72 => 0x9864, 0x73 => 0x98c9,
    0x74 => 0x98c6, 0x75 => 0x98c0, 0x76 => 0x9958, 0x77 => 0x9956,
    0x78 => 0x9a39, 0x79 => 0x9a3d, 0x7a => 0x9a46, 0x7b => 0x9a44,
    0x7c => 0x9a42, 0x7d => 0x9a41, 0x7e => 0x9a3a, 0xa1 => 0x9a3f,
    0xa2 => 0x9acd, 0xa3 => 0x9b15, 0xa4 => 0x9b17, 0xa5 => 0x9b18,
    0xa6 => 0x9b16, 0xa7 => 0x9b3a, 0xa8 => 0x9b52, 0xa9 => 0x9c2b,
    0xaa => 0x9c1d, 0xab => 0x9c1c, 0xac => 0x9c2c, 0xad => 0x9c23,
    0xae => 0x9c28, 0xaf => 0x9c29, 0xb0 => 0x9c24, 0xb1 => 0x9c21,
    0xb2 => 0x9db7, 0xb3 => 0x9db6, 0xb4 => 0x9dbc, 0xb5 => 0x9dc1,
    0xb6 => 0x9dc7, 0xb7 => 0x9dca, 0xb8 => 0x9dcf, 0xb9 => 0x9dbe,
    0xba => 0x9dc5, 0xbb => 0x9dc3, 0xbc => 0x9dbb, 0xbd => 0x9db5,
    0xbe => 0x9dce, 0xbf => 0x9db9, 0xc0 => 0x9dba, 0xc1 => 0x9dac,
    0xc2 => 0x9dc8, 0xc3 => 0x9db1, 0xc4 => 0x9dad, 0xc5 => 0x9dcc,
    0xc6 => 0x9db3, 0xc7 => 0x9dcd, 0xc8 => 0x9db2, 0xc9 => 0x9e7a,
    0xca => 0x9e9c, 0xcb => 0x9eeb, 0xcc => 0x9eee, 0xcd => 0x9eed,
    0xce => 0x9f1b, 0xcf => 0x9f18, 0xd0 => 0x9f1a, 0xd1 => 0x9f31,
    0xd2 => 0x9f4e, 0xd3 => 0x9f65, 0xd4 => 0x9f64, 0xd5 => 0x9f92,
    0xd6 => 0x4eb9, 0xd7 => 0x56c6, 0xd8 => 0x56c5, 0xd9 => 0x56cb,
    0xda => 0x5971, 0xdb => 0x5b4b, 0xdc => 0x5b4c, 0xdd => 0x5dd5,
    0xde => 0x5dd1, 0xdf => 0x5ef2, 0xe0 => 0x6521, 0xe1 => 0x6520,
    0xe2 => 0x6526, 0xe3 => 0x6522, 0xe4 => 0x6b0b, 0xe5 => 0x6b08,
    0xe6 => 0x6b09, 0xe7 => 0x6c0d, 0xe8 => 0x7055, 0xe9 => 0x7056,
    0xea => 0x7057, 0xeb => 0x7052, 0xec => 0x721e, 0xed => 0x721f,
    0xee => 0x72a9, 0xef => 0x737f, 0xf0 => 0x74d8, 0xf1 => 0x74d5,
    0xf2 => 0x74d9, 0xf3 => 0x74d7, 0xf4 => 0x766d, 0xf5 => 0x76ad,
    0xf6 => 0x7935, 0xf7 => 0x79b4, 0xf8 => 0x7a70, 0xf9 => 0x7a71,
    0xfa => 0x7c57, 0xfb => 0x7c5c, 0xfc => 0x7c59, 0xfd => 0x7c5b,
    0xfe => 0x7c5a,
  },
  0xf7 => {
    0x40 => 0x7cf4, 0x41 => 0x7cf1, 0x42 => 0x7e91, 0x43 => 0x7f4f,
    0x44 => 0x7f87, 0x45 => 0x81de, 0x46 => 0x826b, 0x47 => 0x8634,
    0x48 => 0x8635, 0x49 => 0x8633, 0x4a => 0x862c, 0x4b => 0x8632,
    0x4c => 0x8636, 0x4d => 0x882c, 0x4e => 0x8828, 0x4f => 0x8826,
    0x50 => 0x882a, 0x51 => 0x8825, 0x52 => 0x8971, 0x53 => 0x89bf,
    0x54 => 0x89be, 0x55 => 0x89fb, 0x56 => 0x8b7e, 0x57 => 0x8b84,
    0x58 => 0x8b82, 0x59 => 0x8b86, 0x5a => 0x8b85, 0x5b => 0x8b7f,
    0x5c => 0x8d15, 0x5d => 0x8e95, 0x5e => 0x8e94, 0x5f => 0x8e9a,
    0x60 => 0x8e92, 0x61 => 0x8e90, 0x62 => 0x8e96, 0x63 => 0x8e97,
    0x64 => 0x8f60, 0x65 => 0x8f62, 0x66 => 0x9147, 0x67 => 0x944c,
    0x68 => 0x9450, 0x69 => 0x944a, 0x6a => 0x944b, 0x6b => 0x944f,
    0x6c => 0x9447, 0x6d => 0x9445, 0x6e => 0x9448, 0x6f => 0x9449,
    0x70 => 0x9446, 0x71 => 0x973f, 0x72 => 0x97e3, 0x73 => 0x986a,
    0x74 => 0x9869, 0x75 => 0x98cb, 0x76 => 0x9954, 0x77 => 0x995b,
    0x78 => 0x9a4e, 0x79 => 0x9a53, 0x7a => 0x9a54, 0x7b => 0x9a4c,
    0x7c => 0x9a4f, 0x7d => 0x9a48, 0x7e => 0x9a4a, 0xa1 => 0x9a49,
    0xa2 => 0x9a52, 0xa3 => 0x9a50, 0xa4 => 0x9ad0, 0xa5 => 0x9b19,
    0xa6 => 0x9b2b, 0xa7 => 0x9b3b, 0xa8 => 0x9b56, 0xa9 => 0x9b55,
    0xaa => 0x9c46, 0xab => 0x9c48, 0xac => 0x9c3f, 0xad => 0x9c44,
    0xae => 0x9c39, 0xaf => 0x9c33, 0xb0 => 0x9c41, 0xb1 => 0x9c3c,
    0xb2 => 0x9c37, 0xb3 => 0x9c34, 0xb4 => 0x9c32, 0xb5 => 0x9c3d,
    0xb6 => 0x9c36, 0xb7 => 0x9ddb, 0xb8 => 0x9dd2, 0xb9 => 0x9dde,
    0xba => 0x9dda, 0xbb => 0x9dcb, 0xbc => 0x9dd0, 0xbd => 0x9ddc,
    0xbe => 0x9dd1, 0xbf => 0x9ddf, 0xc0 => 0x9de9, 0xc1 => 0x9dd9,
    0xc2 => 0x9dd8, 0xc3 => 0x9dd6, 0xc4 => 0x9df5, 0xc5 => 0x9dd5,
    0xc6 => 0x9ddd, 0xc7 => 0x9eb6, 0xc8 => 0x9ef0, 0xc9 => 0x9f35,
    0xca => 0x9f33, 0xcb => 0x9f32, 0xcc => 0x9f42, 0xcd => 0x9f6b,
    0xce => 0x9f95, 0xcf => 0x9fa2, 0xd0 => 0x513d, 0xd1 => 0x5299,
    0xd2 => 0x58e8, 0xd3 => 0x58e7, 0xd4 => 0x5972, 0xd5 => 0x5b4d,
    0xd6 => 0x5dd8, 0xd7 => 0x882f, 0xd8 => 0x5f4f, 0xd9 => 0x6201,
    0xda => 0x6203, 0xdb => 0x6204, 0xdc => 0x6529, 0xdd => 0x6525,
    0xde => 0x6596, 0xdf => 0x66eb, 0xe0 => 0x6b11, 0xe1 => 0x6b12,
    0xe2 => 0x6b0f, 0xe3 => 0x6bca, 0xe4 => 0x705b, 0xe5 => 0x705a,
    0xe6 => 0x7222, 0xe7 => 0x7382, 0xe8 => 0x7381, 0xe9 => 0x7383,
    0xea => 0x7670, 0xeb => 0x77d4, 0xec => 0x7c67, 0xed => 0x7c66,
    0xee => 0x7e95, 0xef => 0x826c, 0xf0 => 0x863a, 0xf1 => 0x8640,
    0xf2 => 0x8639, 0xf3 => 0x863c, 0xf4 => 0x8631, 0xf5 => 0x863b,
    0xf6 => 0x863e, 0xf7 => 0x8830, 0xf8 => 0x8832, 0xf9 => 0x882e,
    0xfa => 0x8833, 0xfb => 0x8976, 0xfc => 0x8974, 0xfd => 0x8973,
    0xfe => 0x89fe,
  },
  0xf8 => {
    0x40 => 0x8b8c, 0x41 => 0x8b8e, 0x42 => 0x8b8b, 0x43 => 0x8b88,
    0x44 => 0x8c45, 0x45 => 0x8d19, 0x46 => 0x8e98, 0x47 => 0x8f64,
    0x48 => 0x8f63, 0x49 => 0x91bc, 0x4a => 0x9462, 0x4b => 0x9455,
    0x4c => 0x945d, 0x4d => 0x9457, 0x4e => 0x945e, 0x4f => 0x97c4,
    0x50 => 0x97c5, 0x51 => 0x9800, 0x52 => 0x9a56, 0x53 => 0x9a59,
    0x54 => 0x9b1e, 0x55 => 0x9b1f, 0x56 => 0x9b20, 0x57 => 0x9c52,
    0x58 => 0x9c58, 0x59 => 0x9c50, 0x5a => 0x9c4a, 0x5b => 0x9c4d,
    0x5c => 0x9c4b, 0x5d => 0x9c55, 0x5e => 0x9c59, 0x5f => 0x9c4c,
    0x60 => 0x9c4e, 0x61 => 0x9dfb, 0x62 => 0x9df7, 0x63 => 0x9def,
    0x64 => 0x9de3, 0x65 => 0x9deb, 0x66 => 0x9df8, 0x67 => 0x9de4,
    0x68 => 0x9df6, 0x69 => 0x9de1, 0x6a => 0x9dee, 0x6b => 0x9de6,
    0x6c => 0x9df2, 0x6d => 0x9df0, 0x6e => 0x9de2, 0x6f => 0x9dec,
    0x70 => 0x9df4, 0x71 => 0x9df3, 0x72 => 0x9de8, 0x73 => 0x9ded,
    0x74 => 0x9ec2, 0x75 => 0x9ed0, 0x76 => 0x9ef2, 0x77 => 0x9ef3,
    0x78 => 0x9f06, 0x79 => 0x9f1c, 0x7a => 0x9f38, 0x7b => 0x9f37,
    0x7c => 0x9f36, 0x7d => 0x9f43, 0x7e => 0x9f4f, 0xa1 => 0x9f71,
    0xa2 => 0x9f70, 0xa3 => 0x9f6e, 0xa4 => 0x9f6f, 0xa5 => 0x56d3,
    0xa6 => 0x56cd, 0xa7 => 0x5b4e, 0xa8 => 0x5c6d, 0xa9 => 0x652d,
    0xaa => 0x66ed, 0xab => 0x66ee, 0xac => 0x6b13, 0xad => 0x705f,
    0xae => 0x7061, 0xaf => 0x705d, 0xb0 => 0x7060, 0xb1 => 0x7223,
    0xb2 => 0x74db, 0xb3 => 0x74e5, 0xb4 => 0x77d5, 0xb5 => 0x7938,
    0xb6 => 0x79b7, 0xb7 => 0x79b6, 0xb8 => 0x7c6a, 0xb9 => 0x7e97,
    0xba => 0x7f89, 0xbb => 0x826d, 0xbc => 0x8643, 0xbd => 0x8838,
    0xbe => 0x8837, 0xbf => 0x8835, 0xc0 => 0x884b, 0xc1 => 0x8b94,
    0xc2 => 0x8b95, 0xc3 => 0x8e9e, 0xc4 => 0x8e9f, 0xc5 => 0x8ea0,
    0xc6 => 0x8e9d, 0xc7 => 0x91be, 0xc8 => 0x91bd, 0xc9 => 0x91c2,
    0xca => 0x946b, 0xcb => 0x9468, 0xcc => 0x9469, 0xcd => 0x96e5,
    0xce => 0x9746, 0xcf => 0x9743, 0xd0 => 0x9747, 0xd1 => 0x97c7,
    0xd2 => 0x97e5, 0xd3 => 0x9a5e, 0xd4 => 0x9ad5, 0xd5 => 0x9b59,
    0xd6 => 0x9c63, 0xd7 => 0x9c67, 0xd8 => 0x9c66, 0xd9 => 0x9c62,
    0xda => 0x9c5e, 0xdb => 0x9c60, 0xdc => 0x9e02, 0xdd => 0x9dfe,
    0xde => 0x9e07, 0xdf => 0x9e03, 0xe0 => 0x9e06, 0xe1 => 0x9e05,
    0xe2 => 0x9e00, 0xe3 => 0x9e01, 0xe4 => 0x9e09, 0xe5 => 0x9dff,
    0xe6 => 0x9dfd, 0xe7 => 0x9e04, 0xe8 => 0x9ea0, 0xe9 => 0x9f1e,
    0xea => 0x9f46, 0xeb => 0x9f74, 0xec => 0x9f75, 0xed => 0x9f76,
    0xee => 0x56d4, 0xef => 0x652e, 0xf0 => 0x65b8, 0xf1 => 0x6b18,
    0xf2 => 0x6b19, 0xf3 => 0x6b17, 0xf4 => 0x6b1a, 0xf5 => 0x7062,
    0xf6 => 0x7226, 0xf7 => 0x72aa, 0xf8 => 0x77d8, 0xf9 => 0x77d9,
    0xfa => 0x7939, 0xfb => 0x7c69, 0xfc => 0x7c6b, 0xfd => 0x7cf6,
    0xfe => 0x7e9a,
  },
  0xf9 => {
    0x40 => 0x7e98, 0x41 => 0x7e9b, 0x42 => 0x7e99, 0x43 => 0x81e0,
    0x44 => 0x81e1, 0x45 => 0x8646, 0x46 => 0x8647, 0x47 => 0x8648,
    0x48 => 0x8979, 0x49 => 0x897a, 0x4a => 0x897c, 0x4b => 0x897b,
    0x4c => 0x89ff, 0x4d => 0x8b98, 0x4e => 0x8b99, 0x4f => 0x8ea5,
    0x50 => 0x8ea4, 0x51 => 0x8ea3, 0x52 => 0x946e, 0x53 => 0x946d,
    0x54 => 0x946f, 0x55 => 0x9471, 0x56 => 0x9473, 0x57 => 0x9749,
    0x58 => 0x9872, 0x59 => 0x995f, 0x5a => 0x9c68, 0x5b => 0x9c6e,
    0x5c => 0x9c6d, 0x5d => 0x9e0b, 0x5e => 0x9e0d, 0x5f => 0x9e10,
    0x60 => 0x9e0f, 0x61 => 0x9e12, 0x62 => 0x9e11, 0x63 => 0x9ea1,
    0x64 => 0x9ef5, 0x65 => 0x9f09, 0x66 => 0x9f47, 0x67 => 0x9f78,
    0x68 => 0x9f7b, 0x69 => 0x9f7a, 0x6a => 0x9f79, 0x6b => 0x571e,
    0x6c => 0x7066, 0x6d => 0x7c6f, 0x6e => 0x883c, 0x6f => 0x8db2,
    0x70 => 0x8ea6, 0x71 => 0x91c3, 0x72 => 0x9474, 0x73 => 0x9478,
    0x74 => 0x9476, 0x75 => 0x9475, 0x76 => 0x9a60, 0x77 => 0x9c74,
    0x78 => 0x9c73, 0x79 => 0x9c71, 0x7a => 0x9c75, 0x7b => 0x9e14,
    0x7c => 0x9e13, 0x7d => 0x9ef6, 0x7e => 0x9f0a, 0xa1 => 0x9fa4,
    0xa2 => 0x7068, 0xa3 => 0x7065, 0xa4 => 0x7cf7, 0xa5 => 0x866a,
    0xa6 => 0x883e, 0xa7 => 0x883d, 0xa8 => 0x883f, 0xa9 => 0x8b9e,
    0xaa => 0x8c9c, 0xab => 0x8ea9, 0xac => 0x8ec9, 0xad => 0x974b,
    0xae => 0x9873, 0xaf => 0x9874, 0xb0 => 0x98cc, 0xb1 => 0x9961,
    0xb2 => 0x99ab, 0xb3 => 0x9a64, 0xb4 => 0x9a66, 0xb5 => 0x9a67,
    0xb6 => 0x9b24, 0xb7 => 0x9e15, 0xb8 => 0x9e17, 0xb9 => 0x9f48,
    0xba => 0x6207, 0xbb => 0x6b1e, 0xbc => 0x7227, 0xbd => 0x864c,
    0xbe => 0x8ea8, 0xbf => 0x9482, 0xc0 => 0x9480, 0xc1 => 0x9481,
    0xc2 => 0x9a69, 0xc3 => 0x9a68, 0xc4 => 0x9b2e, 0xc5 => 0x9e19,
    0xc6 => 0x7229, 0xc7 => 0x864b, 0xc8 => 0x8b9f, 0xc9 => 0x9483,
    0xca => 0x9c79, 0xcb => 0x9eb7, 0xcc => 0x7675, 0xcd => 0x9a6b,
    0xce => 0x9c7a, 0xcf => 0x9e1d, 0xd0 => 0x7069, 0xd1 => 0x706a,
    0xd2 => 0x9ea4, 0xd3 => 0x9f7e, 0xd4 => 0x9f49, 0xd5 => 0x9f98,
  },
);

1; # end
