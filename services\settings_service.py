"""
Settings service for saving and loading application settings
"""

import os
import json

class SettingsService:
    def __init__(self):
        self.settings_file = os.path.join(os.path.expanduser("~"), ".gema_settings.json")
        self.default_settings = {
            'last_directory': os.path.expanduser("~"),
            'theme': 'light',
            'window_geometry': '900x700',
            'recent_files': []
        }
        self.settings = self.load_settings()
    
    def load_settings(self):
        try:
            if os.path.exists(self.settings_file):
                with open(self.settings_file, 'r', encoding='utf-8') as f:
                    loaded_settings = json.load(f)
                    # Merge with defaults to ensure all keys exist
                    settings = self.default_settings.copy()
                    settings.update(loaded_settings)
                    return settings
        except Exception as e:
            print(f"Error loading settings: {e}")
        
        return self.default_settings.copy()
    
    def save_settings(self):
        try:
            # Ensure directory exists
            settings_dir = os.path.dirname(self.settings_file)
            if settings_dir and not os.path.exists(settings_dir):
                os.makedirs(settings_dir, exist_ok=True)

            with open(self.settings_file, 'w', encoding='utf-8') as f:
                json.dump(self.settings, f, indent=2, ensure_ascii=False)
            return True
        except Exception as e:
            print(f"Error saving settings: {e}")
            return False
    
    def get_setting(self, key, default=None):
        return self.settings.get(key, default)
    
    def set_setting(self, key, value):
        self.settings[key] = value
        # Don't fail if save fails, just log the error
        if not self.save_settings():
            print(f"Warning: Could not save setting {key}={value}")
    
    def get_last_directory(self):
        path = self.settings.get('last_directory', os.path.expanduser("~"))
        # Verify path still exists
        if os.path.exists(path) and os.path.isdir(path):
            return path
        return os.path.expanduser("~")
    
    def set_last_directory(self, path):
        if os.path.exists(path) and os.path.isdir(path):
            self.set_setting('last_directory', path)
    
    def get_theme(self):
        return self.settings.get('theme', 'light')
    
    def set_theme(self, theme):
        self.set_setting('theme', theme)
    
    def get_window_geometry(self):
        return self.settings.get('window_geometry', '900x700')
    
    def set_window_geometry(self, geometry):
        self.set_setting('window_geometry', geometry)
    
    def add_recent_file(self, file_path):
        recent_files = self.settings.get('recent_files', [])
        
        # Remove if already exists
        if file_path in recent_files:
            recent_files.remove(file_path)
        
        # Add to beginning
        recent_files.insert(0, file_path)
        
        # Keep only last 10 files
        recent_files = recent_files[:10]
        
        # Filter out non-existent files
        recent_files = [f for f in recent_files if os.path.exists(f)]
        
        self.set_setting('recent_files', recent_files)
    
    def get_recent_files(self):
        recent_files = self.settings.get('recent_files', [])
        # Filter out non-existent files
        return [f for f in recent_files if os.path.exists(f)]
    
    def clear_recent_files(self):
        self.set_setting('recent_files', [])
