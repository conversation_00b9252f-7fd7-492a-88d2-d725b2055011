#------------------------------------------------------------------------------
# File:         MacKorean.pm
#
# Description:  Mac Korean to Unicode
#
# Revisions:    2010/01/20 - P<PERSON> created
#
# References:   1) http://unicode.org/Public/MAPPINGS/VENDORS/APPLE/KOREAN.TXT
#
# Notes:        The table omits 1-byte characters with the same values as Unicode
#------------------------------------------------------------------------------
use strict;

%Image::ExifTool::Charset::MacKorean = (
  0x80 => 0xa0, 0x81 => 0x20a9, 0x82 => [0x2013,0xf87f], 0x83 => 0xa9,
  0x84 => [0xff3f,0xf87f], 0xff => [0x2026,0xf87f],
  0xa1 => {
    0x41 => [0x300c,0xf87f], 0x42 => [0x300d,0xf87f], 0x43 => [0x300c,0xf87b],
    0x44 => [0x300d,0xf87b], 0x45 => [0x300c,0xf87c], 0x46 => [0x300d,0xf87c],
    0x47 => [0x300e,0xf87c], 0x48 => [0x300f,0xf87c], 0x49 => [0x300a,0xf878],
    0x4a => [0x300b,0xf878], 0x4b => [0x3008,0xf878], 0x4c => [0x3009,0xf878],
    0x4d => 0xfe59, 0x4e => 0xfe5a, 0x4f => [0xfe59,0xf87f],
    0x50 => [0xfe5a,0xf87f], 0x51 => [0x2985,0xf87f], 0x52 => [0x2986,0xf87f],
    0x53 => [0x2985,0xf879], 0x54 => [0x2986,0xf879], 0x55 => [0x2985,0xf87c],
    0x56 => [0x2986,0xf87c], 0x57 => [0x28,0xf87c], 0x58 => [0x29,0xf87c],
    0x59 => 0x2985, 0x5a => 0x2986, 0x5b => [0x3010,0xf87f],
    0x5c => [0x3011,0xf87f], 0x5d => 0x3016, 0x5e => 0x3017, 0x5f => 0x3018,
    0x60 => 0x3019, 0x61 => [0x5b,0xf87b], 0x62 => [0x5d,0xf87b],
    0x63 => [0x5b,0xf87c], 0x64 => [0x5d,0xf87c], 0x65 => [0x2985,0xf87b],
    0x66 => [0x2986,0xf87b], 0x67 => [0x2020,0xf87f], 0x68 => [0x2021,0xf87f],
    0x69 => [0x2020,0xf87b], 0x6a => [0x2021,0xf87c], 0x6b => [0x2020,0xf877],
    0x6c => [0x2a,0xf877], 0x6d => 0x2051, 0x6e => 0xf840, 0x6f => 0x201f,
    0x70 => 0x201b, 0x71 => 0x207a, 0x72 => 0x207b, 0x73 => [0xd7,0xf877],
    0x74 => [0x221e,0xf877], 0x75 => [0x223d,0xf877], 0x76 => [0xb1,0xf877],
    0x77 => [0x2213,0xf877], 0x78 => [0x3e,0xf877], 0x79 => [0x3c,0xf877],
    0x7a => 0x207c, 0x7b => [0x2260,0xf877], 0x7c => 0x207d, 0x7d => 0x207e,
    0x81 => [0x7b,0xf877], 0x82 => [0x7d,0xf877], 0x83 => [0x5b,0xf877],
    0x84 => [0x5d,0xf877], 0x85 => [0x2229,0xf877], 0x86 => [0x222a,0xf877],
    0x87 => [0x2282,0xf877], 0x88 => [0x2208,0xf877], 0x89 => [0x2211,0xf877],
    0x8a => [0x21,0xf877], 0x8b => [0x3007,0xf876], 0x8c => [0x4e00,0xf876],
    0x8d => [0x4e8c,0xf876], 0x8e => [0x4e09,0xf876], 0x8f => [0x56db,0xf876],
    0x90 => [0x4e94,0xf876], 0x91 => [0x516d,0xf876], 0x92 => [0x4e03,0xf876],
    0x93 => [0x516b,0xf876], 0x94 => [0x4e5d,0xf876], 0x95 => [0x5341,0xf876],
    0x96 => 0x204c, 0x97 => 0x204d, 0x98 => 0x02bc, 0x99 => 0x2997,
    0x9a => 0x2998, 0x9c => [0xff0a,0xf874], 0x9d => [0x2051,0xf87c],
    0x9e => [0xff0a,0xf875], 0x9f => [0xff0a,0xf871], 0xa0 => [0x2051,0xf879],
    0xa1 => 0x3000, 0xa2 => 0x3001, 0xa3 => 0x3002, 0xa4 => 0xb7,
    0xa5 => 0x2025, 0xa6 => 0x2026, 0xa7 => 0xa8, 0xa8 => 0x3003,
    0xa9 => 0x2013, 0xaa => 0x2014, 0xab => 0x2016, 0xac => 0xff3c,
    0xad => 0x301c, 0xae => 0x2018, 0xaf => 0x2019, 0xb0 => 0x201c,
    0xb1 => 0x201d, 0xb2 => 0x3014, 0xb3 => 0x3015, 0xb4 => 0x3008,
    0xb5 => 0x3009, 0xb6 => 0x300a, 0xb7 => 0x300b, 0xb8 => 0x300c,
    0xb9 => 0x300d, 0xba => 0x300e, 0xbb => 0x300f, 0xbc => 0x3010,
    0xbd => 0x3011, 0xbe => 0xb1, 0xbf => 0xd7, 0xc0 => 0xf7, 0xc1 => 0x2260,
    0xc2 => 0x2264, 0xc3 => 0x2265, 0xc4 => 0x221e, 0xc5 => 0x2234,
    0xc6 => 0xb0, 0xc7 => 0x2032, 0xc8 => 0x2033, 0xc9 => 0x2103,
    0xca => 0x212b, 0xcb => 0xa2, 0xcc => 0xa3, 0xcd => 0xa5, 0xce => 0x2642,
    0xcf => 0x2640, 0xd0 => 0x2220, 0xd1 => 0x22a5, 0xd2 => 0x2312,
    0xd3 => 0x2202, 0xd4 => 0x2207, 0xd5 => 0x2261, 0xd6 => 0x2252,
    0xd7 => 0xa7, 0xd8 => 0x203b, 0xd9 => 0x2606, 0xda => 0x2605,
    0xdb => 0x25cb, 0xdc => 0x25cf, 0xdd => 0x25ce, 0xde => 0x25c7,
    0xdf => 0x25c6, 0xe0 => 0x25a1, 0xe1 => 0x25a0, 0xe2 => 0x25b3,
    0xe3 => 0x25b2, 0xe4 => 0x25bd, 0xe5 => 0x25bc, 0xe6 => 0x2192,
    0xe7 => 0x2190, 0xe8 => 0x2191, 0xe9 => 0x2193, 0xea => 0x2194,
    0xeb => 0x3013, 0xec => 0x226a, 0xed => 0x226b, 0xee => 0x221a,
    0xef => 0x223d, 0xf0 => 0x221d, 0xf1 => 0x2235, 0xf2 => 0x222b,
    0xf3 => 0x222c, 0xf4 => 0x2208, 0xf5 => 0x220b, 0xf6 => 0x2286,
    0xf7 => 0x2287, 0xf8 => 0x2282, 0xf9 => 0x2283, 0xfa => 0x222a,
    0xfb => 0x2229, 0xfc => 0x2227, 0xfd => 0x2228, 0xfe => 0xac,
  },
  0xa2 => {
    0x41 => [0x2985,0xf878], 0x42 => [0x2986,0xf878], 0x43 => [0x2985,0xf873],
    0x44 => [0x2986,0xf873], 0x45 => [0xfe59,0xf87c], 0x46 => [0xfe5a,0xf87c],
    0x47 => [0x3016,0xf878], 0x48 => [0x3017,0xf878], 0x49 => [0x3010,0xf878],
    0x4a => [0x3011,0xf878], 0x4b => [0x28,0xf87f], 0x4c => [0x29,0xf87f],
    0xa1 => 0x21d2, 0xa2 => 0x21d4, 0xa3 => 0x2200, 0xa4 => 0x2203,
    0xa5 => 0xb4, 0xa6 => 0x02dc, 0xa7 => 0x02c7, 0xa8 => 0x02d8,
    0xa9 => 0x02dd, 0xaa => 0x02da, 0xab => 0x02d9, 0xac => 0xb8,
    0xad => 0x02db, 0xae => 0xa1, 0xaf => 0xbf, 0xb0 => 0x02d0, 0xb1 => 0x222e,
    0xb2 => 0x2211, 0xb3 => 0x220f, 0xb4 => 0xa4, 0xb5 => 0x2109,
    0xb6 => 0x2030, 0xb7 => 0x25c1, 0xb8 => 0x25c0, 0xb9 => 0x25b7,
    0xba => 0x25b6, 0xbb => 0x2664, 0xbc => 0x2660, 0xbd => 0x2661,
    0xbe => 0x2665, 0xbf => 0x2667, 0xc0 => 0x2663, 0xc1 => 0x25c9,
    0xc2 => 0x25c8, 0xc3 => 0x25a3, 0xc4 => 0x25d0, 0xc5 => 0x25d1,
    0xc6 => 0x2592, 0xc7 => 0x25a4, 0xc8 => 0x25a5, 0xc9 => 0x25a8,
    0xca => 0x25a7, 0xcb => 0x25a6, 0xcc => 0x25a9, 0xcd => 0x2668,
    0xce => 0x260f, 0xcf => 0x260e, 0xd0 => 0x261c, 0xd1 => 0x261e,
    0xd2 => 0xb6, 0xd3 => 0x2020, 0xd4 => 0x2021, 0xd5 => 0x2195,
    0xd6 => 0x2197, 0xd7 => 0x2199, 0xd8 => 0x2196, 0xd9 => 0x2198,
    0xda => 0x266d, 0xdb => 0x2669, 0xdc => 0x266a, 0xdd => 0x266c,
    0xde => 0x327f, 0xdf => 0x321c, 0xe0 => 0x2116, 0xe1 => 0x33c7,
    0xe2 => 0x2122, 0xe3 => 0x33c2, 0xe4 => 0x33d8, 0xe5 => 0x2121,
    0xe6 => [0x31,0x20de,0xf87c], 0xe7 => [0x32,0x20de,0xf87c],
    0xe8 => [0x33,0x20de,0xf87c], 0xe9 => [0x34,0x20de,0xf87c],
    0xea => [0x35,0x20de,0xf87c], 0xeb => [0x36,0x20de,0xf87c],
    0xec => [0x37,0x20de,0xf87c], 0xed => [0x38,0x20de,0xf87c],
    0xee => [0x39,0x20de,0xf87c], 0xef => [0xf863,0x5b,0x31,0x30,0x5d],
    0xf0 => [0xf863,0x5b,0x31,0x31,0x5d], 0xf1 => [0xf863,0x5b,0x31,0x32,0x5d],
    0xf2 => [0xf863,0x5b,0x31,0x33,0x5d], 0xf3 => [0xf863,0x5b,0x31,0x34,0x5d],
    0xf4 => [0xf863,0x5b,0x31,0x35,0x5d], 0xf5 => [0xf863,0x5b,0x31,0x36,0x5d],
    0xf6 => [0xf863,0x5b,0x31,0x37,0x5d], 0xf7 => [0xf863,0x5b,0x31,0x38,0x5d],
    0xf8 => [0xf863,0x5b,0x31,0x39,0x5d], 0xf9 => [0xf863,0x5b,0x32,0x30,0x5d],
    0xfa => [0xb6,0xf87f], 0xfb => [0x2016,0xf87b], 0xfc => [0x2016,0xf87c],
    0xfd => 0x22ee, 0xfe => 0x2237,
  },
  0xa3 => {
    0x41 => [0x31,0x20de,0xf87b], 0x42 => [0x32,0x20de,0xf87b],
    0x43 => [0x33,0x20de,0xf87b], 0x44 => [0x34,0x20de,0xf87b],
    0x45 => [0x35,0x20de,0xf87b], 0x46 => [0x36,0x20de,0xf87b],
    0x47 => [0x37,0x20de,0xf87b], 0x48 => [0x38,0x20de,0xf87b],
    0x49 => [0x39,0x20de,0xf87b], 0x4a => [0xf864,0x5b,0x31,0x30,0x5d],
    0x4b => [0xf864,0x5b,0x31,0x31,0x5d], 0x4c => [0xf864,0x5b,0x31,0x32,0x5d],
    0x4d => [0xf864,0x5b,0x31,0x33,0x5d], 0x4e => [0xf864,0x5b,0x31,0x34,0x5d],
    0x4f => [0xf864,0x5b,0x31,0x35,0x5d], 0x50 => [0xf864,0x5b,0x31,0x36,0x5d],
    0x51 => [0xf864,0x5b,0x31,0x37,0x5d], 0x52 => [0xf864,0x5b,0x31,0x38,0x5d],
    0x53 => [0xf864,0x5b,0x31,0x39,0x5d], 0x54 => [0xf864,0x5b,0x32,0x30,0x5d],
    0x55 => 0x278a, 0x56 => 0x278b, 0x57 => 0x278c, 0x58 => 0x278d,
    0x59 => 0x278e, 0x5a => 0x278f, 0x5b => 0x2790, 0x5c => 0x2791,
    0x5d => 0x2792, 0x5e => 0x2793, 0x5f => [0x24eb,0xf87f],
    0x60 => [0x24ec,0xf87f], 0x61 => [0x24ed,0xf87f], 0x62 => [0x24ee,0xf87f],
    0x63 => [0x24ef,0xf87f], 0x64 => [0x24f0,0xf87f], 0x65 => [0x24f1,0xf87f],
    0x66 => [0x24f2,0xf87f], 0x67 => [0x24f3,0xf87f], 0x68 => [0x24f4,0xf87f],
    0x69 => [0xf861,0x28,0x41,0x29], 0x6a => [0xf861,0x28,0x42,0x29],
    0x6b => [0xf861,0x28,0x43,0x29], 0x6c => [0xf861,0x28,0x44,0x29],
    0x6d => [0xf861,0x28,0x45,0x29], 0x6e => [0xf861,0x28,0x46,0x29],
    0x6f => [0xf861,0x28,0x47,0x29], 0x70 => [0xf861,0x28,0x48,0x29],
    0x71 => [0xf861,0x28,0x49,0x29], 0x72 => [0xf861,0x28,0x4a,0x29],
    0x73 => [0xf861,0x28,0x4b,0x29], 0x74 => [0xf861,0x28,0x4c,0x29],
    0x75 => [0xf861,0x28,0x4d,0x29], 0x76 => [0xf861,0x28,0x4e,0x29],
    0x77 => [0xf861,0x28,0x4f,0x29], 0x78 => [0xf861,0x28,0x50,0x29],
    0x79 => [0xf861,0x28,0x51,0x29], 0x7a => [0xf861,0x28,0x52,0x29],
    0x7b => [0xf861,0x28,0x53,0x29], 0x7c => [0xf861,0x28,0x54,0x29],
    0x7d => [0xf861,0x28,0x55,0x29], 0x81 => [0xf861,0x28,0x56,0x29],
    0x82 => [0xf861,0x28,0x57,0x29], 0x83 => [0xf861,0x28,0x58,0x29],
    0x84 => [0xf861,0x28,0x59,0x29], 0x85 => [0xf861,0x28,0x5a,0x29],
    0x86 => 0x24b6, 0x87 => 0x24b7, 0x88 => 0x24b8, 0x89 => 0x24b9,
    0x8a => 0x24ba, 0x8b => 0x24bb, 0x8c => 0x24bc, 0x8d => 0x24bd,
    0x8e => 0x24be, 0x8f => 0x24bf, 0x90 => 0x24c0, 0x91 => 0x24c1,
    0x92 => 0x24c2, 0x93 => 0x24c3, 0x94 => 0x24c4, 0x95 => 0x24c5,
    0x96 => 0x24c6, 0x97 => 0x24c7, 0x98 => 0x24c8, 0x99 => 0x24c9,
    0x9a => 0x24ca, 0x9b => 0x24cb, 0x9c => 0x24cc, 0x9d => 0x24cd,
    0x9e => 0x24ce, 0x9f => 0x24cf, 0xa1 => 0xff01, 0xa2 => 0xff02,
    0xa3 => 0xff03, 0xa4 => 0xff04, 0xa5 => 0xff05, 0xa6 => 0xff06,
    0xa7 => 0xff07, 0xa8 => 0xff08, 0xa9 => 0xff09, 0xaa => 0xff0a,
    0xab => 0xff0b, 0xac => 0xff0c, 0xad => 0xff0d, 0xae => 0xff0e,
    0xaf => 0xff0f, 0xb0 => 0xff10, 0xb1 => 0xff11, 0xb2 => 0xff12,
    0xb3 => 0xff13, 0xb4 => 0xff14, 0xb5 => 0xff15, 0xb6 => 0xff16,
    0xb7 => 0xff17, 0xb8 => 0xff18, 0xb9 => 0xff19, 0xba => 0xff1a,
    0xbb => 0xff1b, 0xbc => 0xff1c, 0xbd => 0xff1d, 0xbe => 0xff1e,
    0xbf => 0xff1f, 0xc0 => 0xff20, 0xc1 => 0xff21, 0xc2 => 0xff22,
    0xc3 => 0xff23, 0xc4 => 0xff24, 0xc5 => 0xff25, 0xc6 => 0xff26,
    0xc7 => 0xff27, 0xc8 => 0xff28, 0xc9 => 0xff29, 0xca => 0xff2a,
    0xcb => 0xff2b, 0xcc => 0xff2c, 0xcd => 0xff2d, 0xce => 0xff2e,
    0xcf => 0xff2f, 0xd0 => 0xff30, 0xd1 => 0xff31, 0xd2 => 0xff32,
    0xd3 => 0xff33, 0xd4 => 0xff34, 0xd5 => 0xff35, 0xd6 => 0xff36,
    0xd7 => 0xff37, 0xd8 => 0xff38, 0xd9 => 0xff39, 0xda => 0xff3a,
    0xdb => 0xff3b, 0xdc => 0xffe6, 0xdd => 0xff3d, 0xde => 0xff3e,
    0xdf => 0xff3f, 0xe0 => 0xff40, 0xe1 => 0xff41, 0xe2 => 0xff42,
    0xe3 => 0xff43, 0xe4 => 0xff44, 0xe5 => 0xff45, 0xe6 => 0xff46,
    0xe7 => 0xff47, 0xe8 => 0xff48, 0xe9 => 0xff49, 0xea => 0xff4a,
    0xeb => 0xff4b, 0xec => 0xff4c, 0xed => 0xff4d, 0xee => 0xff4e,
    0xef => 0xff4f, 0xf0 => 0xff50, 0xf1 => 0xff51, 0xf2 => 0xff52,
    0xf3 => 0xff53, 0xf4 => 0xff54, 0xf5 => 0xff55, 0xf6 => 0xff56,
    0xf7 => 0xff57, 0xf8 => 0xff58, 0xf9 => 0xff59, 0xfa => 0xff5a,
    0xfb => 0xff5b, 0xfc => 0xff5c, 0xfd => 0xff5d, 0xfe => 0x203e,
  },
  0xa4 => {
    0x41 => [0x31,0x20de,0xf87f], 0x42 => [0x32,0x20de,0xf87f],
    0x43 => [0x33,0x20de,0xf87f], 0x44 => [0x34,0x20de,0xf87f],
    0x45 => [0x35,0x20de,0xf87f], 0x46 => [0x36,0x20de,0xf87f],
    0x47 => [0x37,0x20de,0xf87f], 0x48 => [0x38,0x20de,0xf87f],
    0x49 => [0x39,0x20de,0xf87f], 0x4a => [0xf862,0x5b,0x31,0x30,0x5d],
    0x4b => [0xf862,0x5b,0x31,0x31,0x5d], 0x4c => [0xf862,0x5b,0x31,0x32,0x5d],
    0x4d => [0xf862,0x5b,0x31,0x33,0x5d], 0x4e => [0xf862,0x5b,0x31,0x34,0x5d],
    0x4f => [0xf862,0x5b,0x31,0x35,0x5d], 0x50 => [0xf862,0x5b,0x31,0x36,0x5d],
    0x51 => [0xf862,0x5b,0x31,0x37,0x5d], 0x52 => [0xf862,0x5b,0x31,0x38,0x5d],
    0x53 => [0xf862,0x5b,0x31,0x39,0x5d], 0x54 => [0xf862,0x5b,0x32,0x30,0x5d],
    0x55 => [0x31,0x20de,0xf87a], 0x56 => [0x32,0x20de,0xf87a],
    0x57 => [0x33,0x20de,0xf87a], 0x58 => [0x34,0x20de,0xf87a],
    0x59 => [0x35,0x20de,0xf87a], 0x5a => [0x36,0x20de,0xf87a],
    0x5b => [0x37,0x20de,0xf87a], 0x5c => [0x38,0x20de,0xf87a],
    0x5d => [0x39,0x20de,0xf87a], 0x5e => [0xf865,0x5b,0x31,0x30,0x5d],
    0x5f => [0xf865,0x5b,0x31,0x31,0x5d], 0x60 => [0xf865,0x5b,0x31,0x32,0x5d],
    0x61 => [0xf865,0x5b,0x31,0x33,0x5d], 0x62 => [0xf865,0x5b,0x31,0x34,0x5d],
    0x63 => [0xf865,0x5b,0x31,0x35,0x5d], 0x64 => [0xf865,0x5b,0x31,0x36,0x5d],
    0x65 => [0xf865,0x5b,0x31,0x37,0x5d], 0x66 => [0xf865,0x5b,0x31,0x38,0x5d],
    0x67 => [0xf865,0x5b,0x31,0x39,0x5d], 0x68 => [0xf865,0x5b,0x32,0x30,0x5d],
    0x69 => [0x278a,0xf87f], 0x6a => [0x278b,0xf87f], 0x6b => [0x278c,0xf87f],
    0x6c => [0x278d,0xf87f], 0x6d => [0x278e,0xf87f], 0x6e => [0x278f,0xf87f],
    0x6f => [0x2790,0xf87f], 0x70 => [0x2791,0xf87f], 0x71 => [0x2792,0xf87f],
    0x72 => [0x2793,0xf87f], 0x73 => [0x24eb,0xf878], 0x74 => [0x24ec,0xf878],
    0x75 => [0x24ed,0xf878], 0x76 => [0x24ee,0xf878], 0x77 => [0x24ef,0xf878],
    0x78 => [0x24f0,0xf878], 0x79 => [0x24f1,0xf878], 0x7a => [0x24f2,0xf878],
    0x7b => [0x24f3,0xf878], 0x7c => [0x24f4,0xf878], 0x7d => 0x2a26,
    0x81 => 0x227a, 0x82 => 0x227b, 0x83 => 0x22ce, 0x84 => 0x22cf,
    0x85 => 0x2280, 0x86 => 0x2281, 0x87 => 0x2270, 0x88 => 0x2271,
    0x89 => 0x2272, 0x8a => 0x2273, 0x8b => 0x2ac5, 0x8c => 0x2acb,
    0x8d => 0x2ac6, 0x8e => 0x2acc, 0x8f => 0x2276, 0x90 => 0x2277,
    0x91 => 0x2279, 0x92 => 0x22da, 0x93 => 0x22db, 0x94 => 0x2a8b,
    0x95 => 0x2a8c, 0x96 => 0x2a91, 0x97 => 0x2a92, 0x98 => [0x2222,0xf87f],
    0x99 => 0x2245, 0x9a => 0x2243, 0x9b => 0x2248, 0x9c => 0x29a3,
    0x9d => 0x22a4, 0x9e => [0x2225,0x0347], 0x9f => [0x2afd,0x0347],
    0xa1 => 0x3131, 0xa2 => 0x3132, 0xa3 => 0x3133, 0xa4 => 0x3134,
    0xa5 => 0x3135, 0xa6 => 0x3136, 0xa7 => 0x3137, 0xa8 => 0x3138,
    0xa9 => 0x3139, 0xaa => 0x313a, 0xab => 0x313b, 0xac => 0x313c,
    0xad => 0x313d, 0xae => 0x313e, 0xaf => 0x313f, 0xb0 => 0x3140,
    0xb1 => 0x3141, 0xb2 => 0x3142, 0xb3 => 0x3143, 0xb4 => 0x3144,
    0xb5 => 0x3145, 0xb6 => 0x3146, 0xb7 => 0x3147, 0xb8 => 0x3148,
    0xb9 => 0x3149, 0xba => 0x314a, 0xbb => 0x314b, 0xbc => 0x314c,
    0xbd => 0x314d, 0xbe => 0x314e, 0xbf => 0x314f, 0xc0 => 0x3150,
    0xc1 => 0x3151, 0xc2 => 0x3152, 0xc3 => 0x3153, 0xc4 => 0x3154,
    0xc5 => 0x3155, 0xc6 => 0x3156, 0xc7 => 0x3157, 0xc8 => 0x3158,
    0xc9 => 0x3159, 0xca => 0x315a, 0xcb => 0x315b, 0xcc => 0x315c,
    0xcd => 0x315d, 0xce => 0x315e, 0xcf => 0x315f, 0xd0 => 0x3160,
    0xd1 => 0x3161, 0xd2 => 0x3162, 0xd3 => 0x3163, 0xd4 => 0x3164,
    0xd5 => 0x3165, 0xd6 => 0x3166, 0xd7 => 0x3167, 0xd8 => 0x3168,
    0xd9 => 0x3169, 0xda => 0x316a, 0xdb => 0x316b, 0xdc => 0x316c,
    0xdd => 0x316d, 0xde => 0x316e, 0xdf => 0x316f, 0xe0 => 0x3170,
    0xe1 => 0x3171, 0xe2 => 0x3172, 0xe3 => 0x3173, 0xe4 => 0x3174,
    0xe5 => 0x3175, 0xe6 => 0x3176, 0xe7 => 0x3177, 0xe8 => 0x3178,
    0xe9 => 0x3179, 0xea => 0x317a, 0xeb => 0x317b, 0xec => 0x317c,
    0xed => 0x317d, 0xee => 0x317e, 0xef => 0x317f, 0xf0 => 0x3180,
    0xf1 => 0x3181, 0xf2 => 0x3182, 0xf3 => 0x3183, 0xf4 => 0x3184,
    0xf5 => 0x3185, 0xf6 => 0x3186, 0xf7 => 0x3187, 0xf8 => 0x3188,
    0xf9 => 0x3189, 0xfa => 0x318a, 0xfb => 0x318b, 0xfc => 0x318c,
    0xfd => 0x318d, 0xfe => 0x318e,
  },
  0xa5 => {
    0x41 => [0x30,0x20de], 0x42 => [0x31,0x20de], 0x43 => [0x32,0x20de],
    0x44 => [0x33,0x20de], 0x45 => [0x34,0x20de], 0x46 => [0x35,0x20de],
    0x47 => [0x36,0x20de], 0x48 => [0x37,0x20de], 0x49 => [0x38,0x20de],
    0x4a => [0x39,0x20de], 0x4b => [0x24ea,0xf87f], 0x4c => [0x2460,0xf87f],
    0x4d => [0x2461,0xf87f], 0x4e => [0x2462,0xf87f], 0x4f => [0x2463,0xf87f],
    0x50 => [0x2464,0xf87f], 0x51 => [0x2465,0xf87f], 0x52 => [0x2466,0xf87f],
    0x53 => [0x2467,0xf87f], 0x54 => [0x2468,0xf87f],
    0x55 => [0xf860,0x41,0x29], 0x56 => [0xf860,0x42,0x29],
    0x57 => [0xf860,0x43,0x29], 0x58 => [0xf860,0x44,0x29],
    0x59 => [0xf860,0x45,0x29], 0x5a => [0xf860,0x46,0x29],
    0x5b => [0xf860,0x47,0x29], 0x5c => [0xf860,0x48,0x29],
    0x5d => [0xf860,0x49,0x29], 0x5e => [0xf860,0x4a,0x29],
    0x5f => [0xf860,0x4b,0x29], 0x60 => [0xf860,0x4c,0x29],
    0x61 => [0xf860,0x4d,0x29], 0x62 => [0xf860,0x4e,0x29],
    0x63 => [0xf860,0x4f,0x29], 0x64 => [0xf860,0x50,0x29],
    0x65 => [0xf860,0x51,0x29], 0x66 => [0xf860,0x52,0x29],
    0x67 => [0xf860,0x53,0x29], 0x68 => [0xf860,0x54,0x29],
    0x69 => [0xf860,0x55,0x29], 0x6a => [0xf860,0x56,0x29],
    0x6b => [0xf860,0x57,0x29], 0x6c => [0xf860,0x58,0x29],
    0x6d => [0xf860,0x59,0x29], 0x6e => [0xf860,0x5a,0x29],
    0x6f => [0xf860,0x61,0x29], 0x70 => [0xf860,0x62,0x29],
    0x71 => [0xf860,0x63,0x29], 0x72 => [0xf860,0x64,0x29],
    0x73 => [0xf860,0x65,0x29], 0x74 => [0xf860,0x66,0x29],
    0x75 => [0xf860,0x67,0x29], 0x76 => [0xf860,0x68,0x29],
    0x77 => [0xf860,0x69,0x29], 0x78 => [0xf860,0x6a,0x29],
    0x79 => [0xf860,0x6b,0x29], 0x7a => [0xf860,0x6c,0x29],
    0x7b => [0xf860,0x6d,0x29], 0x7c => [0xf860,0x6e,0x29],
    0x7d => [0xf860,0x6f,0x29], 0x81 => [0xf860,0x70,0x29],
    0x82 => [0xf860,0x71,0x29], 0x83 => [0xf860,0x72,0x29],
    0x84 => [0xf860,0x73,0x29], 0x85 => [0xf860,0x74,0x29],
    0x86 => [0xf860,0x75,0x29], 0x87 => [0xf860,0x76,0x29],
    0x88 => [0xf860,0x77,0x29], 0x89 => [0xf860,0x78,0x29],
    0x8a => [0xf860,0x79,0x29], 0x8b => [0xf860,0x7a,0x29], 0xa1 => 0x2170,
    0xa2 => 0x2171, 0xa3 => 0x2172, 0xa4 => 0x2173, 0xa5 => 0x2174,
    0xa6 => 0x2175, 0xa7 => 0x2176, 0xa8 => 0x2177, 0xa9 => 0x2178,
    0xaa => 0x2179, 0xb0 => 0x2160, 0xb1 => 0x2161, 0xb2 => 0x2162,
    0xb3 => 0x2163, 0xb4 => 0x2164, 0xb5 => 0x2165, 0xb6 => 0x2166,
    0xb7 => 0x2167, 0xb8 => 0x2168, 0xb9 => 0x2169, 0xc1 => 0x0391,
    0xc2 => 0x0392, 0xc3 => 0x0393, 0xc4 => 0x0394, 0xc5 => 0x0395,
    0xc6 => 0x0396, 0xc7 => 0x0397, 0xc8 => 0x0398, 0xc9 => 0x0399,
    0xca => 0x039a, 0xcb => 0x039b, 0xcc => 0x039c, 0xcd => 0x039d,
    0xce => 0x039e, 0xcf => 0x039f, 0xd0 => 0x03a0, 0xd1 => 0x03a1,
    0xd2 => 0x03a3, 0xd3 => 0x03a4, 0xd4 => 0x03a5, 0xd5 => 0x03a6,
    0xd6 => 0x03a7, 0xd7 => 0x03a8, 0xd8 => 0x03a9, 0xda => [0xff01,0xf874],
    0xdb => [0x3002,0xf87d], 0xdc => [0x2032,0xf87f], 0xdd => [0x2033,0xf87f],
    0xde => 0x2034, 0xe1 => 0x03b1, 0xe2 => 0x03b2, 0xe3 => 0x03b3,
    0xe4 => 0x03b4, 0xe5 => 0x03b5, 0xe6 => 0x03b6, 0xe7 => 0x03b7,
    0xe8 => 0x03b8, 0xe9 => 0x03b9, 0xea => 0x03ba, 0xeb => 0x03bb,
    0xec => 0x03bc, 0xed => 0x03bd, 0xee => 0x03be, 0xef => 0x03bf,
    0xf0 => 0x03c0, 0xf1 => 0x03c1, 0xf2 => 0x03c3, 0xf3 => 0x03c4,
    0xf4 => 0x03c5, 0xf5 => 0x03c6, 0xf6 => 0x03c7, 0xf7 => 0x03c8,
    0xf8 => 0x03c9, 0xf9 => [0x3257,0xf87a], 0xfa => [0x3258,0xf87a],
    0xfb => [0x3259,0xf87a], 0xfc => [0x325a,0xf87a],
  },
  0xa6 => {
    0x41 => [0xf83d,0xf87f], 0x42 => 0xf83d, 0x43 => [0x2020,0xf87c],
    0x44 => [0xf860,0x2020,0x2020], 0x45 => [0xf860,0x2021,0x2021],
    0x46 => [0xf861,0x2020,0x2020,0x2020], 0x47 => [0xa7,0xf87c],
    0x48 => 0x266f, 0x49 => [0xff0a,0xf87f], 0x4a => [0xff0a,0xf873],
    0x4b => [0x2051,0xf874], 0x4c => [0xf860,0x2a,0x2a], 0x4d => 0x2042,
    0x4e => 0x204e, 0x4f => [0x2051,0xf871], 0x50 => [0xf867,0x2a,0x2a],
    0x51 => [0x2042,0xf879], 0x52 => 0x273d, 0x53 => 0x2731, 0x54 => 0x2747,
    0x55 => 0x2022, 0x56 => [0x25a0,0x20df], 0x57 => [0x25c7,0x20df],
    0x58 => 0xf805, 0x59 => [0x25a1,0x20df], 0x5a => 0x2039, 0x5b => 0x203a,
    0x5c => 0xab, 0x5d => 0xbb, 0x5e => [0x261c,0xf87f],
    0x5f => [0x261e,0xf87f], 0x60 => [0xf806,0x20df],
    0x61 => [0x25c7,0x20df,0x20df], 0x62 => [0x25c7,0x20de], 0x63 => 0xf806,
    0x64 => 0x29c8, 0x65 => [0x25c6,0x20de], 0x66 => [0xf805,0x20de],
    0x67 => [0x29c8,0x20de], 0x68 => 0x29be, 0x69 => [0x25ce,0x20dd],
    0x6a => [0x25b3,0x20dd], 0x6b => [0x25b2,0x20dd], 0x6c => 0x271a,
    0x6d => 0x2716, 0x6e => 0x29bf, 0x6f => 0x25ef, 0x70 => [0x25ef,0xf87c],
    0x71 => [0x2610,0xf87c], 0x72 => 0x2723, 0x73 => 0x2756, 0x74 => 0xf80a,
    0x75 => 0x25cc, 0x76 => [0x2610,0xf87f], 0x77 => 0x2610, 0x78 => 0x25a2,
    0x79 => [0x2723,0xf87a], 0x7a => [0x2756,0xf87a], 0x7b => [0x273f,0xf87a],
    0x7c => 0x273f, 0x7d => [0x3013,0xf87c], 0x81 => 0xf809,
    0x82 => [0x25c9,0x20dd], 0x83 => 0x274d, 0x84 => 0x25cd,
    0x85 => [0x27e1,0x20dd], 0x86 => [0xf80b,0xf87f], 0x87 => [0x2720,0xf87a],
    0x88 => 0x2720, 0x89 => [0x25c8,0xf87f], 0x8a => [0x25a8,0xf87f],
    0x8d => 0x2741, 0x8e => [0x2756,0xf87f], 0x8f => 0xf808,
    0x90 => [0x20a9,0xf87f], 0x91 => [0xf809,0xf87a], 0x92 => [0x534d,0xf87f],
    0x93 => 0x262f, 0x96 => 0xf80b, 0x97 => [0x262f,0xf87a],
    0x98 => [0x262f,0xf876], 0x99 => 0x2740, 0x9a => 0xf80c,
    0x9b => [0x2748,0x20d8], 0x9e => 0x3020, 0x9f => 0xf807, 0xa1 => 0x2500,
    0xa2 => 0x2502, 0xa3 => 0x250c, 0xa4 => 0x2510, 0xa5 => 0x2518,
    0xa6 => 0x2514, 0xa7 => 0x251c, 0xa8 => 0x252c, 0xa9 => 0x2524,
    0xaa => 0x2534, 0xab => 0x253c, 0xac => 0x2501, 0xad => 0x2503,
    0xae => 0x250f, 0xaf => 0x2513, 0xb0 => 0x251b, 0xb1 => 0x2517,
    0xb2 => 0x2523, 0xb3 => 0x2533, 0xb4 => 0x252b, 0xb5 => 0x253b,
    0xb6 => 0x254b, 0xb7 => 0x2520, 0xb8 => 0x252f, 0xb9 => 0x2528,
    0xba => 0x2537, 0xbb => 0x253f, 0xbc => 0x251d, 0xbd => 0x2530,
    0xbe => 0x2525, 0xbf => 0x2538, 0xc0 => 0x2542, 0xc1 => 0x2512,
    0xc2 => 0x2511, 0xc3 => 0x251a, 0xc4 => 0x2519, 0xc5 => 0x2516,
    0xc6 => 0x2515, 0xc7 => 0x250e, 0xc8 => 0x250d, 0xc9 => 0x251e,
    0xca => 0x251f, 0xcb => 0x2521, 0xcc => 0x2522, 0xcd => 0x2526,
    0xce => 0x2527, 0xcf => 0x2529, 0xd0 => 0x252a, 0xd1 => 0x252d,
    0xd2 => 0x252e, 0xd3 => 0x2531, 0xd4 => 0x2532, 0xd5 => 0x2535,
    0xd6 => 0x2536, 0xd7 => 0x2539, 0xd8 => 0x253a, 0xd9 => 0x253d,
    0xda => 0x253e, 0xdb => 0x2540, 0xdc => 0x2541, 0xdd => 0x2543,
    0xde => 0x2544, 0xdf => 0x2545, 0xe0 => 0x2546, 0xe1 => 0x2547,
    0xe2 => 0x2548, 0xe3 => 0x2549, 0xe4 => 0x254a, 0xe5 => 0x2776,
    0xe6 => 0x2777, 0xe7 => 0x2778, 0xe8 => 0x2779, 0xe9 => 0x277a,
    0xea => 0x277b, 0xeb => 0x277c, 0xec => 0x277d, 0xed => 0x277e,
    0xee => 0x277f, 0xef => 0x24eb, 0xf0 => 0x24ec, 0xf1 => 0x24ed,
    0xf2 => 0x24ee, 0xf3 => 0x24ef, 0xf4 => 0x24f0, 0xf5 => 0x24f1,
    0xf6 => 0x24f2, 0xf7 => 0x24f3, 0xf8 => 0x24f4, 0xf9 => [0x3251,0xf87a],
    0xfa => [0x3252,0xf87a], 0xfb => [0x3253,0xf87a], 0xfc => [0x3254,0xf87a],
    0xfd => [0x3255,0xf87a], 0xfe => [0x3256,0xf87a],
  },
  0xa7 => {
    0x41 => [0x2642,0xf87f], 0x42 => 0x3012, 0x43 => 0x3036,
    0x44 => [0x25cb,0xf87f], 0x45 => [0x25b3,0xf87f], 0x46 => 0x25fb,
    0x47 => 0xf84c, 0x48 => [0x2394,0xf876], 0x49 => [0x25ad,0xf878],
    0x4a => 0x25ad, 0x4b => 0xf84d, 0x4c => 0xf84e, 0x4d => 0xf84f,
    0x4e => [0x25c7,0xf87f], 0x4f => [0x51f9,0xf87f], 0x50 => [0x51f8,0xf87f],
    0x51 => 0x2206, 0x52 => [0x2206,0xf87f], 0x53 => 0x221f,
    0x54 => [0x222a,0xf87f], 0x55 => 0x2225, 0x56 => 0x2226,
    0x57 => [0x2229,0xf87f], 0x58 => 0x2253, 0x59 => 0x2251, 0x5a => 0x2266,
    0x5b => 0x2267, 0x5c => 0x2213, 0x5d => 0x2295, 0x5e => 0x2296,
    0x5f => 0x2297, 0x60 => 0x2a38, 0x61 => 0x2314, 0x62 => [0x3d,0x20e5],
    0x63 => [0x2261,0x20e5], 0x64 => 0x2262, 0x65 => [0x3d,0x20d2],
    0x66 => 0x25b1, 0x67 => [0x2d,0x0308], 0x68 => 0x2222, 0x69 => 0x2250,
    0x6a => 0x03d5, 0x6b => 0x2ae8, 0x6c => 0x22a3, 0x6d => [0x22a5,0x0338],
    0x6e => [0x2261,0x20d2], 0x6f => 0x226e, 0x70 => 0x226f, 0x71 => 0x2285,
    0x72 => 0x2284, 0x73 => 0x2209, 0x74 => 0x220c, 0x75 => 0x22bb,
    0x76 => 0x22bc, 0x77 => 0x225a, 0x78 => 0x2306, 0x79 => [0x223d,0x0336],
    0x7a => [0x2314,0xf87f], 0x7b => 0x2a72, 0x7c => [0x88dc,0x20e4],
    0x7d => [0xf862,0xc8fc,0xc2dd,0xd68c,0xc0ac],
    0x81 => [0xf863,0xc8fc,0xc2dd,0xd68c,0xc0ac], 0x82 => 0x329e,
    0x83 => [0x329e,0xf87f], 0x84 => 0x203c, 0x85 => 0x2049,
    0x86 => [0x203c,0xf87f], 0x87 => 0x2047, 0x88 => [0x25c7,0xf87c],
    0x89 => [0x25c7,0xf879], 0x8a => [0x25c7,0xf87b], 0x8b => [0x25c6,0xf879],
    0x8c => [0x25a1,0xf87c], 0x8d => [0x25a1,0xf879], 0x8e => [0x25a1,0xf87b],
    0x8f => 0x2588, 0x90 => 0x25e6, 0x91 => [0x25cb,0xf879],
    0x92 => [0x25cb,0xf87b], 0x93 => [0x25cf,0xf879], 0x94 => 0x25bf,
    0x95 => 0x25b5, 0x96 => 0x25b9, 0x97 => 0x25c3, 0x98 => 0x2666,
    0x99 => 0x2981, 0x9a => 0x25fc, 0x9b => [0x25b4,0x20e4], 0x9c => 0x25ca,
    0x9d => 0x3231, 0x9e => 0x3239, 0x9f => 0x33cb, 0xa1 => 0x3395,
    0xa2 => 0x3396, 0xa3 => 0x3397, 0xa4 => 0x2113, 0xa5 => 0x3398,
    0xa6 => 0x33c4, 0xa7 => 0x33a3, 0xa8 => 0x33a4, 0xa9 => 0x33a5,
    0xaa => 0x33a6, 0xab => 0x3399, 0xac => 0x339a, 0xad => 0x339b,
    0xae => 0x339c, 0xaf => 0x339d, 0xb0 => 0x339e, 0xb1 => 0x339f,
    0xb2 => 0x33a0, 0xb3 => 0x33a1, 0xb4 => 0x33a2, 0xb5 => 0x33ca,
    0xb6 => 0x338d, 0xb7 => 0x338e, 0xb8 => 0x338f, 0xb9 => 0x33cf,
    0xba => 0x3388, 0xbb => 0x3389, 0xbc => 0x33c8, 0xbd => 0x33a7,
    0xbe => 0x33a8, 0xbf => 0x33b0, 0xc0 => 0x33b1, 0xc1 => 0x33b2,
    0xc2 => 0x33b3, 0xc3 => 0x33b4, 0xc4 => 0x33b5, 0xc5 => 0x33b6,
    0xc6 => 0x33b7, 0xc7 => 0x33b8, 0xc8 => 0x33b9, 0xc9 => 0x3380,
    0xca => 0x3381, 0xcb => 0x3382, 0xcc => 0x3383, 0xcd => 0x3384,
    0xce => 0x33ba, 0xcf => 0x33bb, 0xd0 => 0x33bc, 0xd1 => 0x33bd,
    0xd2 => 0x33be, 0xd3 => 0x33bf, 0xd4 => 0x3390, 0xd5 => 0x3391,
    0xd6 => 0x3392, 0xd7 => 0x3393, 0xd8 => 0x3394, 0xd9 => 0x2126,
    0xda => 0x33c0, 0xdb => 0x33c1, 0xdc => 0x338a, 0xdd => 0x338b,
    0xde => 0x338c, 0xdf => 0x33d6, 0xe0 => 0x33c5, 0xe1 => 0x33ad,
    0xe2 => 0x33ae, 0xe3 => 0x33af, 0xe4 => 0x33db, 0xe5 => 0x33a9,
    0xe6 => 0x33aa, 0xe7 => 0x33ab, 0xe8 => 0x33ac, 0xe9 => 0x33dd,
    0xea => 0x33d0, 0xeb => 0x33d3, 0xec => 0x33c3, 0xed => 0x33c9,
    0xee => 0x33dc, 0xef => 0x33c6, 0xf0 => 0x246f, 0xf1 => 0x2470,
    0xf2 => 0x2471, 0xf3 => 0x2472, 0xf4 => 0x2473, 0xf5 => 0x3251,
    0xf6 => 0x3252, 0xf7 => 0x3253, 0xf8 => 0x3254, 0xf9 => 0x3255,
    0xfa => 0x3256, 0xfb => 0x3257, 0xfc => 0x3258, 0xfd => 0x3259,
    0xfe => 0x325a,
  },
  0xa8 => {
    0x41 => [0x2192,0xf87b], 0x42 => [0x2190,0xf87b], 0x43 => [0x2191,0xf87b],
    0x44 => [0x2193,0xf87b], 0x45 => [0x2196,0xf87b], 0x46 => [0x2197,0xf87b],
    0x47 => [0x2198,0xf87b], 0x48 => [0x2199,0xf87b], 0x49 => 0x21d0,
    0x4a => 0x21cf, 0x4b => 0x21cd, 0x4c => [0x21d4,0xf87f],
    0x4d => [0x2192,0xf87c], 0x4e => [0x2190,0xf87c], 0x4f => [0x2191,0xf87c],
    0x50 => [0x2193,0xf87c], 0x51 => [0x2194,0xf87c], 0x52 => [0x2195,0xf87c],
    0x53 => [0x2190,0xf879], 0x54 => [0x2192,0xf879], 0x55 => [0x2191,0xf879],
    0x56 => [0x2193,0xf879], 0x57 => [0x21e6,0x20de], 0x58 => [0x21e8,0x20de],
    0x59 => [0x21e7,0x20de], 0x5a => [0x21e9,0x20de], 0x5b => [0x21e6,0x20dd],
    0x5c => 0x27b2, 0x5d => [0x21e7,0x20dd], 0x5e => [0x21e9,0x20dd],
    0x5f => [0x2190,0xf87f], 0x60 => 0x279c, 0x61 => [0x2191,0xf87f],
    0x62 => [0x2193,0xf87f], 0x63 => [0x2190,0xf875], 0x64 => [0x2192,0xf875],
    0x65 => [0x2191,0xf875], 0x66 => [0x2193,0xf875], 0x67 => 0xf846,
    0x68 => 0xf847, 0x69 => [0x2190,0xf871], 0x6a => 0x279b,
    0x6b => [0x2190,0xf872], 0x6c => [0x2192,0xf872], 0x6d => [0x2191,0xf872],
    0x6e => [0x2193,0xf872], 0x6f => 0x2962, 0x70 => 0x2964, 0x71 => 0x2963,
    0x72 => 0x2965, 0x73 => [0x21e6,0xf87a], 0x74 => 0x27a1,
    0x75 => [0x21e7,0xf87a], 0x76 => [0x21e9,0xf87a], 0x77 => [0x21e6,0xf87b],
    0x78 => 0x279e, 0x79 => [0x21e7,0xf87b], 0x7a => [0x21e9,0xf87b],
    0x7b => 0x21b2, 0x7c => 0x21b1, 0x7d => [0x21bb,0xf87b], 0x81 => 0x21b4,
    0x82 => 0x21b0, 0x83 => 0x21b3, 0x84 => [0x2939,0xf87f],
    0x85 => [0x2934,0xf87f], 0x86 => 0x2936, 0x87 => [0x21b1,0xf87f],
    0x88 => [0x21bb,0xf87f], 0x89 => 0x2935, 0x8a => [0x21b0,0xf87f],
    0x8b => 0x2937, 0x8c => 0x2939, 0x8d => 0x2934, 0x8e => [0x21e6,0xf879],
    0x8f => [0x21e8,0xf879], 0x90 => [0x21e7,0xf879], 0x91 => [0x21e9,0xf879],
    0x92 => 0x21bc, 0x93 => 0x21c0, 0x94 => 0xf841, 0x95 => [0x21d4,0xf879],
    0x96 => [0x21e8,0xf874], 0x97 => [0x21e6,0xf874], 0x98 => [0x21c0,0xf879],
    0x99 => [0x21bc,0xf879], 0x9a => [0x21d2,0xf87c], 0x9b => [0x21d0,0xf87c],
    0x9c => 0xf849, 0x9d => 0xf848, 0x9e => 0x21c4, 0x9f => 0x21c5,
    0xa1 => 0xc6, 0xa2 => 0xd0, 0xa3 => 0xaa, 0xa4 => 0x0126, 0xa6 => 0x0132,
    0xa8 => 0x013f, 0xa9 => 0x0141, 0xaa => 0xd8, 0xab => 0x0152, 0xac => 0xba,
    0xad => 0xde, 0xae => 0x0166, 0xaf => 0x014a, 0xb1 => 0x3260,
    0xb2 => 0x3261, 0xb3 => 0x3262, 0xb4 => 0x3263, 0xb5 => 0x3264,
    0xb6 => 0x3265, 0xb7 => 0x3266, 0xb8 => 0x3267, 0xb9 => 0x3268,
    0xba => 0x3269, 0xbb => 0x326a, 0xbc => 0x326b, 0xbd => 0x326c,
    0xbe => 0x326d, 0xbf => 0x326e, 0xc0 => 0x326f, 0xc1 => 0x3270,
    0xc2 => 0x3271, 0xc3 => 0x3272, 0xc4 => 0x3273, 0xc5 => 0x3274,
    0xc6 => 0x3275, 0xc7 => 0x3276, 0xc8 => 0x3277, 0xc9 => 0x3278,
    0xca => 0x3279, 0xcb => 0x327a, 0xcc => 0x327b, 0xcd => 0x24d0,
    0xce => 0x24d1, 0xcf => 0x24d2, 0xd0 => 0x24d3, 0xd1 => 0x24d4,
    0xd2 => 0x24d5, 0xd3 => 0x24d6, 0xd4 => 0x24d7, 0xd5 => 0x24d8,
    0xd6 => 0x24d9, 0xd7 => 0x24da, 0xd8 => 0x24db, 0xd9 => 0x24dc,
    0xda => 0x24dd, 0xdb => 0x24de, 0xdc => 0x24df, 0xdd => 0x24e0,
    0xde => 0x24e1, 0xdf => 0x24e2, 0xe0 => 0x24e3, 0xe1 => 0x24e4,
    0xe2 => 0x24e5, 0xe3 => 0x24e6, 0xe4 => 0x24e7, 0xe5 => 0x24e8,
    0xe6 => 0x24e9, 0xe7 => 0x2460, 0xe8 => 0x2461, 0xe9 => 0x2462,
    0xea => 0x2463, 0xeb => 0x2464, 0xec => 0x2465, 0xed => 0x2466,
    0xee => 0x2467, 0xef => 0x2468, 0xf0 => 0x2469, 0xf1 => 0x246a,
    0xf2 => 0x246b, 0xf3 => 0x246c, 0xf4 => 0x246d, 0xf5 => 0x246e,
    0xf6 => 0xbd, 0xf7 => 0x2153, 0xf8 => 0x2154, 0xf9 => 0xbc, 0xfa => 0xbe,
    0xfb => 0x215b, 0xfc => 0x215c, 0xfd => 0x215d, 0xfe => 0x215e,
  },
  0xa9 => {
    0x41 => [0xf860,0x41,0x2e], 0x42 => [0xf860,0x42,0x2e],
    0x43 => [0xf860,0x43,0x2e], 0x44 => [0xf860,0x44,0x2e],
    0x45 => [0xf860,0x45,0x2e], 0x46 => [0xf860,0x46,0x2e],
    0x47 => [0xf860,0x47,0x2e], 0x48 => [0xf860,0x48,0x2e],
    0x49 => [0xf860,0x49,0x2e], 0x4a => [0xf860,0x4a,0x2e],
    0x4b => [0xf860,0x4b,0x2e], 0x4c => [0xf860,0x4c,0x2e],
    0x4d => [0xf860,0x4d,0x2e], 0x4e => [0xf860,0x4e,0x2e],
    0x4f => [0xf860,0x4f,0x2e], 0x50 => [0xf860,0x50,0x2e],
    0x51 => [0xf860,0x51,0x2e], 0x52 => [0xf860,0x52,0x2e],
    0x53 => [0xf860,0x53,0x2e], 0x54 => [0xf860,0x54,0x2e],
    0x55 => [0xf860,0x55,0x2e], 0x56 => [0xf860,0x56,0x2e],
    0x57 => [0xf860,0x57,0x2e], 0x58 => [0xf860,0x58,0x2e],
    0x59 => [0xf860,0x59,0x2e], 0x5a => [0xf860,0x5a,0x2e],
    0x5b => [0xf860,0x61,0x2e], 0x5c => [0xf860,0x62,0x2e],
    0x5d => [0xf860,0x63,0x2e], 0x5e => [0xf860,0x64,0x2e],
    0x5f => [0xf860,0x65,0x2e], 0x60 => [0xf860,0x66,0x2e],
    0x61 => [0xf860,0x67,0x2e], 0x62 => [0xf860,0x68,0x2e],
    0x63 => [0xf860,0x69,0x2e], 0x64 => [0xf860,0x6a,0x2e],
    0x65 => [0xf860,0x6b,0x2e], 0x66 => [0xf860,0x6c,0x2e],
    0x67 => [0xf860,0x6d,0x2e], 0x68 => [0xf860,0x6e,0x2e],
    0x69 => [0xf860,0x6f,0x2e], 0x6a => [0xf860,0x70,0x2e],
    0x6b => [0xf860,0x71,0x2e], 0x6c => [0xf860,0x72,0x2e],
    0x6d => [0xf860,0x73,0x2e], 0x6e => [0xf860,0x74,0x2e],
    0x6f => [0xf860,0x75,0x2e], 0x70 => [0xf860,0x76,0x2e],
    0x71 => [0xf860,0x77,0x2e], 0x72 => [0xf860,0x78,0x2e],
    0x73 => [0xf860,0x79,0x2e], 0x74 => [0xf860,0x7a,0x2e], 0xa1 => 0xe6,
    0xa2 => 0x0111, 0xa3 => 0xf0, 0xa4 => 0x0127, 0xa5 => 0x0131,
    0xa6 => 0x0133, 0xa7 => 0x0138, 0xa8 => 0x0140, 0xa9 => 0x0142,
    0xaa => 0xf8, 0xab => 0x0153, 0xac => 0xdf, 0xad => 0xfe, 0xae => 0x0167,
    0xaf => 0x014b, 0xb0 => 0x0149, 0xb1 => 0x3200, 0xb2 => 0x3201,
    0xb3 => 0x3202, 0xb4 => 0x3203, 0xb5 => 0x3204, 0xb6 => 0x3205,
    0xb7 => 0x3206, 0xb8 => 0x3207, 0xb9 => 0x3208, 0xba => 0x3209,
    0xbb => 0x320a, 0xbc => 0x320b, 0xbd => 0x320c, 0xbe => 0x320d,
    0xbf => 0x320e, 0xc0 => 0x320f, 0xc1 => 0x3210, 0xc2 => 0x3211,
    0xc3 => 0x3212, 0xc4 => 0x3213, 0xc5 => 0x3214, 0xc6 => 0x3215,
    0xc7 => 0x3216, 0xc8 => 0x3217, 0xc9 => 0x3218, 0xca => 0x3219,
    0xcb => 0x321a, 0xcc => 0x321b, 0xcd => 0x249c, 0xce => 0x249d,
    0xcf => 0x249e, 0xd0 => 0x249f, 0xd1 => 0x24a0, 0xd2 => 0x24a1,
    0xd3 => 0x24a2, 0xd4 => 0x24a3, 0xd5 => 0x24a4, 0xd6 => 0x24a5,
    0xd7 => 0x24a6, 0xd8 => 0x24a7, 0xd9 => 0x24a8, 0xda => 0x24a9,
    0xdb => 0x24aa, 0xdc => 0x24ab, 0xdd => 0x24ac, 0xde => 0x24ad,
    0xdf => 0x24ae, 0xe0 => 0x24af, 0xe1 => 0x24b0, 0xe2 => 0x24b1,
    0xe3 => 0x24b2, 0xe4 => 0x24b3, 0xe5 => 0x24b4, 0xe6 => 0x24b5,
    0xe7 => 0x2474, 0xe8 => 0x2475, 0xe9 => 0x2476, 0xea => 0x2477,
    0xeb => 0x2478, 0xec => 0x2479, 0xed => 0x247a, 0xee => 0x247b,
    0xef => 0x247c, 0xf0 => 0x247d, 0xf1 => 0x247e, 0xf2 => 0x247f,
    0xf3 => 0x2480, 0xf4 => 0x2481, 0xf5 => 0x2482, 0xf6 => 0xb9, 0xf7 => 0xb2,
    0xf8 => 0xb3, 0xf9 => 0x2074, 0xfa => 0x207f, 0xfb => 0x2081,
    0xfc => 0x2082, 0xfd => 0x2083, 0xfe => 0x2084,
  },
  0xaa => {
    0x41 => [0xc6b4,0x20de], 0x42 => [0xb2f5,0x20de], 0x43 => [0xc8fc,0x20de],
    0x44 => [0xba85,0x20de], 0x45 => [0xb300,0x20de], 0x46 => [0xd615,0x20de],
    0x47 => [0xbd80,0x20de], 0x48 => [0xc804,0x20de], 0x49 => [0xc811,0x20de],
    0x4a => [0xc218,0x20de], 0x4b => [0xb3d9,0x20de], 0x4c => [0xbe44,0x20de],
    0x4d => [0xbc18,0x20de], 0x4e => [0xc790,0x20de], 0x4f => [0xd0c0,0x20de],
    0x50 => [0xac10,0x20de], 0x51 => [0xc57d,0x20de], 0x52 => [0xc778,0x20de],
    0x53 => [0xb73b,0x20de], 0x54 => [0x5370,0x20de], 0x55 => [0x8a3b,0x20de],
    0x56 => [0xc608,0x20de], 0x57 => [0x611f,0x20de], 0x58 => [0x51a0,0x20de],
    0x59 => [0x7b54,0x20de], 0x5a => [0x4ee3,0x20de], 0x5b => [0x982d,0x20de],
    0x5c => [0x52d5,0x20de], 0x5d => [0x540d,0x20de], 0x5e => [0x76ee,0x20de],
    0x5f => [0x53cd,0x20de], 0x60 => [0x88dc,0x20de], 0x61 => [0x672c,0x20de],
    0x62 => [0x526f,0x20de], 0x63 => [0x5e8f,0x20de], 0x64 => [0x9023,0x20de],
    0x65 => [0x5f71,0x20de], 0x66 => [0x4f8b,0x20de], 0x67 => [0x6e90,0x20de],
    0x68 => [0x5b50,0x20de], 0x69 => [0x524d,0x20de], 0x6a => [0x7bc0,0x20de],
    0x6b => [0x63a5,0x20de], 0x6c => [0x52a9,0x20de], 0x6d => [0x6307,0x20de],
    0x6e => [0x4ed6,0x20de], 0x6f => [0x6d3e,0x20de], 0x70 => [0x5f62,0x20de],
    0x71 => [0xc870,0x20de], 0x72 => [0xbb38,0x20de,0xf87a],
    0x73 => [0xb2f5,0x20de,0xf87a], 0x74 => [0xc8fc,0x20de,0xf87a],
    0x75 => [0xb73b,0x20de,0xf87a], 0x76 => [0x8a3b,0x20de,0xf87a],
    0x77 => [0xad50,0x20de,0xf87a], 0x78 => [0xc5ed,0x20de,0xf87a],
    0x79 => [0xc74c,0x20de,0xf87a], 0x7a => [0xc815,0x20de,0xf87a],
    0x7b => [0xd574,0x20de,0xf87a], 0x7c => [0xc608,0x20de,0xf87a],
    0x7d => [0xc874,0x20dd], 0x81 => [0xb77c,0x20dd], 0x82 => [0xb9c8,0x20dd],
    0x83 => [0xbc14,0x20dd], 0x84 => [0xc0ac,0x20dd], 0x85 => [0xc544,0x20dd],
    0x86 => [0xc790,0x20dd], 0x87 => [0xcc28,0x20dd], 0x88 => [0xce74,0x20dd],
    0x89 => [0xd0c0,0x20dd], 0x8a => [0xd30c,0x20dd], 0x8b => [0xb192,0x20dd],
    0x8c => [0xb0ae,0x20dd], 0x8d => [0xba85,0x20dd], 0x8e => [0xb300,0x20dd],
    0x8f => [0xd615,0x20dd], 0x90 => [0xbd80,0x20dd], 0x91 => [0xc804,0x20dd],
    0x92 => [0xc811,0x20dd], 0x93 => [0xc218,0x20dd], 0x94 => [0xb3d9,0x20dd],
    0x95 => [0xbe44,0x20dd], 0x96 => [0xac8c,0x20dd], 0x97 => [0xbc18,0x20dd],
    0x98 => [0xc18d,0x20dd], 0x99 => [0xc778,0x20dd], 0x9a => [0xbcf8,0x20dd],
    0x9b => [0xc57d,0x20dd], 0x9c => [0xc219,0x20dd], 0x9d => [0xc720,0x20dd],
    0x9e => [0xad00,0x20dd], 0x9f => [0x51a0,0x20dd], 0xa1 => 0x3041,
    0xa2 => 0x3042, 0xa3 => 0x3043, 0xa4 => 0x3044, 0xa5 => 0x3045,
    0xa6 => 0x3046, 0xa7 => 0x3047, 0xa8 => 0x3048, 0xa9 => 0x3049,
    0xaa => 0x304a, 0xab => 0x304b, 0xac => 0x304c, 0xad => 0x304d,
    0xae => 0x304e, 0xaf => 0x304f, 0xb0 => 0x3050, 0xb1 => 0x3051,
    0xb2 => 0x3052, 0xb3 => 0x3053, 0xb4 => 0x3054, 0xb5 => 0x3055,
    0xb6 => 0x3056, 0xb7 => 0x3057, 0xb8 => 0x3058, 0xb9 => 0x3059,
    0xba => 0x305a, 0xbb => 0x305b, 0xbc => 0x305c, 0xbd => 0x305d,
    0xbe => 0x305e, 0xbf => 0x305f, 0xc0 => 0x3060, 0xc1 => 0x3061,
    0xc2 => 0x3062, 0xc3 => 0x3063, 0xc4 => 0x3064, 0xc5 => 0x3065,
    0xc6 => 0x3066, 0xc7 => 0x3067, 0xc8 => 0x3068, 0xc9 => 0x3069,
    0xca => 0x306a, 0xcb => 0x306b, 0xcc => 0x306c, 0xcd => 0x306d,
    0xce => 0x306e, 0xcf => 0x306f, 0xd0 => 0x3070, 0xd1 => 0x3071,
    0xd2 => 0x3072, 0xd3 => 0x3073, 0xd4 => 0x3074, 0xd5 => 0x3075,
    0xd6 => 0x3076, 0xd7 => 0x3077, 0xd8 => 0x3078, 0xd9 => 0x3079,
    0xda => 0x307a, 0xdb => 0x307b, 0xdc => 0x307c, 0xdd => 0x307d,
    0xde => 0x307e, 0xdf => 0x307f, 0xe0 => 0x3080, 0xe1 => 0x3081,
    0xe2 => 0x3082, 0xe3 => 0x3083, 0xe4 => 0x3084, 0xe5 => 0x3085,
    0xe6 => 0x3086, 0xe7 => 0x3087, 0xe8 => 0x3088, 0xe9 => 0x3089,
    0xea => 0x308a, 0xeb => 0x308b, 0xec => 0x308c, 0xed => 0x308d,
    0xee => 0x308e, 0xef => 0x308f, 0xf0 => 0x3090, 0xf1 => 0x3091,
    0xf2 => 0x3092, 0xf3 => 0x3093, 0xf4 => 0x2483, 0xf5 => 0x2484,
    0xf6 => 0x2485, 0xf7 => 0x2486, 0xf8 => 0x2487,
    0xf9 => [0xf862,0x28,0x32,0x31,0x29], 0xfa => [0xf862,0x28,0x32,0x32,0x29],
    0xfb => [0xf862,0x28,0x32,0x33,0x29], 0xfc => [0xf862,0x28,0x32,0x34,0x29],
    0xfd => [0xf862,0x28,0x32,0x35,0x29], 0xfe => [0xf862,0x28,0x32,0x36,0x29],
  },
  0xab => {
    0x41 => [0xc870,0x20dd], 0x42 => [0xad6d,0x20dd], 0x43 => [0xac10,0x20dd],
    0x44 => [0x5370,0x20dd], 0x45 => [0x8863,0x20dd], 0x46 => [0x672b,0x20dd],
    0x47 => [0xac70,0x20dd], 0x48 => [0xb2f5,0x20dd], 0x49 => [0xbcc0,0x20dd],
    0x4a => [0xc0c1,0x20dd], 0x4b => [0xc13c,0x20dd], 0x4c => [0xc2e0,0x20dd],
    0x4d => [0xc5ec,0x20dd], 0x4e => [0xc608,0x20dd], 0x4f => [0xc6d0,0x20dd],
    0x50 => [0xc791,0x20dd], 0x51 => [0xc900,0x20dd], 0x52 => [0xd0b9,0x20dd],
    0x53 => [0xc678,0x20dd], 0x54 => [0xd65c,0x20dd], 0x55 => [0xac04,0x20dd],
    0x56 => [0xac19,0x20dd], 0x57 => [0xc2e4,0x20dd], 0x58 => [0x611f,0x20dd],
    0x59 => [0x6163,0x20dd], 0x5a => [0x4ee3,0x20dd], 0x5b => [0x52d5,0x20dd],
    0x5c => 0x3294, 0x5d => [0x53cd,0x20dd], 0x5e => [0x526f,0x20dd],
    0x5f => [0x81ea,0x20dd], 0x60 => [0x524d,0x20dd], 0x61 => [0x96fb,0x20dd],
    0x62 => [0x63a5,0x20dd], 0x63 => [0x52a9,0x20dd], 0x64 => [0x6ce8,0x20dd],
    0x65 => [0x53c3,0x20dd], 0x66 => [0x672c,0x20dd], 0x67 => [0x65b0,0x20dd],
    0x68 => [0x73fe,0x20dd], 0x69 => [0x5f62,0x20dd], 0x6a => [0x9593,0x20dd],
    0x6b => [0x570b,0x20dd], 0x6c => 0x32a5, 0x6d => [0x4ed6,0x20dd],
    0x6e => [0xbe60,0x20dd], 0x6f => [0xc2dc,0x20dd], 0x70 => [0xc785,0x20dd],
    0x71 => [0xc73c,0x20dd], 0x72 => [0xc74c,0x20dd], 0x73 => [0xc9c1,0x20dd],
    0x74 => [0xd45c,0x20dd], 0x75 => [0xac00,0x20dd], 0x76 => [0xb098,0x20dd],
    0x77 => [0xb2e4,0x20dd], 0x78 => [0xd558,0x20dd],
    0x79 => [0xb9c8,0x20dd,0xf87a], 0x7a => [0xbc14,0x20dd,0xf87a],
    0x7b => [0xc0ac,0x20dd,0xf87a], 0x7c => [0xc544,0x20dd,0xf87a],
    0x7d => [0xc790,0x20dd,0xf87a], 0x81 => [0xcc28,0x20dd,0xf87a],
    0x82 => [0xce74,0x20dd,0xf87a], 0x83 => [0xd0c0,0x20dd,0xf87a],
    0x84 => [0xd30c,0x20dd,0xf87a], 0x85 => [0xd558,0x20dd,0xf87a],
    0x86 => [0xbe44,0x20dd,0xf87a], 0x87 => [0xb2f5,0x20dd,0xf87a],
    0x88 => [0xbe60,0x20dd,0xf87a], 0x89 => [0xbcf8,0x20dd,0xf87a],
    0x8a => [0xb2e8,0x20dd,0xf87a], 0x8b => [0xc13c,0x20dd,0xf87a],
    0x8c => [0xc2dc,0x20dd,0xf87a], 0x8d => [0xc5ec,0x20dd,0xf87a],
    0x8e => [0xc608,0x20dd,0xf87a], 0x8f => [0xc73c,0x20dd,0xf87a],
    0x90 => [0xc74c,0x20dd,0xf87a], 0x91 => [0xc785,0x20dd,0xf87a],
    0x92 => [0xc81c,0x20dd,0xf87a], 0x93 => [0xc874,0x20dd,0xf87a],
    0x94 => [0xc900,0x20dd,0xf87a], 0x95 => [0xd45c,0x20dd,0xf87a],
    0x96 => [0xd574,0x20dd,0xf87a], 0x97 => [0xb290,0x20dd,0xf87a],
    0x98 => [0xb192,0x20dd,0xf87a], 0x99 => [0xb0ae,0x20dd,0xf87a],
    0x9a => [0xbc18,0x20dd,0xf87a], 0x9b => [0xac00,0x20dd,0xf87a],
    0x9c => [0xb098,0x20dd,0xf87a], 0x9d => [0xb2e4,0x20dd,0xf87a],
    0x9e => [0xb77c,0x20dd,0xf87a], 0x9f => [0xc678,0x20dd,0xf87a],
    0xa1 => 0x30a1, 0xa2 => 0x30a2, 0xa3 => 0x30a3, 0xa4 => 0x30a4,
    0xa5 => 0x30a5, 0xa6 => 0x30a6, 0xa7 => 0x30a7, 0xa8 => 0x30a8,
    0xa9 => 0x30a9, 0xaa => 0x30aa, 0xab => 0x30ab, 0xac => 0x30ac,
    0xad => 0x30ad, 0xae => 0x30ae, 0xaf => 0x30af, 0xb0 => 0x30b0,
    0xb1 => 0x30b1, 0xb2 => 0x30b2, 0xb3 => 0x30b3, 0xb4 => 0x30b4,
    0xb5 => 0x30b5, 0xb6 => 0x30b6, 0xb7 => 0x30b7, 0xb8 => 0x30b8,
    0xb9 => 0x30b9, 0xba => 0x30ba, 0xbb => 0x30bb, 0xbc => 0x30bc,
    0xbd => 0x30bd, 0xbe => 0x30be, 0xbf => 0x30bf, 0xc0 => 0x30c0,
    0xc1 => 0x30c1, 0xc2 => 0x30c2, 0xc3 => 0x30c3, 0xc4 => 0x30c4,
    0xc5 => 0x30c5, 0xc6 => 0x30c6, 0xc7 => 0x30c7, 0xc8 => 0x30c8,
    0xc9 => 0x30c9, 0xca => 0x30ca, 0xcb => 0x30cb, 0xcc => 0x30cc,
    0xcd => 0x30cd, 0xce => 0x30ce, 0xcf => 0x30cf, 0xd0 => 0x30d0,
    0xd1 => 0x30d1, 0xd2 => 0x30d2, 0xd3 => 0x30d3, 0xd4 => 0x30d4,
    0xd5 => 0x30d5, 0xd6 => 0x30d6, 0xd7 => 0x30d7, 0xd8 => 0x30d8,
    0xd9 => 0x30d9, 0xda => 0x30da, 0xdb => 0x30db, 0xdc => 0x30dc,
    0xdd => 0x30dd, 0xde => 0x30de, 0xdf => 0x30df, 0xe0 => 0x30e0,
    0xe1 => 0x30e1, 0xe2 => 0x30e2, 0xe3 => 0x30e3, 0xe4 => 0x30e4,
    0xe5 => 0x30e5, 0xe6 => 0x30e6, 0xe7 => 0x30e7, 0xe8 => 0x30e8,
    0xe9 => 0x30e9, 0xea => 0x30ea, 0xeb => 0x30eb, 0xec => 0x30ec,
    0xed => 0x30ed, 0xee => 0x30ee, 0xef => 0x30ef, 0xf0 => 0x30f0,
    0xf1 => 0x30f1, 0xf2 => 0x30f2, 0xf3 => 0x30f3, 0xf4 => 0x30f4,
    0xf5 => 0x30f5, 0xf6 => 0x30f6, 0xf7 => [0xf862,0x28,0x32,0x37,0x29],
    0xf8 => [0xf862,0x28,0x32,0x38,0x29], 0xf9 => [0xf862,0x28,0x32,0x39,0x29],
    0xfa => [0xf862,0x28,0x33,0x30,0x29],
  },
  0xac => {
    0x41 => 0x21f0, 0x42 => 0xf843, 0x43 => 0x27b5, 0x44 => [0x2964,0xf87f],
    0x45 => [0x2962,0xf87f], 0x46 => [0x21e8,0xf870], 0x47 => [0x21e6,0xf870],
    0x48 => 0x27a4, 0x49 => 0xf844, 0x4a => 0xf84b, 0x4b => 0xf84a,
    0x4c => [0x21c0,0xf87f], 0x4d => [0x21bc,0xf87f], 0x4e => [0x21e8,0xf87f],
    0x4f => [0x21e6,0xf87f], 0x50 => 0x21b6, 0x51 => 0x21b7, 0x52 => 0x219d,
    0x53 => 0x219c, 0x54 => 0xf842, 0x55 => [0x2190,0xf87a],
    0x56 => [0x2192,0xf87a], 0x57 => [0x2191,0xf87a], 0x58 => [0x2193,0xf87a],
    0x59 => [0x21e6,0xf87c], 0x5a => [0x21e8,0xf87c], 0x5b => [0x21e7,0xf87c],
    0x5c => [0x21e9,0xf87c], 0x5d => [0x2190,0xf873], 0x5e => 0x2794,
    0x5f => 0xf845, 0x60 => [0x2191,0xf873], 0x61 => [0x2193,0xf873],
    0x62 => [0x2190,0xf878], 0x63 => [0x2192,0xf878], 0x64 => [0x2191,0xf878],
    0x65 => [0x2193,0xf878], 0x66 => [0x2190,0xf874], 0x67 => [0x2192,0xf874],
    0x68 => [0x2191,0xf874], 0x69 => [0x2193,0xf874], 0x6a => 0x21e0,
    0x6b => 0x21e2, 0x6c => 0x21e1, 0x6d => 0x21e3, 0x6e => [0x21e6,0xf875],
    0x6f => [0x21e8,0xf875], 0x70 => [0x21e7,0xf875], 0x71 => [0x21e9,0xf875],
    0x72 => 0x21e6, 0x73 => 0x21e8, 0x74 => 0x21e7, 0x75 => 0x21e9,
    0x76 => [0x2936,0xf87a], 0x77 => [0x21b1,0xf87a], 0x78 => [0x21bb,0xf87a],
    0x79 => [0x2935,0xf87a], 0x7a => [0x21b0,0xf87a], 0x7b => [0x2937,0xf87a],
    0x7c => [0x2939,0xf87a], 0x7d => [0x2934,0xf87a], 0x81 => [0x2936,0xf87c],
    0x82 => [0x21b1,0xf87c], 0x83 => [0x21bb,0xf87c], 0x84 => [0x2935,0xf87c],
    0x85 => [0x21b0,0xf87c], 0x86 => [0x2937,0xf87c], 0x87 => [0x2939,0xf87c],
    0x88 => [0x2934,0xf87c], 0x89 => [0x2190,0xf870], 0x8a => [0x2192,0xf870],
    0x8b => [0x2191,0xf870], 0x8c => [0x2193,0xf870], 0x8d => 0x261d,
    0x8e => 0x261f, 0x8f => [0x261d,0xf87f], 0x90 => [0x261f,0xf87f],
    0x91 => [0xb2e8,0x20dd], 0x92 => [0xcc38,0x20dd], 0x93 => [0xc18c,0x20dd],
    0x94 => [0xc911,0x20dd], 0x95 => [0xc77c,0x20dd], 0x96 => [0xc774,0x20dd],
    0x97 => [0xd734,0x20dd], 0xa1 => 0x0410, 0xa2 => 0x0411, 0xa3 => 0x0412,
    0xa4 => 0x0413, 0xa5 => 0x0414, 0xa6 => 0x0415, 0xa7 => 0x0401,
    0xa8 => 0x0416, 0xa9 => 0x0417, 0xaa => 0x0418, 0xab => 0x0419,
    0xac => 0x041a, 0xad => 0x041b, 0xae => 0x041c, 0xaf => 0x041d,
    0xb0 => 0x041e, 0xb1 => 0x041f, 0xb2 => 0x0420, 0xb3 => 0x0421,
    0xb4 => 0x0422, 0xb5 => 0x0423, 0xb6 => 0x0424, 0xb7 => 0x0425,
    0xb8 => 0x0426, 0xb9 => 0x0427, 0xba => 0x0428, 0xbb => 0x0429,
    0xbc => 0x042a, 0xbd => 0x042b, 0xbe => 0x042c, 0xbf => 0x042d,
    0xc0 => 0x042e, 0xc1 => 0x042f, 0xc2 => [0x31,0x20de,0xf875],
    0xc3 => [0x32,0x20de,0xf875], 0xc4 => [0x33,0x20de,0xf875],
    0xc5 => [0x34,0x20de,0xf875], 0xc6 => [0x35,0x20de,0xf875],
    0xc7 => [0x36,0x20de,0xf875], 0xc8 => [0x37,0x20de,0xf875],
    0xc9 => [0x38,0x20de,0xf875], 0xca => [0x39,0x20de,0xf875],
    0xcb => [0xf866,0x5b,0x31,0x30,0x5d], 0xcc => [0xf866,0x5b,0x31,0x31,0x5d],
    0xcd => [0xf866,0x5b,0x31,0x32,0x5d], 0xce => [0xf866,0x5b,0x31,0x33,0x5d],
    0xcf => [0xf866,0x5b,0x31,0x34,0x5d], 0xd0 => [0xf866,0x5b,0x31,0x35,0x5d],
    0xd1 => 0x0430, 0xd2 => 0x0431, 0xd3 => 0x0432, 0xd4 => 0x0433,
    0xd5 => 0x0434, 0xd6 => 0x0435, 0xd7 => 0x0451, 0xd8 => 0x0436,
    0xd9 => 0x0437, 0xda => 0x0438, 0xdb => 0x0439, 0xdc => 0x043a,
    0xdd => 0x043b, 0xde => 0x043c, 0xdf => 0x043d, 0xe0 => 0x043e,
    0xe1 => 0x043f, 0xe2 => 0x0440, 0xe3 => 0x0441, 0xe4 => 0x0442,
    0xe5 => 0x0443, 0xe6 => 0x0444, 0xe7 => 0x0445, 0xe8 => 0x0446,
    0xe9 => 0x0447, 0xea => 0x0448, 0xeb => 0x0449, 0xec => 0x044a,
    0xed => 0x044b, 0xee => 0x044c, 0xef => 0x044d, 0xf0 => 0x044e,
    0xf1 => 0x044f, 0xf2 => [0xf866,0x5b,0x31,0x36,0x5d],
    0xf3 => [0xf866,0x5b,0x31,0x37,0x5d], 0xf4 => [0xf866,0x5b,0x31,0x38,0x5d],
    0xf5 => [0xf866,0x5b,0x31,0x39,0x5d], 0xf6 => [0xf866,0x5b,0x32,0x30,0x5d],
  },
  0xad => {
    0x41 => [0x4e00,0x20de,0xf87a], 0x42 => [0x4e8c,0x20de,0xf87a],
    0x43 => [0x4e09,0x20de,0xf87a], 0x44 => [0x56db,0x20de,0xf87a],
    0x45 => [0x4e94,0x20de,0xf87a], 0x46 => [0x516d,0x20de,0xf87a],
    0x47 => [0x4e03,0x20de,0xf87a], 0x48 => [0x516b,0x20de,0xf87a],
    0x49 => [0x4e5d,0x20de,0xf87a], 0x4a => [0x5341,0x20de,0xf87a],
    0x4b => [0xf863,0x5b,0x5341,0x4e00,0x5d],
    0x4c => [0xf863,0x5b,0x5341,0x4e8c,0x5d],
    0x4d => [0xf863,0x5b,0x5341,0x4e09,0x5d],
    0x4e => [0xf863,0x5b,0x5341,0x56db,0x5d],
    0x4f => [0xf863,0x5b,0x5341,0x4e94,0x5d],
    0x50 => [0xf863,0x5b,0x5341,0x516d,0x5d],
    0x51 => [0xf863,0x5b,0x5341,0x4e03,0x5d],
    0x52 => [0xf863,0x5b,0x5341,0x516b,0x5d],
    0x53 => [0xf863,0x5b,0x5341,0x4e5d,0x5d],
    0x54 => [0xf863,0x5b,0x4e8c,0x5341,0x5d], 0x55 => [0x4e00,0x20de],
    0x56 => [0x4e8c,0x20de], 0x57 => [0x4e09,0x20de], 0x58 => [0x56db,0x20de],
    0x59 => [0x4e94,0x20de], 0x5a => [0x516d,0x20de], 0x5b => [0x4e03,0x20de],
    0x5c => [0x516b,0x20de], 0x5d => [0x4e5d,0x20de], 0x5e => [0x5341,0x20de],
    0x5f => [0xf862,0x5b,0x5341,0x4e00,0x5d],
    0x60 => [0xf862,0x5b,0x5341,0x4e8c,0x5d],
    0x61 => [0xf862,0x5b,0x5341,0x4e09,0x5d],
    0x62 => [0xf862,0x5b,0x5341,0x56db,0x5d],
    0x63 => [0xf862,0x5b,0x5341,0x4e94,0x5d],
    0x64 => [0xf862,0x5b,0x5341,0x516d,0x5d],
    0x65 => [0xf862,0x5b,0x5341,0x4e03,0x5d],
    0x66 => [0xf862,0x5b,0x5341,0x516b,0x5d],
    0x67 => [0xf862,0x5b,0x5341,0x4e5d,0x5d],
    0x68 => [0xf862,0x5b,0x4e8c,0x5341,0x5d], 0x69 => [0x65e5,0x20de],
    0x6a => [0x6708,0x20de], 0x6b => [0x706b,0x20de], 0x6c => [0x6c34,0x20de],
    0x6d => [0x6728,0x20de], 0x6e => [0x91d1,0x20de], 0x6f => [0x571f,0x20de],
    0x70 => 0x3290, 0x71 => 0x328a, 0x72 => 0x328b, 0x73 => 0x328c,
    0x74 => 0x328d, 0x75 => 0x328e, 0x76 => 0x328f,
    0x77 => [0x65e5,0x20de,0xf87c], 0x78 => [0x6708,0x20de,0xf87c],
    0x79 => [0x706b,0x20de,0xf87c], 0x7a => [0x6c34,0x20de,0xf87c],
    0x7b => [0x6728,0x20de,0xf87c], 0x7c => [0x91d1,0x20de,0xf87c],
    0x7d => [0x571f,0x20de,0xf87c], 0xa1 => [0x300c,0xf879],
    0xa2 => [0x300d,0xf879], 0xa3 => [0x300e,0xf879], 0xa4 => [0x300f,0xf879],
    0xa5 => [0x21e8,0xf878], 0xa6 => [0x21e6,0xf878], 0xa7 => [0x21e7,0xf878],
    0xa8 => [0x21e9,0xf878], 0xa9 => 0x301e, 0xaa => 0x301f, 0xab => 0x2036,
    0xac => [0x2033,0xf873], 0xad => 0x2035, 0xae => [0x2032,0xf873],
    0xaf => [0x21e7,0xf87f], 0xb0 => [0x21,0xf87f],
  },
  0xb0 => {
    0xa1 => 0xac00, 0xa2 => 0xac01, 0xa3 => 0xac04, 0xa4 => 0xac07,
    0xa5 => 0xac08, 0xa6 => 0xac09, 0xa7 => 0xac0a, 0xa8 => 0xac10,
    0xa9 => 0xac11, 0xaa => 0xac12, 0xab => 0xac13, 0xac => 0xac14,
    0xad => 0xac15, 0xae => 0xac16, 0xaf => 0xac17, 0xb0 => 0xac19,
    0xb1 => 0xac1a, 0xb2 => 0xac1b, 0xb3 => 0xac1c, 0xb4 => 0xac1d,
    0xb5 => 0xac20, 0xb6 => 0xac24, 0xb7 => 0xac2c, 0xb8 => 0xac2d,
    0xb9 => 0xac2f, 0xba => 0xac30, 0xbb => 0xac31, 0xbc => 0xac38,
    0xbd => 0xac39, 0xbe => 0xac3c, 0xbf => 0xac40, 0xc0 => 0xac4b,
    0xc1 => 0xac4d, 0xc2 => 0xac54, 0xc3 => 0xac58, 0xc4 => 0xac5c,
    0xc5 => 0xac70, 0xc6 => 0xac71, 0xc7 => 0xac74, 0xc8 => 0xac77,
    0xc9 => 0xac78, 0xca => 0xac7a, 0xcb => 0xac80, 0xcc => 0xac81,
    0xcd => 0xac83, 0xce => 0xac84, 0xcf => 0xac85, 0xd0 => 0xac86,
    0xd1 => 0xac89, 0xd2 => 0xac8a, 0xd3 => 0xac8b, 0xd4 => 0xac8c,
    0xd5 => 0xac90, 0xd6 => 0xac94, 0xd7 => 0xac9c, 0xd8 => 0xac9d,
    0xd9 => 0xac9f, 0xda => 0xaca0, 0xdb => 0xaca1, 0xdc => 0xaca8,
    0xdd => 0xaca9, 0xde => 0xacaa, 0xdf => 0xacac, 0xe0 => 0xacaf,
    0xe1 => 0xacb0, 0xe2 => 0xacb8, 0xe3 => 0xacb9, 0xe4 => 0xacbb,
    0xe5 => 0xacbc, 0xe6 => 0xacbd, 0xe7 => 0xacc1, 0xe8 => 0xacc4,
    0xe9 => 0xacc8, 0xea => 0xaccc, 0xeb => 0xacd5, 0xec => 0xacd7,
    0xed => 0xace0, 0xee => 0xace1, 0xef => 0xace4, 0xf0 => 0xace7,
    0xf1 => 0xace8, 0xf2 => 0xacea, 0xf3 => 0xacec, 0xf4 => 0xacef,
    0xf5 => 0xacf0, 0xf6 => 0xacf1, 0xf7 => 0xacf3, 0xf8 => 0xacf5,
    0xf9 => 0xacf6, 0xfa => 0xacfc, 0xfb => 0xacfd, 0xfc => 0xad00,
    0xfd => 0xad04, 0xfe => 0xad06,
  },
  0xb1 => {
    0xa1 => 0xad0c, 0xa2 => 0xad0d, 0xa3 => 0xad0f, 0xa4 => 0xad11,
    0xa5 => 0xad18, 0xa6 => 0xad1c, 0xa7 => 0xad20, 0xa8 => 0xad29,
    0xa9 => 0xad2c, 0xaa => 0xad2d, 0xab => 0xad34, 0xac => 0xad35,
    0xad => 0xad38, 0xae => 0xad3c, 0xaf => 0xad44, 0xb0 => 0xad45,
    0xb1 => 0xad47, 0xb2 => 0xad49, 0xb3 => 0xad50, 0xb4 => 0xad54,
    0xb5 => 0xad58, 0xb6 => 0xad61, 0xb7 => 0xad63, 0xb8 => 0xad6c,
    0xb9 => 0xad6d, 0xba => 0xad70, 0xbb => 0xad73, 0xbc => 0xad74,
    0xbd => 0xad75, 0xbe => 0xad76, 0xbf => 0xad7b, 0xc0 => 0xad7c,
    0xc1 => 0xad7d, 0xc2 => 0xad7f, 0xc3 => 0xad81, 0xc4 => 0xad82,
    0xc5 => 0xad88, 0xc6 => 0xad89, 0xc7 => 0xad8c, 0xc8 => 0xad90,
    0xc9 => 0xad9c, 0xca => 0xad9d, 0xcb => 0xada4, 0xcc => 0xadb7,
    0xcd => 0xadc0, 0xce => 0xadc1, 0xcf => 0xadc4, 0xd0 => 0xadc8,
    0xd1 => 0xadd0, 0xd2 => 0xadd1, 0xd3 => 0xadd3, 0xd4 => 0xaddc,
    0xd5 => 0xade0, 0xd6 => 0xade4, 0xd7 => 0xadf8, 0xd8 => 0xadf9,
    0xd9 => 0xadfc, 0xda => 0xadff, 0xdb => 0xae00, 0xdc => 0xae01,
    0xdd => 0xae08, 0xde => 0xae09, 0xdf => 0xae0b, 0xe0 => 0xae0d,
    0xe1 => 0xae14, 0xe2 => 0xae30, 0xe3 => 0xae31, 0xe4 => 0xae34,
    0xe5 => 0xae37, 0xe6 => 0xae38, 0xe7 => 0xae3a, 0xe8 => 0xae40,
    0xe9 => 0xae41, 0xea => 0xae43, 0xeb => 0xae45, 0xec => 0xae46,
    0xed => 0xae4a, 0xee => 0xae4c, 0xef => 0xae4d, 0xf0 => 0xae4e,
    0xf1 => 0xae50, 0xf2 => 0xae54, 0xf3 => 0xae56, 0xf4 => 0xae5c,
    0xf5 => 0xae5d, 0xf6 => 0xae5f, 0xf7 => 0xae60, 0xf8 => 0xae61,
    0xf9 => 0xae65, 0xfa => 0xae68, 0xfb => 0xae69, 0xfc => 0xae6c,
    0xfd => 0xae70, 0xfe => 0xae78,
  },
  0xb2 => {
    0xa1 => 0xae79, 0xa2 => 0xae7b, 0xa3 => 0xae7c, 0xa4 => 0xae7d,
    0xa5 => 0xae84, 0xa6 => 0xae85, 0xa7 => 0xae8c, 0xa8 => 0xaebc,
    0xa9 => 0xaebd, 0xaa => 0xaebe, 0xab => 0xaec0, 0xac => 0xaec4,
    0xad => 0xaecc, 0xae => 0xaecd, 0xaf => 0xaecf, 0xb0 => 0xaed0,
    0xb1 => 0xaed1, 0xb2 => 0xaed8, 0xb3 => 0xaed9, 0xb4 => 0xaedc,
    0xb5 => 0xaee8, 0xb6 => 0xaeeb, 0xb7 => 0xaeed, 0xb8 => 0xaef4,
    0xb9 => 0xaef8, 0xba => 0xaefc, 0xbb => 0xaf07, 0xbc => 0xaf08,
    0xbd => 0xaf0d, 0xbe => 0xaf10, 0xbf => 0xaf2c, 0xc0 => 0xaf2d,
    0xc1 => 0xaf30, 0xc2 => 0xaf32, 0xc3 => 0xaf34, 0xc4 => 0xaf3c,
    0xc5 => 0xaf3d, 0xc6 => 0xaf3f, 0xc7 => 0xaf41, 0xc8 => 0xaf42,
    0xc9 => 0xaf43, 0xca => 0xaf48, 0xcb => 0xaf49, 0xcc => 0xaf50,
    0xcd => 0xaf5c, 0xce => 0xaf5d, 0xcf => 0xaf64, 0xd0 => 0xaf65,
    0xd1 => 0xaf79, 0xd2 => 0xaf80, 0xd3 => 0xaf84, 0xd4 => 0xaf88,
    0xd5 => 0xaf90, 0xd6 => 0xaf91, 0xd7 => 0xaf95, 0xd8 => 0xaf9c,
    0xd9 => 0xafb8, 0xda => 0xafb9, 0xdb => 0xafbc, 0xdc => 0xafc0,
    0xdd => 0xafc7, 0xde => 0xafc8, 0xdf => 0xafc9, 0xe0 => 0xafcb,
    0xe1 => 0xafcd, 0xe2 => 0xafce, 0xe3 => 0xafd4, 0xe4 => 0xafdc,
    0xe5 => 0xafe8, 0xe6 => 0xafe9, 0xe7 => 0xaff0, 0xe8 => 0xaff1,
    0xe9 => 0xaff4, 0xea => 0xaff8, 0xeb => 0xb000, 0xec => 0xb001,
    0xed => 0xb004, 0xee => 0xb00c, 0xef => 0xb010, 0xf0 => 0xb014,
    0xf1 => 0xb01c, 0xf2 => 0xb01d, 0xf3 => 0xb028, 0xf4 => 0xb044,
    0xf5 => 0xb045, 0xf6 => 0xb048, 0xf7 => 0xb04a, 0xf8 => 0xb04c,
    0xf9 => 0xb04e, 0xfa => 0xb053, 0xfb => 0xb054, 0xfc => 0xb055,
    0xfd => 0xb057, 0xfe => 0xb059,
  },
  0xb3 => {
    0xa1 => 0xb05d, 0xa2 => 0xb07c, 0xa3 => 0xb07d, 0xa4 => 0xb080,
    0xa5 => 0xb084, 0xa6 => 0xb08c, 0xa7 => 0xb08d, 0xa8 => 0xb08f,
    0xa9 => 0xb091, 0xaa => 0xb098, 0xab => 0xb099, 0xac => 0xb09a,
    0xad => 0xb09c, 0xae => 0xb09f, 0xaf => 0xb0a0, 0xb0 => 0xb0a1,
    0xb1 => 0xb0a2, 0xb2 => 0xb0a8, 0xb3 => 0xb0a9, 0xb4 => 0xb0ab,
    0xb5 => 0xb0ac, 0xb6 => 0xb0ad, 0xb7 => 0xb0ae, 0xb8 => 0xb0af,
    0xb9 => 0xb0b1, 0xba => 0xb0b3, 0xbb => 0xb0b4, 0xbc => 0xb0b5,
    0xbd => 0xb0b8, 0xbe => 0xb0bc, 0xbf => 0xb0c4, 0xc0 => 0xb0c5,
    0xc1 => 0xb0c7, 0xc2 => 0xb0c8, 0xc3 => 0xb0c9, 0xc4 => 0xb0d0,
    0xc5 => 0xb0d1, 0xc6 => 0xb0d4, 0xc7 => 0xb0d8, 0xc8 => 0xb0e0,
    0xc9 => 0xb0e5, 0xca => 0xb108, 0xcb => 0xb109, 0xcc => 0xb10b,
    0xcd => 0xb10c, 0xce => 0xb110, 0xcf => 0xb112, 0xd0 => 0xb113,
    0xd1 => 0xb118, 0xd2 => 0xb119, 0xd3 => 0xb11b, 0xd4 => 0xb11c,
    0xd5 => 0xb11d, 0xd6 => 0xb123, 0xd7 => 0xb124, 0xd8 => 0xb125,
    0xd9 => 0xb128, 0xda => 0xb12c, 0xdb => 0xb134, 0xdc => 0xb135,
    0xdd => 0xb137, 0xde => 0xb138, 0xdf => 0xb139, 0xe0 => 0xb140,
    0xe1 => 0xb141, 0xe2 => 0xb144, 0xe3 => 0xb148, 0xe4 => 0xb150,
    0xe5 => 0xb151, 0xe6 => 0xb154, 0xe7 => 0xb155, 0xe8 => 0xb158,
    0xe9 => 0xb15c, 0xea => 0xb160, 0xeb => 0xb178, 0xec => 0xb179,
    0xed => 0xb17c, 0xee => 0xb180, 0xef => 0xb182, 0xf0 => 0xb188,
    0xf1 => 0xb189, 0xf2 => 0xb18b, 0xf3 => 0xb18d, 0xf4 => 0xb192,
    0xf5 => 0xb193, 0xf6 => 0xb194, 0xf7 => 0xb198, 0xf8 => 0xb19c,
    0xf9 => 0xb1a8, 0xfa => 0xb1cc, 0xfb => 0xb1d0, 0xfc => 0xb1d4,
    0xfd => 0xb1dc, 0xfe => 0xb1dd,
  },
  0xb4 => {
    0xa1 => 0xb1df, 0xa2 => 0xb1e8, 0xa3 => 0xb1e9, 0xa4 => 0xb1ec,
    0xa5 => 0xb1f0, 0xa6 => 0xb1f9, 0xa7 => 0xb1fb, 0xa8 => 0xb1fd,
    0xa9 => 0xb204, 0xaa => 0xb205, 0xab => 0xb208, 0xac => 0xb20b,
    0xad => 0xb20c, 0xae => 0xb214, 0xaf => 0xb215, 0xb0 => 0xb217,
    0xb1 => 0xb219, 0xb2 => 0xb220, 0xb3 => 0xb234, 0xb4 => 0xb23c,
    0xb5 => 0xb258, 0xb6 => 0xb25c, 0xb7 => 0xb260, 0xb8 => 0xb268,
    0xb9 => 0xb269, 0xba => 0xb274, 0xbb => 0xb275, 0xbc => 0xb27c,
    0xbd => 0xb284, 0xbe => 0xb285, 0xbf => 0xb289, 0xc0 => 0xb290,
    0xc1 => 0xb291, 0xc2 => 0xb294, 0xc3 => 0xb298, 0xc4 => 0xb299,
    0xc5 => 0xb29a, 0xc6 => 0xb2a0, 0xc7 => 0xb2a1, 0xc8 => 0xb2a3,
    0xc9 => 0xb2a5, 0xca => 0xb2a6, 0xcb => 0xb2aa, 0xcc => 0xb2ac,
    0xcd => 0xb2b0, 0xce => 0xb2b4, 0xcf => 0xb2c8, 0xd0 => 0xb2c9,
    0xd1 => 0xb2cc, 0xd2 => 0xb2d0, 0xd3 => 0xb2d2, 0xd4 => 0xb2d8,
    0xd5 => 0xb2d9, 0xd6 => 0xb2db, 0xd7 => 0xb2dd, 0xd8 => 0xb2e2,
    0xd9 => 0xb2e4, 0xda => 0xb2e5, 0xdb => 0xb2e6, 0xdc => 0xb2e8,
    0xdd => 0xb2eb, 0xde => 0xb2ec, 0xdf => 0xb2ed, 0xe0 => 0xb2ee,
    0xe1 => 0xb2ef, 0xe2 => 0xb2f3, 0xe3 => 0xb2f4, 0xe4 => 0xb2f5,
    0xe5 => 0xb2f7, 0xe6 => 0xb2f8, 0xe7 => 0xb2f9, 0xe8 => 0xb2fa,
    0xe9 => 0xb2fb, 0xea => 0xb2ff, 0xeb => 0xb300, 0xec => 0xb301,
    0xed => 0xb304, 0xee => 0xb308, 0xef => 0xb310, 0xf0 => 0xb311,
    0xf1 => 0xb313, 0xf2 => 0xb314, 0xf3 => 0xb315, 0xf4 => 0xb31c,
    0xf5 => 0xb354, 0xf6 => 0xb355, 0xf7 => 0xb356, 0xf8 => 0xb358,
    0xf9 => 0xb35b, 0xfa => 0xb35c, 0xfb => 0xb35e, 0xfc => 0xb35f,
    0xfd => 0xb364, 0xfe => 0xb365,
  },
  0xb5 => {
    0xa1 => 0xb367, 0xa2 => 0xb369, 0xa3 => 0xb36b, 0xa4 => 0xb36e,
    0xa5 => 0xb370, 0xa6 => 0xb371, 0xa7 => 0xb374, 0xa8 => 0xb378,
    0xa9 => 0xb380, 0xaa => 0xb381, 0xab => 0xb383, 0xac => 0xb384,
    0xad => 0xb385, 0xae => 0xb38c, 0xaf => 0xb390, 0xb0 => 0xb394,
    0xb1 => 0xb3a0, 0xb2 => 0xb3a1, 0xb3 => 0xb3a8, 0xb4 => 0xb3ac,
    0xb5 => 0xb3c4, 0xb6 => 0xb3c5, 0xb7 => 0xb3c8, 0xb8 => 0xb3cb,
    0xb9 => 0xb3cc, 0xba => 0xb3ce, 0xbb => 0xb3d0, 0xbc => 0xb3d4,
    0xbd => 0xb3d5, 0xbe => 0xb3d7, 0xbf => 0xb3d9, 0xc0 => 0xb3db,
    0xc1 => 0xb3dd, 0xc2 => 0xb3e0, 0xc3 => 0xb3e4, 0xc4 => 0xb3e8,
    0xc5 => 0xb3fc, 0xc6 => 0xb410, 0xc7 => 0xb418, 0xc8 => 0xb41c,
    0xc9 => 0xb420, 0xca => 0xb428, 0xcb => 0xb429, 0xcc => 0xb42b,
    0xcd => 0xb434, 0xce => 0xb450, 0xcf => 0xb451, 0xd0 => 0xb454,
    0xd1 => 0xb458, 0xd2 => 0xb460, 0xd3 => 0xb461, 0xd4 => 0xb463,
    0xd5 => 0xb465, 0xd6 => 0xb46c, 0xd7 => 0xb480, 0xd8 => 0xb488,
    0xd9 => 0xb49d, 0xda => 0xb4a4, 0xdb => 0xb4a8, 0xdc => 0xb4ac,
    0xdd => 0xb4b5, 0xde => 0xb4b7, 0xdf => 0xb4b9, 0xe0 => 0xb4c0,
    0xe1 => 0xb4c4, 0xe2 => 0xb4c8, 0xe3 => 0xb4d0, 0xe4 => 0xb4d5,
    0xe5 => 0xb4dc, 0xe6 => 0xb4dd, 0xe7 => 0xb4e0, 0xe8 => 0xb4e3,
    0xe9 => 0xb4e4, 0xea => 0xb4e6, 0xeb => 0xb4ec, 0xec => 0xb4ed,
    0xed => 0xb4ef, 0xee => 0xb4f1, 0xef => 0xb4f8, 0xf0 => 0xb514,
    0xf1 => 0xb515, 0xf2 => 0xb518, 0xf3 => 0xb51b, 0xf4 => 0xb51c,
    0xf5 => 0xb524, 0xf6 => 0xb525, 0xf7 => 0xb527, 0xf8 => 0xb528,
    0xf9 => 0xb529, 0xfa => 0xb52a, 0xfb => 0xb530, 0xfc => 0xb531,
    0xfd => 0xb534, 0xfe => 0xb538,
  },
  0xb6 => {
    0xa1 => 0xb540, 0xa2 => 0xb541, 0xa3 => 0xb543, 0xa4 => 0xb544,
    0xa5 => 0xb545, 0xa6 => 0xb54b, 0xa7 => 0xb54c, 0xa8 => 0xb54d,
    0xa9 => 0xb550, 0xaa => 0xb554, 0xab => 0xb55c, 0xac => 0xb55d,
    0xad => 0xb55f, 0xae => 0xb560, 0xaf => 0xb561, 0xb0 => 0xb5a0,
    0xb1 => 0xb5a1, 0xb2 => 0xb5a4, 0xb3 => 0xb5a8, 0xb4 => 0xb5aa,
    0xb5 => 0xb5ab, 0xb6 => 0xb5b0, 0xb7 => 0xb5b1, 0xb8 => 0xb5b3,
    0xb9 => 0xb5b4, 0xba => 0xb5b5, 0xbb => 0xb5bb, 0xbc => 0xb5bc,
    0xbd => 0xb5bd, 0xbe => 0xb5c0, 0xbf => 0xb5c4, 0xc0 => 0xb5cc,
    0xc1 => 0xb5cd, 0xc2 => 0xb5cf, 0xc3 => 0xb5d0, 0xc4 => 0xb5d1,
    0xc5 => 0xb5d8, 0xc6 => 0xb5ec, 0xc7 => 0xb610, 0xc8 => 0xb611,
    0xc9 => 0xb614, 0xca => 0xb618, 0xcb => 0xb625, 0xcc => 0xb62c,
    0xcd => 0xb634, 0xce => 0xb648, 0xcf => 0xb664, 0xd0 => 0xb668,
    0xd1 => 0xb69c, 0xd2 => 0xb69d, 0xd3 => 0xb6a0, 0xd4 => 0xb6a4,
    0xd5 => 0xb6ab, 0xd6 => 0xb6ac, 0xd7 => 0xb6b1, 0xd8 => 0xb6d4,
    0xd9 => 0xb6f0, 0xda => 0xb6f4, 0xdb => 0xb6f8, 0xdc => 0xb700,
    0xdd => 0xb701, 0xde => 0xb705, 0xdf => 0xb728, 0xe0 => 0xb729,
    0xe1 => 0xb72c, 0xe2 => 0xb72f, 0xe3 => 0xb730, 0xe4 => 0xb738,
    0xe5 => 0xb739, 0xe6 => 0xb73b, 0xe7 => 0xb744, 0xe8 => 0xb748,
    0xe9 => 0xb74c, 0xea => 0xb754, 0xeb => 0xb755, 0xec => 0xb760,
    0xed => 0xb764, 0xee => 0xb768, 0xef => 0xb770, 0xf0 => 0xb771,
    0xf1 => 0xb773, 0xf2 => 0xb775, 0xf3 => 0xb77c, 0xf4 => 0xb77d,
    0xf5 => 0xb780, 0xf6 => 0xb784, 0xf7 => 0xb78c, 0xf8 => 0xb78d,
    0xf9 => 0xb78f, 0xfa => 0xb790, 0xfb => 0xb791, 0xfc => 0xb792,
    0xfd => 0xb796, 0xfe => 0xb797,
  },
  0xb7 => {
    0xa1 => 0xb798, 0xa2 => 0xb799, 0xa3 => 0xb79c, 0xa4 => 0xb7a0,
    0xa5 => 0xb7a8, 0xa6 => 0xb7a9, 0xa7 => 0xb7ab, 0xa8 => 0xb7ac,
    0xa9 => 0xb7ad, 0xaa => 0xb7b4, 0xab => 0xb7b5, 0xac => 0xb7b8,
    0xad => 0xb7c7, 0xae => 0xb7c9, 0xaf => 0xb7ec, 0xb0 => 0xb7ed,
    0xb1 => 0xb7f0, 0xb2 => 0xb7f4, 0xb3 => 0xb7fc, 0xb4 => 0xb7fd,
    0xb5 => 0xb7ff, 0xb6 => 0xb800, 0xb7 => 0xb801, 0xb8 => 0xb807,
    0xb9 => 0xb808, 0xba => 0xb809, 0xbb => 0xb80c, 0xbc => 0xb810,
    0xbd => 0xb818, 0xbe => 0xb819, 0xbf => 0xb81b, 0xc0 => 0xb81d,
    0xc1 => 0xb824, 0xc2 => 0xb825, 0xc3 => 0xb828, 0xc4 => 0xb82c,
    0xc5 => 0xb834, 0xc6 => 0xb835, 0xc7 => 0xb837, 0xc8 => 0xb838,
    0xc9 => 0xb839, 0xca => 0xb840, 0xcb => 0xb844, 0xcc => 0xb851,
    0xcd => 0xb853, 0xce => 0xb85c, 0xcf => 0xb85d, 0xd0 => 0xb860,
    0xd1 => 0xb864, 0xd2 => 0xb86c, 0xd3 => 0xb86d, 0xd4 => 0xb86f,
    0xd5 => 0xb871, 0xd6 => 0xb878, 0xd7 => 0xb87c, 0xd8 => 0xb88d,
    0xd9 => 0xb8a8, 0xda => 0xb8b0, 0xdb => 0xb8b4, 0xdc => 0xb8b8,
    0xdd => 0xb8c0, 0xde => 0xb8c1, 0xdf => 0xb8c3, 0xe0 => 0xb8c5,
    0xe1 => 0xb8cc, 0xe2 => 0xb8d0, 0xe3 => 0xb8d4, 0xe4 => 0xb8dd,
    0xe5 => 0xb8df, 0xe6 => 0xb8e1, 0xe7 => 0xb8e8, 0xe8 => 0xb8e9,
    0xe9 => 0xb8ec, 0xea => 0xb8f0, 0xeb => 0xb8f8, 0xec => 0xb8f9,
    0xed => 0xb8fb, 0xee => 0xb8fd, 0xef => 0xb904, 0xf0 => 0xb918,
    0xf1 => 0xb920, 0xf2 => 0xb93c, 0xf3 => 0xb93d, 0xf4 => 0xb940,
    0xf5 => 0xb944, 0xf6 => 0xb94c, 0xf7 => 0xb94f, 0xf8 => 0xb951,
    0xf9 => 0xb958, 0xfa => 0xb959, 0xfb => 0xb95c, 0xfc => 0xb960,
    0xfd => 0xb968, 0xfe => 0xb969,
  },
  0xb8 => {
    0xa1 => 0xb96b, 0xa2 => 0xb96d, 0xa3 => 0xb974, 0xa4 => 0xb975,
    0xa5 => 0xb978, 0xa6 => 0xb97c, 0xa7 => 0xb984, 0xa8 => 0xb985,
    0xa9 => 0xb987, 0xaa => 0xb989, 0xab => 0xb98a, 0xac => 0xb98d,
    0xad => 0xb98e, 0xae => 0xb9ac, 0xaf => 0xb9ad, 0xb0 => 0xb9b0,
    0xb1 => 0xb9b4, 0xb2 => 0xb9bc, 0xb3 => 0xb9bd, 0xb4 => 0xb9bf,
    0xb5 => 0xb9c1, 0xb6 => 0xb9c8, 0xb7 => 0xb9c9, 0xb8 => 0xb9cc,
    0xb9 => 0xb9ce, 0xba => 0xb9cf, 0xbb => 0xb9d0, 0xbc => 0xb9d1,
    0xbd => 0xb9d2, 0xbe => 0xb9d8, 0xbf => 0xb9d9, 0xc0 => 0xb9db,
    0xc1 => 0xb9dd, 0xc2 => 0xb9de, 0xc3 => 0xb9e1, 0xc4 => 0xb9e3,
    0xc5 => 0xb9e4, 0xc6 => 0xb9e5, 0xc7 => 0xb9e8, 0xc8 => 0xb9ec,
    0xc9 => 0xb9f4, 0xca => 0xb9f5, 0xcb => 0xb9f7, 0xcc => 0xb9f8,
    0xcd => 0xb9f9, 0xce => 0xb9fa, 0xcf => 0xba00, 0xd0 => 0xba01,
    0xd1 => 0xba08, 0xd2 => 0xba15, 0xd3 => 0xba38, 0xd4 => 0xba39,
    0xd5 => 0xba3c, 0xd6 => 0xba40, 0xd7 => 0xba42, 0xd8 => 0xba48,
    0xd9 => 0xba49, 0xda => 0xba4b, 0xdb => 0xba4d, 0xdc => 0xba4e,
    0xdd => 0xba53, 0xde => 0xba54, 0xdf => 0xba55, 0xe0 => 0xba58,
    0xe1 => 0xba5c, 0xe2 => 0xba64, 0xe3 => 0xba65, 0xe4 => 0xba67,
    0xe5 => 0xba68, 0xe6 => 0xba69, 0xe7 => 0xba70, 0xe8 => 0xba71,
    0xe9 => 0xba74, 0xea => 0xba78, 0xeb => 0xba83, 0xec => 0xba84,
    0xed => 0xba85, 0xee => 0xba87, 0xef => 0xba8c, 0xf0 => 0xbaa8,
    0xf1 => 0xbaa9, 0xf2 => 0xbaab, 0xf3 => 0xbaac, 0xf4 => 0xbab0,
    0xf5 => 0xbab2, 0xf6 => 0xbab8, 0xf7 => 0xbab9, 0xf8 => 0xbabb,
    0xf9 => 0xbabd, 0xfa => 0xbac4, 0xfb => 0xbac8, 0xfc => 0xbad8,
    0xfd => 0xbad9, 0xfe => 0xbafc,
  },
  0xb9 => {
    0xa1 => 0xbb00, 0xa2 => 0xbb04, 0xa3 => 0xbb0d, 0xa4 => 0xbb0f,
    0xa5 => 0xbb11, 0xa6 => 0xbb18, 0xa7 => 0xbb1c, 0xa8 => 0xbb20,
    0xa9 => 0xbb29, 0xaa => 0xbb2b, 0xab => 0xbb34, 0xac => 0xbb35,
    0xad => 0xbb36, 0xae => 0xbb38, 0xaf => 0xbb3b, 0xb0 => 0xbb3c,
    0xb1 => 0xbb3d, 0xb2 => 0xbb3e, 0xb3 => 0xbb44, 0xb4 => 0xbb45,
    0xb5 => 0xbb47, 0xb6 => 0xbb49, 0xb7 => 0xbb4d, 0xb8 => 0xbb4f,
    0xb9 => 0xbb50, 0xba => 0xbb54, 0xbb => 0xbb58, 0xbc => 0xbb61,
    0xbd => 0xbb63, 0xbe => 0xbb6c, 0xbf => 0xbb88, 0xc0 => 0xbb8c,
    0xc1 => 0xbb90, 0xc2 => 0xbba4, 0xc3 => 0xbba8, 0xc4 => 0xbbac,
    0xc5 => 0xbbb4, 0xc6 => 0xbbb7, 0xc7 => 0xbbc0, 0xc8 => 0xbbc4,
    0xc9 => 0xbbc8, 0xca => 0xbbd0, 0xcb => 0xbbd3, 0xcc => 0xbbf8,
    0xcd => 0xbbf9, 0xce => 0xbbfc, 0xcf => 0xbbff, 0xd0 => 0xbc00,
    0xd1 => 0xbc02, 0xd2 => 0xbc08, 0xd3 => 0xbc09, 0xd4 => 0xbc0b,
    0xd5 => 0xbc0c, 0xd6 => 0xbc0d, 0xd7 => 0xbc0f, 0xd8 => 0xbc11,
    0xd9 => 0xbc14, 0xda => 0xbc15, 0xdb => 0xbc16, 0xdc => 0xbc17,
    0xdd => 0xbc18, 0xde => 0xbc1b, 0xdf => 0xbc1c, 0xe0 => 0xbc1d,
    0xe1 => 0xbc1e, 0xe2 => 0xbc1f, 0xe3 => 0xbc24, 0xe4 => 0xbc25,
    0xe5 => 0xbc27, 0xe6 => 0xbc29, 0xe7 => 0xbc2d, 0xe8 => 0xbc30,
    0xe9 => 0xbc31, 0xea => 0xbc34, 0xeb => 0xbc38, 0xec => 0xbc40,
    0xed => 0xbc41, 0xee => 0xbc43, 0xef => 0xbc44, 0xf0 => 0xbc45,
    0xf1 => 0xbc49, 0xf2 => 0xbc4c, 0xf3 => 0xbc4d, 0xf4 => 0xbc50,
    0xf5 => 0xbc5d, 0xf6 => 0xbc84, 0xf7 => 0xbc85, 0xf8 => 0xbc88,
    0xf9 => 0xbc8b, 0xfa => 0xbc8c, 0xfb => 0xbc8e, 0xfc => 0xbc94,
    0xfd => 0xbc95, 0xfe => 0xbc97,
  },
  0xba => {
    0xa1 => 0xbc99, 0xa2 => 0xbc9a, 0xa3 => 0xbca0, 0xa4 => 0xbca1,
    0xa5 => 0xbca4, 0xa6 => 0xbca7, 0xa7 => 0xbca8, 0xa8 => 0xbcb0,
    0xa9 => 0xbcb1, 0xaa => 0xbcb3, 0xab => 0xbcb4, 0xac => 0xbcb5,
    0xad => 0xbcbc, 0xae => 0xbcbd, 0xaf => 0xbcc0, 0xb0 => 0xbcc4,
    0xb1 => 0xbccd, 0xb2 => 0xbccf, 0xb3 => 0xbcd0, 0xb4 => 0xbcd1,
    0xb5 => 0xbcd5, 0xb6 => 0xbcd8, 0xb7 => 0xbcdc, 0xb8 => 0xbcf4,
    0xb9 => 0xbcf5, 0xba => 0xbcf6, 0xbb => 0xbcf8, 0xbc => 0xbcfc,
    0xbd => 0xbd04, 0xbe => 0xbd05, 0xbf => 0xbd07, 0xc0 => 0xbd09,
    0xc1 => 0xbd10, 0xc2 => 0xbd14, 0xc3 => 0xbd24, 0xc4 => 0xbd2c,
    0xc5 => 0xbd40, 0xc6 => 0xbd48, 0xc7 => 0xbd49, 0xc8 => 0xbd4c,
    0xc9 => 0xbd50, 0xca => 0xbd58, 0xcb => 0xbd59, 0xcc => 0xbd64,
    0xcd => 0xbd68, 0xce => 0xbd80, 0xcf => 0xbd81, 0xd0 => 0xbd84,
    0xd1 => 0xbd87, 0xd2 => 0xbd88, 0xd3 => 0xbd89, 0xd4 => 0xbd8a,
    0xd5 => 0xbd90, 0xd6 => 0xbd91, 0xd7 => 0xbd93, 0xd8 => 0xbd95,
    0xd9 => 0xbd99, 0xda => 0xbd9a, 0xdb => 0xbd9c, 0xdc => 0xbda4,
    0xdd => 0xbdb0, 0xde => 0xbdb8, 0xdf => 0xbdd4, 0xe0 => 0xbdd5,
    0xe1 => 0xbdd8, 0xe2 => 0xbddc, 0xe3 => 0xbde9, 0xe4 => 0xbdf0,
    0xe5 => 0xbdf4, 0xe6 => 0xbdf8, 0xe7 => 0xbe00, 0xe8 => 0xbe03,
    0xe9 => 0xbe05, 0xea => 0xbe0c, 0xeb => 0xbe0d, 0xec => 0xbe10,
    0xed => 0xbe14, 0xee => 0xbe1c, 0xef => 0xbe1d, 0xf0 => 0xbe1f,
    0xf1 => 0xbe44, 0xf2 => 0xbe45, 0xf3 => 0xbe48, 0xf4 => 0xbe4c,
    0xf5 => 0xbe4e, 0xf6 => 0xbe54, 0xf7 => 0xbe55, 0xf8 => 0xbe57,
    0xf9 => 0xbe59, 0xfa => 0xbe5a, 0xfb => 0xbe5b, 0xfc => 0xbe60,
    0xfd => 0xbe61, 0xfe => 0xbe64,
  },
  0xbb => {
    0xa1 => 0xbe68, 0xa2 => 0xbe6a, 0xa3 => 0xbe70, 0xa4 => 0xbe71,
    0xa5 => 0xbe73, 0xa6 => 0xbe74, 0xa7 => 0xbe75, 0xa8 => 0xbe7b,
    0xa9 => 0xbe7c, 0xaa => 0xbe7d, 0xab => 0xbe80, 0xac => 0xbe84,
    0xad => 0xbe8c, 0xae => 0xbe8d, 0xaf => 0xbe8f, 0xb0 => 0xbe90,
    0xb1 => 0xbe91, 0xb2 => 0xbe98, 0xb3 => 0xbe99, 0xb4 => 0xbea8,
    0xb5 => 0xbed0, 0xb6 => 0xbed1, 0xb7 => 0xbed4, 0xb8 => 0xbed7,
    0xb9 => 0xbed8, 0xba => 0xbee0, 0xbb => 0xbee3, 0xbc => 0xbee4,
    0xbd => 0xbee5, 0xbe => 0xbeec, 0xbf => 0xbf01, 0xc0 => 0xbf08,
    0xc1 => 0xbf09, 0xc2 => 0xbf18, 0xc3 => 0xbf19, 0xc4 => 0xbf1b,
    0xc5 => 0xbf1c, 0xc6 => 0xbf1d, 0xc7 => 0xbf40, 0xc8 => 0xbf41,
    0xc9 => 0xbf44, 0xca => 0xbf48, 0xcb => 0xbf50, 0xcc => 0xbf51,
    0xcd => 0xbf55, 0xce => 0xbf94, 0xcf => 0xbfb0, 0xd0 => 0xbfc5,
    0xd1 => 0xbfcc, 0xd2 => 0xbfcd, 0xd3 => 0xbfd0, 0xd4 => 0xbfd4,
    0xd5 => 0xbfdc, 0xd6 => 0xbfdf, 0xd7 => 0xbfe1, 0xd8 => 0xc03c,
    0xd9 => 0xc051, 0xda => 0xc058, 0xdb => 0xc05c, 0xdc => 0xc060,
    0xdd => 0xc068, 0xde => 0xc069, 0xdf => 0xc090, 0xe0 => 0xc091,
    0xe1 => 0xc094, 0xe2 => 0xc098, 0xe3 => 0xc0a0, 0xe4 => 0xc0a1,
    0xe5 => 0xc0a3, 0xe6 => 0xc0a5, 0xe7 => 0xc0ac, 0xe8 => 0xc0ad,
    0xe9 => 0xc0af, 0xea => 0xc0b0, 0xeb => 0xc0b3, 0xec => 0xc0b4,
    0xed => 0xc0b5, 0xee => 0xc0b6, 0xef => 0xc0bc, 0xf0 => 0xc0bd,
    0xf1 => 0xc0bf, 0xf2 => 0xc0c0, 0xf3 => 0xc0c1, 0xf4 => 0xc0c5,
    0xf5 => 0xc0c8, 0xf6 => 0xc0c9, 0xf7 => 0xc0cc, 0xf8 => 0xc0d0,
    0xf9 => 0xc0d8, 0xfa => 0xc0d9, 0xfb => 0xc0db, 0xfc => 0xc0dc,
    0xfd => 0xc0dd, 0xfe => 0xc0e4,
  },
  0xbc => {
    0xa1 => 0xc0e5, 0xa2 => 0xc0e8, 0xa3 => 0xc0ec, 0xa4 => 0xc0f4,
    0xa5 => 0xc0f5, 0xa6 => 0xc0f7, 0xa7 => 0xc0f9, 0xa8 => 0xc100,
    0xa9 => 0xc104, 0xaa => 0xc108, 0xab => 0xc110, 0xac => 0xc115,
    0xad => 0xc11c, 0xae => 0xc11d, 0xaf => 0xc11e, 0xb0 => 0xc11f,
    0xb1 => 0xc120, 0xb2 => 0xc123, 0xb3 => 0xc124, 0xb4 => 0xc126,
    0xb5 => 0xc127, 0xb6 => 0xc12c, 0xb7 => 0xc12d, 0xb8 => 0xc12f,
    0xb9 => 0xc130, 0xba => 0xc131, 0xbb => 0xc136, 0xbc => 0xc138,
    0xbd => 0xc139, 0xbe => 0xc13c, 0xbf => 0xc140, 0xc0 => 0xc148,
    0xc1 => 0xc149, 0xc2 => 0xc14b, 0xc3 => 0xc14c, 0xc4 => 0xc14d,
    0xc5 => 0xc154, 0xc6 => 0xc155, 0xc7 => 0xc158, 0xc8 => 0xc15c,
    0xc9 => 0xc164, 0xca => 0xc165, 0xcb => 0xc167, 0xcc => 0xc168,
    0xcd => 0xc169, 0xce => 0xc170, 0xcf => 0xc174, 0xd0 => 0xc178,
    0xd1 => 0xc185, 0xd2 => 0xc18c, 0xd3 => 0xc18d, 0xd4 => 0xc18e,
    0xd5 => 0xc190, 0xd6 => 0xc194, 0xd7 => 0xc196, 0xd8 => 0xc19c,
    0xd9 => 0xc19d, 0xda => 0xc19f, 0xdb => 0xc1a1, 0xdc => 0xc1a5,
    0xdd => 0xc1a8, 0xde => 0xc1a9, 0xdf => 0xc1ac, 0xe0 => 0xc1b0,
    0xe1 => 0xc1bd, 0xe2 => 0xc1c4, 0xe3 => 0xc1c8, 0xe4 => 0xc1cc,
    0xe5 => 0xc1d4, 0xe6 => 0xc1d7, 0xe7 => 0xc1d8, 0xe8 => 0xc1e0,
    0xe9 => 0xc1e4, 0xea => 0xc1e8, 0xeb => 0xc1f0, 0xec => 0xc1f1,
    0xed => 0xc1f3, 0xee => 0xc1fc, 0xef => 0xc1fd, 0xf0 => 0xc200,
    0xf1 => 0xc204, 0xf2 => 0xc20c, 0xf3 => 0xc20d, 0xf4 => 0xc20f,
    0xf5 => 0xc211, 0xf6 => 0xc218, 0xf7 => 0xc219, 0xf8 => 0xc21c,
    0xf9 => 0xc21f, 0xfa => 0xc220, 0xfb => 0xc228, 0xfc => 0xc229,
    0xfd => 0xc22b, 0xfe => 0xc22d,
  },
  0xbd => {
    0xa1 => 0xc22f, 0xa2 => 0xc231, 0xa3 => 0xc232, 0xa4 => 0xc234,
    0xa5 => 0xc248, 0xa6 => 0xc250, 0xa7 => 0xc251, 0xa8 => 0xc254,
    0xa9 => 0xc258, 0xaa => 0xc260, 0xab => 0xc265, 0xac => 0xc26c,
    0xad => 0xc26d, 0xae => 0xc270, 0xaf => 0xc274, 0xb0 => 0xc27c,
    0xb1 => 0xc27d, 0xb2 => 0xc27f, 0xb3 => 0xc281, 0xb4 => 0xc288,
    0xb5 => 0xc289, 0xb6 => 0xc290, 0xb7 => 0xc298, 0xb8 => 0xc29b,
    0xb9 => 0xc29d, 0xba => 0xc2a4, 0xbb => 0xc2a5, 0xbc => 0xc2a8,
    0xbd => 0xc2ac, 0xbe => 0xc2ad, 0xbf => 0xc2b4, 0xc0 => 0xc2b5,
    0xc1 => 0xc2b7, 0xc2 => 0xc2b9, 0xc3 => 0xc2dc, 0xc4 => 0xc2dd,
    0xc5 => 0xc2e0, 0xc6 => 0xc2e3, 0xc7 => 0xc2e4, 0xc8 => 0xc2eb,
    0xc9 => 0xc2ec, 0xca => 0xc2ed, 0xcb => 0xc2ef, 0xcc => 0xc2f1,
    0xcd => 0xc2f6, 0xce => 0xc2f8, 0xcf => 0xc2f9, 0xd0 => 0xc2fb,
    0xd1 => 0xc2fc, 0xd2 => 0xc300, 0xd3 => 0xc308, 0xd4 => 0xc309,
    0xd5 => 0xc30c, 0xd6 => 0xc30d, 0xd7 => 0xc313, 0xd8 => 0xc314,
    0xd9 => 0xc315, 0xda => 0xc318, 0xdb => 0xc31c, 0xdc => 0xc324,
    0xdd => 0xc325, 0xde => 0xc328, 0xdf => 0xc329, 0xe0 => 0xc345,
    0xe1 => 0xc368, 0xe2 => 0xc369, 0xe3 => 0xc36c, 0xe4 => 0xc370,
    0xe5 => 0xc372, 0xe6 => 0xc378, 0xe7 => 0xc379, 0xe8 => 0xc37c,
    0xe9 => 0xc37d, 0xea => 0xc384, 0xeb => 0xc388, 0xec => 0xc38c,
    0xed => 0xc3c0, 0xee => 0xc3d8, 0xef => 0xc3d9, 0xf0 => 0xc3dc,
    0xf1 => 0xc3df, 0xf2 => 0xc3e0, 0xf3 => 0xc3e2, 0xf4 => 0xc3e8,
    0xf5 => 0xc3e9, 0xf6 => 0xc3ed, 0xf7 => 0xc3f4, 0xf8 => 0xc3f5,
    0xf9 => 0xc3f8, 0xfa => 0xc408, 0xfb => 0xc410, 0xfc => 0xc424,
    0xfd => 0xc42c, 0xfe => 0xc430,
  },
  0xbe => {
    0xa1 => 0xc434, 0xa2 => 0xc43c, 0xa3 => 0xc43d, 0xa4 => 0xc448,
    0xa5 => 0xc464, 0xa6 => 0xc465, 0xa7 => 0xc468, 0xa8 => 0xc46c,
    0xa9 => 0xc474, 0xaa => 0xc475, 0xab => 0xc479, 0xac => 0xc480,
    0xad => 0xc494, 0xae => 0xc49c, 0xaf => 0xc4b8, 0xb0 => 0xc4bc,
    0xb1 => 0xc4e9, 0xb2 => 0xc4f0, 0xb3 => 0xc4f1, 0xb4 => 0xc4f4,
    0xb5 => 0xc4f8, 0xb6 => 0xc4fa, 0xb7 => 0xc4ff, 0xb8 => 0xc500,
    0xb9 => 0xc501, 0xba => 0xc50c, 0xbb => 0xc510, 0xbc => 0xc514,
    0xbd => 0xc51c, 0xbe => 0xc528, 0xbf => 0xc529, 0xc0 => 0xc52c,
    0xc1 => 0xc530, 0xc2 => 0xc538, 0xc3 => 0xc539, 0xc4 => 0xc53b,
    0xc5 => 0xc53d, 0xc6 => 0xc544, 0xc7 => 0xc545, 0xc8 => 0xc548,
    0xc9 => 0xc549, 0xca => 0xc54a, 0xcb => 0xc54c, 0xcc => 0xc54d,
    0xcd => 0xc54e, 0xce => 0xc553, 0xcf => 0xc554, 0xd0 => 0xc555,
    0xd1 => 0xc557, 0xd2 => 0xc558, 0xd3 => 0xc559, 0xd4 => 0xc55d,
    0xd5 => 0xc55e, 0xd6 => 0xc560, 0xd7 => 0xc561, 0xd8 => 0xc564,
    0xd9 => 0xc568, 0xda => 0xc570, 0xdb => 0xc571, 0xdc => 0xc573,
    0xdd => 0xc574, 0xde => 0xc575, 0xdf => 0xc57c, 0xe0 => 0xc57d,
    0xe1 => 0xc580, 0xe2 => 0xc584, 0xe3 => 0xc587, 0xe4 => 0xc58c,
    0xe5 => 0xc58d, 0xe6 => 0xc58f, 0xe7 => 0xc591, 0xe8 => 0xc595,
    0xe9 => 0xc597, 0xea => 0xc598, 0xeb => 0xc59c, 0xec => 0xc5a0,
    0xed => 0xc5a9, 0xee => 0xc5b4, 0xef => 0xc5b5, 0xf0 => 0xc5b8,
    0xf1 => 0xc5b9, 0xf2 => 0xc5bb, 0xf3 => 0xc5bc, 0xf4 => 0xc5bd,
    0xf5 => 0xc5be, 0xf6 => 0xc5c4, 0xf7 => 0xc5c5, 0xf8 => 0xc5c6,
    0xf9 => 0xc5c7, 0xfa => 0xc5c8, 0xfb => 0xc5c9, 0xfc => 0xc5ca,
    0xfd => 0xc5cc, 0xfe => 0xc5ce,
  },
  0xbf => {
    0xa1 => 0xc5d0, 0xa2 => 0xc5d1, 0xa3 => 0xc5d4, 0xa4 => 0xc5d8,
    0xa5 => 0xc5e0, 0xa6 => 0xc5e1, 0xa7 => 0xc5e3, 0xa8 => 0xc5e5,
    0xa9 => 0xc5ec, 0xaa => 0xc5ed, 0xab => 0xc5ee, 0xac => 0xc5f0,
    0xad => 0xc5f4, 0xae => 0xc5f6, 0xaf => 0xc5f7, 0xb0 => 0xc5fc,
    0xb1 => 0xc5fd, 0xb2 => 0xc5fe, 0xb3 => 0xc5ff, 0xb4 => 0xc600,
    0xb5 => 0xc601, 0xb6 => 0xc605, 0xb7 => 0xc606, 0xb8 => 0xc607,
    0xb9 => 0xc608, 0xba => 0xc60c, 0xbb => 0xc610, 0xbc => 0xc618,
    0xbd => 0xc619, 0xbe => 0xc61b, 0xbf => 0xc61c, 0xc0 => 0xc624,
    0xc1 => 0xc625, 0xc2 => 0xc628, 0xc3 => 0xc62c, 0xc4 => 0xc62d,
    0xc5 => 0xc62e, 0xc6 => 0xc630, 0xc7 => 0xc633, 0xc8 => 0xc634,
    0xc9 => 0xc635, 0xca => 0xc637, 0xcb => 0xc639, 0xcc => 0xc63b,
    0xcd => 0xc640, 0xce => 0xc641, 0xcf => 0xc644, 0xd0 => 0xc648,
    0xd1 => 0xc650, 0xd2 => 0xc651, 0xd3 => 0xc653, 0xd4 => 0xc654,
    0xd5 => 0xc655, 0xd6 => 0xc65c, 0xd7 => 0xc65d, 0xd8 => 0xc660,
    0xd9 => 0xc66c, 0xda => 0xc66f, 0xdb => 0xc671, 0xdc => 0xc678,
    0xdd => 0xc679, 0xde => 0xc67c, 0xdf => 0xc680, 0xe0 => 0xc688,
    0xe1 => 0xc689, 0xe2 => 0xc68b, 0xe3 => 0xc68d, 0xe4 => 0xc694,
    0xe5 => 0xc695, 0xe6 => 0xc698, 0xe7 => 0xc69c, 0xe8 => 0xc6a4,
    0xe9 => 0xc6a5, 0xea => 0xc6a7, 0xeb => 0xc6a9, 0xec => 0xc6b0,
    0xed => 0xc6b1, 0xee => 0xc6b4, 0xef => 0xc6b8, 0xf0 => 0xc6b9,
    0xf1 => 0xc6ba, 0xf2 => 0xc6c0, 0xf3 => 0xc6c1, 0xf4 => 0xc6c3,
    0xf5 => 0xc6c5, 0xf6 => 0xc6cc, 0xf7 => 0xc6cd, 0xf8 => 0xc6d0,
    0xf9 => 0xc6d4, 0xfa => 0xc6dc, 0xfb => 0xc6dd, 0xfc => 0xc6e0,
    0xfd => 0xc6e1, 0xfe => 0xc6e8,
  },
  0xc0 => {
    0xa1 => 0xc6e9, 0xa2 => 0xc6ec, 0xa3 => 0xc6f0, 0xa4 => 0xc6f8,
    0xa5 => 0xc6f9, 0xa6 => 0xc6fd, 0xa7 => 0xc704, 0xa8 => 0xc705,
    0xa9 => 0xc708, 0xaa => 0xc70c, 0xab => 0xc714, 0xac => 0xc715,
    0xad => 0xc717, 0xae => 0xc719, 0xaf => 0xc720, 0xb0 => 0xc721,
    0xb1 => 0xc724, 0xb2 => 0xc728, 0xb3 => 0xc730, 0xb4 => 0xc731,
    0xb5 => 0xc733, 0xb6 => 0xc735, 0xb7 => 0xc737, 0xb8 => 0xc73c,
    0xb9 => 0xc73d, 0xba => 0xc740, 0xbb => 0xc744, 0xbc => 0xc74a,
    0xbd => 0xc74c, 0xbe => 0xc74d, 0xbf => 0xc74f, 0xc0 => 0xc751,
    0xc1 => 0xc752, 0xc2 => 0xc753, 0xc3 => 0xc754, 0xc4 => 0xc755,
    0xc5 => 0xc756, 0xc6 => 0xc757, 0xc7 => 0xc758, 0xc8 => 0xc75c,
    0xc9 => 0xc760, 0xca => 0xc768, 0xcb => 0xc76b, 0xcc => 0xc774,
    0xcd => 0xc775, 0xce => 0xc778, 0xcf => 0xc77c, 0xd0 => 0xc77d,
    0xd1 => 0xc77e, 0xd2 => 0xc783, 0xd3 => 0xc784, 0xd4 => 0xc785,
    0xd5 => 0xc787, 0xd6 => 0xc788, 0xd7 => 0xc789, 0xd8 => 0xc78a,
    0xd9 => 0xc78e, 0xda => 0xc790, 0xdb => 0xc791, 0xdc => 0xc794,
    0xdd => 0xc796, 0xde => 0xc797, 0xdf => 0xc798, 0xe0 => 0xc79a,
    0xe1 => 0xc7a0, 0xe2 => 0xc7a1, 0xe3 => 0xc7a3, 0xe4 => 0xc7a4,
    0xe5 => 0xc7a5, 0xe6 => 0xc7a6, 0xe7 => 0xc7ac, 0xe8 => 0xc7ad,
    0xe9 => 0xc7b0, 0xea => 0xc7b4, 0xeb => 0xc7bc, 0xec => 0xc7bd,
    0xed => 0xc7bf, 0xee => 0xc7c0, 0xef => 0xc7c1, 0xf0 => 0xc7c8,
    0xf1 => 0xc7c9, 0xf2 => 0xc7cc, 0xf3 => 0xc7ce, 0xf4 => 0xc7d0,
    0xf5 => 0xc7d8, 0xf6 => 0xc7dd, 0xf7 => 0xc7e4, 0xf8 => 0xc7e8,
    0xf9 => 0xc7ec, 0xfa => 0xc800, 0xfb => 0xc801, 0xfc => 0xc804,
    0xfd => 0xc808, 0xfe => 0xc80a,
  },
  0xc1 => {
    0xa1 => 0xc810, 0xa2 => 0xc811, 0xa3 => 0xc813, 0xa4 => 0xc815,
    0xa5 => 0xc816, 0xa6 => 0xc81c, 0xa7 => 0xc81d, 0xa8 => 0xc820,
    0xa9 => 0xc824, 0xaa => 0xc82c, 0xab => 0xc82d, 0xac => 0xc82f,
    0xad => 0xc831, 0xae => 0xc838, 0xaf => 0xc83c, 0xb0 => 0xc840,
    0xb1 => 0xc848, 0xb2 => 0xc849, 0xb3 => 0xc84c, 0xb4 => 0xc84d,
    0xb5 => 0xc854, 0xb6 => 0xc870, 0xb7 => 0xc871, 0xb8 => 0xc874,
    0xb9 => 0xc878, 0xba => 0xc87a, 0xbb => 0xc880, 0xbc => 0xc881,
    0xbd => 0xc883, 0xbe => 0xc885, 0xbf => 0xc886, 0xc0 => 0xc887,
    0xc1 => 0xc88b, 0xc2 => 0xc88c, 0xc3 => 0xc88d, 0xc4 => 0xc894,
    0xc5 => 0xc89d, 0xc6 => 0xc89f, 0xc7 => 0xc8a1, 0xc8 => 0xc8a8,
    0xc9 => 0xc8bc, 0xca => 0xc8bd, 0xcb => 0xc8c4, 0xcc => 0xc8c8,
    0xcd => 0xc8cc, 0xce => 0xc8d4, 0xcf => 0xc8d5, 0xd0 => 0xc8d7,
    0xd1 => 0xc8d9, 0xd2 => 0xc8e0, 0xd3 => 0xc8e1, 0xd4 => 0xc8e4,
    0xd5 => 0xc8f5, 0xd6 => 0xc8fc, 0xd7 => 0xc8fd, 0xd8 => 0xc900,
    0xd9 => 0xc904, 0xda => 0xc905, 0xdb => 0xc906, 0xdc => 0xc90c,
    0xdd => 0xc90d, 0xde => 0xc90f, 0xdf => 0xc911, 0xe0 => 0xc918,
    0xe1 => 0xc92c, 0xe2 => 0xc934, 0xe3 => 0xc950, 0xe4 => 0xc951,
    0xe5 => 0xc954, 0xe6 => 0xc958, 0xe7 => 0xc960, 0xe8 => 0xc961,
    0xe9 => 0xc963, 0xea => 0xc96c, 0xeb => 0xc970, 0xec => 0xc974,
    0xed => 0xc97c, 0xee => 0xc988, 0xef => 0xc989, 0xf0 => 0xc98c,
    0xf1 => 0xc990, 0xf2 => 0xc998, 0xf3 => 0xc999, 0xf4 => 0xc99b,
    0xf5 => 0xc99d, 0xf6 => 0xc9c0, 0xf7 => 0xc9c1, 0xf8 => 0xc9c4,
    0xf9 => 0xc9c7, 0xfa => 0xc9c8, 0xfb => 0xc9ca, 0xfc => 0xc9d0,
    0xfd => 0xc9d1, 0xfe => 0xc9d3,
  },
  0xc2 => {
    0xa1 => 0xc9d5, 0xa2 => 0xc9d6, 0xa3 => 0xc9d9, 0xa4 => 0xc9da,
    0xa5 => 0xc9dc, 0xa6 => 0xc9dd, 0xa7 => 0xc9e0, 0xa8 => 0xc9e2,
    0xa9 => 0xc9e4, 0xaa => 0xc9e7, 0xab => 0xc9ec, 0xac => 0xc9ed,
    0xad => 0xc9ef, 0xae => 0xc9f0, 0xaf => 0xc9f1, 0xb0 => 0xc9f8,
    0xb1 => 0xc9f9, 0xb2 => 0xc9fc, 0xb3 => 0xca00, 0xb4 => 0xca08,
    0xb5 => 0xca09, 0xb6 => 0xca0b, 0xb7 => 0xca0c, 0xb8 => 0xca0d,
    0xb9 => 0xca14, 0xba => 0xca18, 0xbb => 0xca29, 0xbc => 0xca4c,
    0xbd => 0xca4d, 0xbe => 0xca50, 0xbf => 0xca54, 0xc0 => 0xca5c,
    0xc1 => 0xca5d, 0xc2 => 0xca5f, 0xc3 => 0xca60, 0xc4 => 0xca61,
    0xc5 => 0xca68, 0xc6 => 0xca7d, 0xc7 => 0xca84, 0xc8 => 0xca98,
    0xc9 => 0xcabc, 0xca => 0xcabd, 0xcb => 0xcac0, 0xcc => 0xcac4,
    0xcd => 0xcacc, 0xce => 0xcacd, 0xcf => 0xcacf, 0xd0 => 0xcad1,
    0xd1 => 0xcad3, 0xd2 => 0xcad8, 0xd3 => 0xcad9, 0xd4 => 0xcae0,
    0xd5 => 0xcaec, 0xd6 => 0xcaf4, 0xd7 => 0xcb08, 0xd8 => 0xcb10,
    0xd9 => 0xcb14, 0xda => 0xcb18, 0xdb => 0xcb20, 0xdc => 0xcb21,
    0xdd => 0xcb41, 0xde => 0xcb48, 0xdf => 0xcb49, 0xe0 => 0xcb4c,
    0xe1 => 0xcb50, 0xe2 => 0xcb58, 0xe3 => 0xcb59, 0xe4 => 0xcb5d,
    0xe5 => 0xcb64, 0xe6 => 0xcb78, 0xe7 => 0xcb79, 0xe8 => 0xcb9c,
    0xe9 => 0xcbb8, 0xea => 0xcbd4, 0xeb => 0xcbe4, 0xec => 0xcbe7,
    0xed => 0xcbe9, 0xee => 0xcc0c, 0xef => 0xcc0d, 0xf0 => 0xcc10,
    0xf1 => 0xcc14, 0xf2 => 0xcc1c, 0xf3 => 0xcc1d, 0xf4 => 0xcc21,
    0xf5 => 0xcc22, 0xf6 => 0xcc27, 0xf7 => 0xcc28, 0xf8 => 0xcc29,
    0xf9 => 0xcc2c, 0xfa => 0xcc2e, 0xfb => 0xcc30, 0xfc => 0xcc38,
    0xfd => 0xcc39, 0xfe => 0xcc3b,
  },
  0xc3 => {
    0xa1 => 0xcc3c, 0xa2 => 0xcc3d, 0xa3 => 0xcc3e, 0xa4 => 0xcc44,
    0xa5 => 0xcc45, 0xa6 => 0xcc48, 0xa7 => 0xcc4c, 0xa8 => 0xcc54,
    0xa9 => 0xcc55, 0xaa => 0xcc57, 0xab => 0xcc58, 0xac => 0xcc59,
    0xad => 0xcc60, 0xae => 0xcc64, 0xaf => 0xcc66, 0xb0 => 0xcc68,
    0xb1 => 0xcc70, 0xb2 => 0xcc75, 0xb3 => 0xcc98, 0xb4 => 0xcc99,
    0xb5 => 0xcc9c, 0xb6 => 0xcca0, 0xb7 => 0xcca8, 0xb8 => 0xcca9,
    0xb9 => 0xccab, 0xba => 0xccac, 0xbb => 0xccad, 0xbc => 0xccb4,
    0xbd => 0xccb5, 0xbe => 0xccb8, 0xbf => 0xccbc, 0xc0 => 0xccc4,
    0xc1 => 0xccc5, 0xc2 => 0xccc7, 0xc3 => 0xccc9, 0xc4 => 0xccd0,
    0xc5 => 0xccd4, 0xc6 => 0xcce4, 0xc7 => 0xccec, 0xc8 => 0xccf0,
    0xc9 => 0xcd01, 0xca => 0xcd08, 0xcb => 0xcd09, 0xcc => 0xcd0c,
    0xcd => 0xcd10, 0xce => 0xcd18, 0xcf => 0xcd19, 0xd0 => 0xcd1b,
    0xd1 => 0xcd1d, 0xd2 => 0xcd24, 0xd3 => 0xcd28, 0xd4 => 0xcd2c,
    0xd5 => 0xcd39, 0xd6 => 0xcd5c, 0xd7 => 0xcd60, 0xd8 => 0xcd64,
    0xd9 => 0xcd6c, 0xda => 0xcd6d, 0xdb => 0xcd6f, 0xdc => 0xcd71,
    0xdd => 0xcd78, 0xde => 0xcd88, 0xdf => 0xcd94, 0xe0 => 0xcd95,
    0xe1 => 0xcd98, 0xe2 => 0xcd9c, 0xe3 => 0xcda4, 0xe4 => 0xcda5,
    0xe5 => 0xcda7, 0xe6 => 0xcda9, 0xe7 => 0xcdb0, 0xe8 => 0xcdc4,
    0xe9 => 0xcdcc, 0xea => 0xcdd0, 0xeb => 0xcde8, 0xec => 0xcdec,
    0xed => 0xcdf0, 0xee => 0xcdf8, 0xef => 0xcdf9, 0xf0 => 0xcdfb,
    0xf1 => 0xcdfd, 0xf2 => 0xce04, 0xf3 => 0xce08, 0xf4 => 0xce0c,
    0xf5 => 0xce14, 0xf6 => 0xce19, 0xf7 => 0xce20, 0xf8 => 0xce21,
    0xf9 => 0xce24, 0xfa => 0xce28, 0xfb => 0xce30, 0xfc => 0xce31,
    0xfd => 0xce33, 0xfe => 0xce35,
  },
  0xc4 => {
    0xa1 => 0xce58, 0xa2 => 0xce59, 0xa3 => 0xce5c, 0xa4 => 0xce5f,
    0xa5 => 0xce60, 0xa6 => 0xce61, 0xa7 => 0xce68, 0xa8 => 0xce69,
    0xa9 => 0xce6b, 0xaa => 0xce6d, 0xab => 0xce74, 0xac => 0xce75,
    0xad => 0xce78, 0xae => 0xce7c, 0xaf => 0xce84, 0xb0 => 0xce85,
    0xb1 => 0xce87, 0xb2 => 0xce89, 0xb3 => 0xce90, 0xb4 => 0xce91,
    0xb5 => 0xce94, 0xb6 => 0xce98, 0xb7 => 0xcea0, 0xb8 => 0xcea1,
    0xb9 => 0xcea3, 0xba => 0xcea4, 0xbb => 0xcea5, 0xbc => 0xceac,
    0xbd => 0xcead, 0xbe => 0xcec1, 0xbf => 0xcee4, 0xc0 => 0xcee5,
    0xc1 => 0xcee8, 0xc2 => 0xceeb, 0xc3 => 0xceec, 0xc4 => 0xcef4,
    0xc5 => 0xcef5, 0xc6 => 0xcef7, 0xc7 => 0xcef8, 0xc8 => 0xcef9,
    0xc9 => 0xcf00, 0xca => 0xcf01, 0xcb => 0xcf04, 0xcc => 0xcf08,
    0xcd => 0xcf10, 0xce => 0xcf11, 0xcf => 0xcf13, 0xd0 => 0xcf15,
    0xd1 => 0xcf1c, 0xd2 => 0xcf20, 0xd3 => 0xcf24, 0xd4 => 0xcf2c,
    0xd5 => 0xcf2d, 0xd6 => 0xcf2f, 0xd7 => 0xcf30, 0xd8 => 0xcf31,
    0xd9 => 0xcf38, 0xda => 0xcf54, 0xdb => 0xcf55, 0xdc => 0xcf58,
    0xdd => 0xcf5c, 0xde => 0xcf64, 0xdf => 0xcf65, 0xe0 => 0xcf67,
    0xe1 => 0xcf69, 0xe2 => 0xcf70, 0xe3 => 0xcf71, 0xe4 => 0xcf74,
    0xe5 => 0xcf78, 0xe6 => 0xcf80, 0xe7 => 0xcf85, 0xe8 => 0xcf8c,
    0xe9 => 0xcfa1, 0xea => 0xcfa8, 0xeb => 0xcfb0, 0xec => 0xcfc4,
    0xed => 0xcfe0, 0xee => 0xcfe1, 0xef => 0xcfe4, 0xf0 => 0xcfe8,
    0xf1 => 0xcff0, 0xf2 => 0xcff1, 0xf3 => 0xcff3, 0xf4 => 0xcff5,
    0xf5 => 0xcffc, 0xf6 => 0xd000, 0xf7 => 0xd004, 0xf8 => 0xd011,
    0xf9 => 0xd018, 0xfa => 0xd02d, 0xfb => 0xd034, 0xfc => 0xd035,
    0xfd => 0xd038, 0xfe => 0xd03c,
  },
  0xc5 => {
    0xa1 => 0xd044, 0xa2 => 0xd045, 0xa3 => 0xd047, 0xa4 => 0xd049,
    0xa5 => 0xd050, 0xa6 => 0xd054, 0xa7 => 0xd058, 0xa8 => 0xd060,
    0xa9 => 0xd06c, 0xaa => 0xd06d, 0xab => 0xd070, 0xac => 0xd074,
    0xad => 0xd07c, 0xae => 0xd07d, 0xaf => 0xd081, 0xb0 => 0xd0a4,
    0xb1 => 0xd0a5, 0xb2 => 0xd0a8, 0xb3 => 0xd0ac, 0xb4 => 0xd0b4,
    0xb5 => 0xd0b5, 0xb6 => 0xd0b7, 0xb7 => 0xd0b9, 0xb8 => 0xd0c0,
    0xb9 => 0xd0c1, 0xba => 0xd0c4, 0xbb => 0xd0c8, 0xbc => 0xd0c9,
    0xbd => 0xd0d0, 0xbe => 0xd0d1, 0xbf => 0xd0d3, 0xc0 => 0xd0d4,
    0xc1 => 0xd0d5, 0xc2 => 0xd0dc, 0xc3 => 0xd0dd, 0xc4 => 0xd0e0,
    0xc5 => 0xd0e4, 0xc6 => 0xd0ec, 0xc7 => 0xd0ed, 0xc8 => 0xd0ef,
    0xc9 => 0xd0f0, 0xca => 0xd0f1, 0xcb => 0xd0f8, 0xcc => 0xd10d,
    0xcd => 0xd130, 0xce => 0xd131, 0xcf => 0xd134, 0xd0 => 0xd138,
    0xd1 => 0xd13a, 0xd2 => 0xd140, 0xd3 => 0xd141, 0xd4 => 0xd143,
    0xd5 => 0xd144, 0xd6 => 0xd145, 0xd7 => 0xd14c, 0xd8 => 0xd14d,
    0xd9 => 0xd150, 0xda => 0xd154, 0xdb => 0xd15c, 0xdc => 0xd15d,
    0xdd => 0xd15f, 0xde => 0xd161, 0xdf => 0xd168, 0xe0 => 0xd16c,
    0xe1 => 0xd17c, 0xe2 => 0xd184, 0xe3 => 0xd188, 0xe4 => 0xd1a0,
    0xe5 => 0xd1a1, 0xe6 => 0xd1a4, 0xe7 => 0xd1a8, 0xe8 => 0xd1b0,
    0xe9 => 0xd1b1, 0xea => 0xd1b3, 0xeb => 0xd1b5, 0xec => 0xd1ba,
    0xed => 0xd1bc, 0xee => 0xd1c0, 0xef => 0xd1d8, 0xf0 => 0xd1f4,
    0xf1 => 0xd1f8, 0xf2 => 0xd207, 0xf3 => 0xd209, 0xf4 => 0xd210,
    0xf5 => 0xd22c, 0xf6 => 0xd22d, 0xf7 => 0xd230, 0xf8 => 0xd234,
    0xf9 => 0xd23c, 0xfa => 0xd23d, 0xfb => 0xd23f, 0xfc => 0xd241,
    0xfd => 0xd248, 0xfe => 0xd25c,
  },
  0xc6 => {
    0xa1 => 0xd264, 0xa2 => 0xd280, 0xa3 => 0xd281, 0xa4 => 0xd284,
    0xa5 => 0xd288, 0xa6 => 0xd290, 0xa7 => 0xd291, 0xa8 => 0xd295,
    0xa9 => 0xd29c, 0xaa => 0xd2a0, 0xab => 0xd2a4, 0xac => 0xd2ac,
    0xad => 0xd2b1, 0xae => 0xd2b8, 0xaf => 0xd2b9, 0xb0 => 0xd2bc,
    0xb1 => 0xd2bf, 0xb2 => 0xd2c0, 0xb3 => 0xd2c2, 0xb4 => 0xd2c8,
    0xb5 => 0xd2c9, 0xb6 => 0xd2cb, 0xb7 => 0xd2d4, 0xb8 => 0xd2d8,
    0xb9 => 0xd2dc, 0xba => 0xd2e4, 0xbb => 0xd2e5, 0xbc => 0xd2f0,
    0xbd => 0xd2f1, 0xbe => 0xd2f4, 0xbf => 0xd2f8, 0xc0 => 0xd300,
    0xc1 => 0xd301, 0xc2 => 0xd303, 0xc3 => 0xd305, 0xc4 => 0xd30c,
    0xc5 => 0xd30d, 0xc6 => 0xd30e, 0xc7 => 0xd310, 0xc8 => 0xd314,
    0xc9 => 0xd316, 0xca => 0xd31c, 0xcb => 0xd31d, 0xcc => 0xd31f,
    0xcd => 0xd320, 0xce => 0xd321, 0xcf => 0xd325, 0xd0 => 0xd328,
    0xd1 => 0xd329, 0xd2 => 0xd32c, 0xd3 => 0xd330, 0xd4 => 0xd338,
    0xd5 => 0xd339, 0xd6 => 0xd33b, 0xd7 => 0xd33c, 0xd8 => 0xd33d,
    0xd9 => 0xd344, 0xda => 0xd345, 0xdb => 0xd37c, 0xdc => 0xd37d,
    0xdd => 0xd380, 0xde => 0xd384, 0xdf => 0xd38c, 0xe0 => 0xd38d,
    0xe1 => 0xd38f, 0xe2 => 0xd390, 0xe3 => 0xd391, 0xe4 => 0xd398,
    0xe5 => 0xd399, 0xe6 => 0xd39c, 0xe7 => 0xd3a0, 0xe8 => 0xd3a8,
    0xe9 => 0xd3a9, 0xea => 0xd3ab, 0xeb => 0xd3ad, 0xec => 0xd3b4,
    0xed => 0xd3b8, 0xee => 0xd3bc, 0xef => 0xd3c4, 0xf0 => 0xd3c5,
    0xf1 => 0xd3c8, 0xf2 => 0xd3c9, 0xf3 => 0xd3d0, 0xf4 => 0xd3d8,
    0xf5 => 0xd3e1, 0xf6 => 0xd3e3, 0xf7 => 0xd3ec, 0xf8 => 0xd3ed,
    0xf9 => 0xd3f0, 0xfa => 0xd3f4, 0xfb => 0xd3fc, 0xfc => 0xd3fd,
    0xfd => 0xd3ff, 0xfe => 0xd401,
  },
  0xc7 => {
    0xa1 => 0xd408, 0xa2 => 0xd41d, 0xa3 => 0xd440, 0xa4 => 0xd444,
    0xa5 => 0xd45c, 0xa6 => 0xd460, 0xa7 => 0xd464, 0xa8 => 0xd46d,
    0xa9 => 0xd46f, 0xaa => 0xd478, 0xab => 0xd479, 0xac => 0xd47c,
    0xad => 0xd47f, 0xae => 0xd480, 0xaf => 0xd482, 0xb0 => 0xd488,
    0xb1 => 0xd489, 0xb2 => 0xd48b, 0xb3 => 0xd48d, 0xb4 => 0xd494,
    0xb5 => 0xd4a9, 0xb6 => 0xd4cc, 0xb7 => 0xd4d0, 0xb8 => 0xd4d4,
    0xb9 => 0xd4dc, 0xba => 0xd4df, 0xbb => 0xd4e8, 0xbc => 0xd4ec,
    0xbd => 0xd4f0, 0xbe => 0xd4f8, 0xbf => 0xd4fb, 0xc0 => 0xd4fd,
    0xc1 => 0xd504, 0xc2 => 0xd508, 0xc3 => 0xd50c, 0xc4 => 0xd514,
    0xc5 => 0xd515, 0xc6 => 0xd517, 0xc7 => 0xd53c, 0xc8 => 0xd53d,
    0xc9 => 0xd540, 0xca => 0xd544, 0xcb => 0xd54c, 0xcc => 0xd54d,
    0xcd => 0xd54f, 0xce => 0xd551, 0xcf => 0xd558, 0xd0 => 0xd559,
    0xd1 => 0xd55c, 0xd2 => 0xd560, 0xd3 => 0xd565, 0xd4 => 0xd568,
    0xd5 => 0xd569, 0xd6 => 0xd56b, 0xd7 => 0xd56d, 0xd8 => 0xd574,
    0xd9 => 0xd575, 0xda => 0xd578, 0xdb => 0xd57c, 0xdc => 0xd584,
    0xdd => 0xd585, 0xde => 0xd587, 0xdf => 0xd588, 0xe0 => 0xd589,
    0xe1 => 0xd590, 0xe2 => 0xd5a5, 0xe3 => 0xd5c8, 0xe4 => 0xd5c9,
    0xe5 => 0xd5cc, 0xe6 => 0xd5d0, 0xe7 => 0xd5d2, 0xe8 => 0xd5d8,
    0xe9 => 0xd5d9, 0xea => 0xd5db, 0xeb => 0xd5dd, 0xec => 0xd5e4,
    0xed => 0xd5e5, 0xee => 0xd5e8, 0xef => 0xd5ec, 0xf0 => 0xd5f4,
    0xf1 => 0xd5f5, 0xf2 => 0xd5f7, 0xf3 => 0xd5f9, 0xf4 => 0xd600,
    0xf5 => 0xd601, 0xf6 => 0xd604, 0xf7 => 0xd608, 0xf8 => 0xd610,
    0xf9 => 0xd611, 0xfa => 0xd613, 0xfb => 0xd614, 0xfc => 0xd615,
    0xfd => 0xd61c, 0xfe => 0xd620,
  },
  0xc8 => {
    0xa1 => 0xd624, 0xa2 => 0xd62d, 0xa3 => 0xd638, 0xa4 => 0xd639,
    0xa5 => 0xd63c, 0xa6 => 0xd640, 0xa7 => 0xd645, 0xa8 => 0xd648,
    0xa9 => 0xd649, 0xaa => 0xd64b, 0xab => 0xd64d, 0xac => 0xd651,
    0xad => 0xd654, 0xae => 0xd655, 0xaf => 0xd658, 0xb0 => 0xd65c,
    0xb1 => 0xd667, 0xb2 => 0xd669, 0xb3 => 0xd670, 0xb4 => 0xd671,
    0xb5 => 0xd674, 0xb6 => 0xd683, 0xb7 => 0xd685, 0xb8 => 0xd68c,
    0xb9 => 0xd68d, 0xba => 0xd690, 0xbb => 0xd694, 0xbc => 0xd69d,
    0xbd => 0xd69f, 0xbe => 0xd6a1, 0xbf => 0xd6a8, 0xc0 => 0xd6ac,
    0xc1 => 0xd6b0, 0xc2 => 0xd6b9, 0xc3 => 0xd6bb, 0xc4 => 0xd6c4,
    0xc5 => 0xd6c5, 0xc6 => 0xd6c8, 0xc7 => 0xd6cc, 0xc8 => 0xd6d1,
    0xc9 => 0xd6d4, 0xca => 0xd6d7, 0xcb => 0xd6d9, 0xcc => 0xd6e0,
    0xcd => 0xd6e4, 0xce => 0xd6e8, 0xcf => 0xd6f0, 0xd0 => 0xd6f5,
    0xd1 => 0xd6fc, 0xd2 => 0xd6fd, 0xd3 => 0xd700, 0xd4 => 0xd704,
    0xd5 => 0xd711, 0xd6 => 0xd718, 0xd7 => 0xd719, 0xd8 => 0xd71c,
    0xd9 => 0xd720, 0xda => 0xd728, 0xdb => 0xd729, 0xdc => 0xd72b,
    0xdd => 0xd72d, 0xde => 0xd734, 0xdf => 0xd735, 0xe0 => 0xd738,
    0xe1 => 0xd73c, 0xe2 => 0xd744, 0xe3 => 0xd747, 0xe4 => 0xd749,
    0xe5 => 0xd750, 0xe6 => 0xd751, 0xe7 => 0xd754, 0xe8 => 0xd756,
    0xe9 => 0xd757, 0xea => 0xd758, 0xeb => 0xd759, 0xec => 0xd760,
    0xed => 0xd761, 0xee => 0xd763, 0xef => 0xd765, 0xf0 => 0xd769,
    0xf1 => 0xd76c, 0xf2 => 0xd770, 0xf3 => 0xd774, 0xf4 => 0xd77c,
    0xf5 => 0xd77d, 0xf6 => 0xd781, 0xf7 => 0xd788, 0xf8 => 0xd789,
    0xf9 => 0xd78c, 0xfa => 0xd790, 0xfb => 0xd798, 0xfc => 0xd799,
    0xfd => 0xd79b, 0xfe => 0xd79d,
  },
  0xca => {
    0xa1 => 0x4f3d, 0xa2 => 0x4f73, 0xa3 => 0x5047, 0xa4 => 0x50f9,
    0xa5 => 0x52a0, 0xa6 => 0x53ef, 0xa7 => 0x5475, 0xa8 => 0x54e5,
    0xa9 => 0x5609, 0xaa => 0x5ac1, 0xab => 0x5bb6, 0xac => 0x6687,
    0xad => 0x67b6, 0xae => 0x67b7, 0xaf => 0x67ef, 0xb0 => 0x6b4c,
    0xb1 => 0x73c2, 0xb2 => 0x75c2, 0xb3 => 0x7a3c, 0xb4 => 0x82db,
    0xb5 => 0x8304, 0xb6 => 0x8857, 0xb7 => 0x8888, 0xb8 => 0x8a36,
    0xb9 => 0x8cc8, 0xba => 0x8dcf, 0xbb => 0x8efb, 0xbc => 0x8fe6,
    0xbd => 0x99d5, 0xbe => 0x523b, 0xbf => 0x5374, 0xc0 => 0x5404,
    0xc1 => 0x606a, 0xc2 => 0x6164, 0xc3 => 0x6bbc, 0xc4 => 0x73cf,
    0xc5 => 0x811a, 0xc6 => 0x89ba, 0xc7 => 0x89d2, 0xc8 => 0x95a3,
    0xc9 => 0x4f83, 0xca => 0x520a, 0xcb => 0x58be, 0xcc => 0x5978,
    0xcd => 0x59e6, 0xce => 0x5e72, 0xcf => 0x5e79, 0xd0 => 0x61c7,
    0xd1 => 0x63c0, 0xd2 => 0x6746, 0xd3 => 0x67ec, 0xd4 => 0x687f,
    0xd5 => 0x6f97, 0xd6 => 0x764e, 0xd7 => 0x770b, 0xd8 => 0x78f5,
    0xd9 => 0x7a08, 0xda => 0x7aff, 0xdb => 0x7c21, 0xdc => 0x809d,
    0xdd => 0x826e, 0xde => 0x8271, 0xdf => 0x8aeb, 0xe0 => 0x9593,
    0xe1 => 0x4e6b, 0xe2 => 0x559d, 0xe3 => 0x66f7, 0xe4 => 0x6e34,
    0xe5 => 0x78a3, 0xe6 => 0x7aed, 0xe7 => 0x845b, 0xe8 => 0x8910,
    0xe9 => 0x874e, 0xea => 0x97a8, 0xeb => 0x52d8, 0xec => 0x574e,
    0xed => 0x582a, 0xee => 0x5d4c, 0xef => 0x611f, 0xf0 => 0x61be,
    0xf1 => 0x6221, 0xf2 => 0x6562, 0xf3 => 0x67d1, 0xf4 => 0x6a44,
    0xf5 => 0x6e1b, 0xf6 => 0x7518, 0xf7 => 0x75b3, 0xf8 => 0x76e3,
    0xf9 => 0x77b0, 0xfa => 0x7d3a, 0xfb => 0x90af, 0xfc => 0x9451,
    0xfd => 0x9452, 0xfe => 0x9f95,
  },
  0xcb => {
    0xa1 => 0x5323, 0xa2 => 0x5cac, 0xa3 => 0x7532, 0xa4 => 0x80db,
    0xa5 => 0x9240, 0xa6 => 0x9598, 0xa7 => 0x525b, 0xa8 => 0x5808,
    0xa9 => 0x59dc, 0xaa => 0x5ca1, 0xab => 0x5d17, 0xac => 0x5eb7,
    0xad => 0x5f3a, 0xae => 0x5f4a, 0xaf => 0x6177, 0xb0 => 0x6c5f,
    0xb1 => 0x757a, 0xb2 => 0x7586, 0xb3 => 0x7ce0, 0xb4 => 0x7d73,
    0xb5 => 0x7db1, 0xb6 => 0x7f8c, 0xb7 => 0x8154, 0xb8 => 0x8221,
    0xb9 => 0x8591, 0xba => 0x8941, 0xbb => 0x8b1b, 0xbc => 0x92fc,
    0xbd => 0x964d, 0xbe => 0x9c47, 0xbf => 0x4ecb, 0xc0 => 0x4ef7,
    0xc1 => 0x500b, 0xc2 => 0x51f1, 0xc3 => 0x584f, 0xc4 => 0x6137,
    0xc5 => 0x613e, 0xc6 => 0x6168, 0xc7 => 0x6539, 0xc8 => 0x69ea,
    0xc9 => 0x6f11, 0xca => 0x75a5, 0xcb => 0x7686, 0xcc => 0x76d6,
    0xcd => 0x7b87, 0xce => 0x82a5, 0xcf => 0x84cb, 0xd0 => 0xf900,
    0xd1 => 0x93a7, 0xd2 => 0x958b, 0xd3 => 0x5580, 0xd4 => 0x5ba2,
    0xd5 => 0x5751, 0xd6 => 0xf901, 0xd7 => 0x7cb3, 0xd8 => 0x7fb9,
    0xd9 => 0x91b5, 0xda => 0x5028, 0xdb => 0x53bb, 0xdc => 0x5c45,
    0xdd => 0x5de8, 0xde => 0x62d2, 0xdf => 0x636e, 0xe0 => 0x64da,
    0xe1 => 0x64e7, 0xe2 => 0x6e20, 0xe3 => 0x70ac, 0xe4 => 0x795b,
    0xe5 => 0x8ddd, 0xe6 => 0x8e1e, 0xe7 => 0xf902, 0xe8 => 0x907d,
    0xe9 => 0x9245, 0xea => 0x92f8, 0xeb => 0x4e7e, 0xec => 0x4ef6,
    0xed => 0x5065, 0xee => 0x5dfe, 0xef => 0x5efa, 0xf0 => 0x6106,
    0xf1 => 0x6957, 0xf2 => 0x8171, 0xf3 => 0x8654, 0xf4 => 0x8e47,
    0xf5 => 0x9375, 0xf6 => 0x9a2b, 0xf7 => 0x4e5e, 0xf8 => 0x5091,
    0xf9 => 0x6770, 0xfa => 0x6840, 0xfb => 0x5109, 0xfc => 0x528d,
    0xfd => 0x5292, 0xfe => 0x6aa2,
  },
  0xcc => {
    0xa1 => 0x77bc, 0xa2 => 0x9210, 0xa3 => 0x9ed4, 0xa4 => 0x52ab,
    0xa5 => 0x602f, 0xa6 => 0x8ff2, 0xa7 => 0x5048, 0xa8 => 0x61a9,
    0xa9 => 0x63ed, 0xaa => 0x64ca, 0xab => 0x683c, 0xac => 0x6a84,
    0xad => 0x6fc0, 0xae => 0x8188, 0xaf => 0x89a1, 0xb0 => 0x9694,
    0xb1 => 0x5805, 0xb2 => 0x727d, 0xb3 => 0x72ac, 0xb4 => 0x7504,
    0xb5 => 0x7d79, 0xb6 => 0x7e6d, 0xb7 => 0x80a9, 0xb8 => 0x898b,
    0xb9 => 0x8b74, 0xba => 0x9063, 0xbb => 0x9d51, 0xbc => 0x6289,
    0xbd => 0x6c7a, 0xbe => 0x6f54, 0xbf => 0x7d50, 0xc0 => 0x7f3a,
    0xc1 => 0x8a23, 0xc2 => 0x517c, 0xc3 => 0x614a, 0xc4 => 0x7b9d,
    0xc5 => 0x8b19, 0xc6 => 0x9257, 0xc7 => 0x938c, 0xc8 => 0x4eac,
    0xc9 => 0x4fd3, 0xca => 0x501e, 0xcb => 0x50be, 0xcc => 0x5106,
    0xcd => 0x52c1, 0xce => 0x52cd, 0xcf => 0x537f, 0xd0 => 0x5770,
    0xd1 => 0x5883, 0xd2 => 0x5e9a, 0xd3 => 0x5f91, 0xd4 => 0x6176,
    0xd5 => 0x61ac, 0xd6 => 0x64ce, 0xd7 => 0x656c, 0xd8 => 0x666f,
    0xd9 => 0x66bb, 0xda => 0x66f4, 0xdb => 0x6897, 0xdc => 0x6d87,
    0xdd => 0x7085, 0xde => 0x70f1, 0xdf => 0x749f, 0xe0 => 0x74a5,
    0xe1 => 0x74ca, 0xe2 => 0x75d9, 0xe3 => 0x786c, 0xe4 => 0x78ec,
    0xe5 => 0x7adf, 0xe6 => 0x7af6, 0xe7 => 0x7d45, 0xe8 => 0x7d93,
    0xe9 => 0x8015, 0xea => 0x803f, 0xeb => 0x811b, 0xec => 0x8396,
    0xed => 0x8b66, 0xee => 0x8f15, 0xef => 0x9015, 0xf0 => 0x93e1,
    0xf1 => 0x9803, 0xf2 => 0x9838, 0xf3 => 0x9a5a, 0xf4 => 0x9be8,
    0xf5 => 0x4fc2, 0xf6 => 0x5553, 0xf7 => 0x583a, 0xf8 => 0x5951,
    0xf9 => 0x5b63, 0xfa => 0x5c46, 0xfb => 0x60b8, 0xfc => 0x6212,
    0xfd => 0x6842, 0xfe => 0x68b0,
  },
  0xcd => {
    0xa1 => 0x68e8, 0xa2 => 0x6eaa, 0xa3 => 0x754c, 0xa4 => 0x7678,
    0xa5 => 0x78ce, 0xa6 => 0x7a3d, 0xa7 => 0x7cfb, 0xa8 => 0x7e6b,
    0xa9 => 0x7e7c, 0xaa => 0x8a08, 0xab => 0x8aa1, 0xac => 0x8c3f,
    0xad => 0x968e, 0xae => 0x9dc4, 0xaf => 0x53e4, 0xb0 => 0x53e9,
    0xb1 => 0x544a, 0xb2 => 0x5471, 0xb3 => 0x56fa, 0xb4 => 0x59d1,
    0xb5 => 0x5b64, 0xb6 => 0x5c3b, 0xb7 => 0x5eab, 0xb8 => 0x62f7,
    0xb9 => 0x6537, 0xba => 0x6545, 0xbb => 0x6572, 0xbc => 0x66a0,
    0xbd => 0x67af, 0xbe => 0x69c1, 0xbf => 0x6cbd, 0xc0 => 0x75fc,
    0xc1 => 0x7690, 0xc2 => 0x777e, 0xc3 => 0x7a3f, 0xc4 => 0x7f94,
    0xc5 => 0x8003, 0xc6 => 0x80a1, 0xc7 => 0x818f, 0xc8 => 0x82e6,
    0xc9 => 0x82fd, 0xca => 0x83f0, 0xcb => 0x85c1, 0xcc => 0x8831,
    0xcd => 0x88b4, 0xce => 0x8aa5, 0xcf => 0xf903, 0xd0 => 0x8f9c,
    0xd1 => 0x932e, 0xd2 => 0x96c7, 0xd3 => 0x9867, 0xd4 => 0x9ad8,
    0xd5 => 0x9f13, 0xd6 => 0x54ed, 0xd7 => 0x659b, 0xd8 => 0x66f2,
    0xd9 => 0x688f, 0xda => 0x7a40, 0xdb => 0x8c37, 0xdc => 0x9d60,
    0xdd => 0x56f0, 0xde => 0x5764, 0xdf => 0x5d11, 0xe0 => 0x6606,
    0xe1 => 0x68b1, 0xe2 => 0x68cd, 0xe3 => 0x6efe, 0xe4 => 0x7428,
    0xe5 => 0x889e, 0xe6 => 0x9be4, 0xe7 => 0x6c68, 0xe8 => 0xf904,
    0xe9 => 0x9aa8, 0xea => 0x4f9b, 0xeb => 0x516c, 0xec => 0x5171,
    0xed => 0x529f, 0xee => 0x5b54, 0xef => 0x5de5, 0xf0 => 0x6050,
    0xf1 => 0x606d, 0xf2 => 0x62f1, 0xf3 => 0x63a7, 0xf4 => 0x653b,
    0xf5 => 0x73d9, 0xf6 => 0x7a7a, 0xf7 => 0x86a3, 0xf8 => 0x8ca2,
    0xf9 => 0x978f, 0xfa => 0x4e32, 0xfb => 0x5be1, 0xfc => 0x6208,
    0xfd => 0x679c, 0xfe => 0x74dc,
  },
  0xce => {
    0xa1 => 0x79d1, 0xa2 => 0x83d3, 0xa3 => 0x8a87, 0xa4 => 0x8ab2,
    0xa5 => 0x8de8, 0xa6 => 0x904e, 0xa7 => 0x934b, 0xa8 => 0x9846,
    0xa9 => 0x5ed3, 0xaa => 0x69e8, 0xab => 0x85ff, 0xac => 0x90ed,
    0xad => 0xf905, 0xae => 0x51a0, 0xaf => 0x5b98, 0xb0 => 0x5bec,
    0xb1 => 0x6163, 0xb2 => 0x68fa, 0xb3 => 0x6b3e, 0xb4 => 0x704c,
    0xb5 => 0x742f, 0xb6 => 0x74d8, 0xb7 => 0x7ba1, 0xb8 => 0x7f50,
    0xb9 => 0x83c5, 0xba => 0x89c0, 0xbb => 0x8cab, 0xbc => 0x95dc,
    0xbd => 0x9928, 0xbe => 0x522e, 0xbf => 0x605d, 0xc0 => 0x62ec,
    0xc1 => 0x9002, 0xc2 => 0x4f8a, 0xc3 => 0x5149, 0xc4 => 0x5321,
    0xc5 => 0x58d9, 0xc6 => 0x5ee3, 0xc7 => 0x66e0, 0xc8 => 0x6d38,
    0xc9 => 0x709a, 0xca => 0x72c2, 0xcb => 0x73d6, 0xcc => 0x7b50,
    0xcd => 0x80f1, 0xce => 0x945b, 0xcf => 0x5366, 0xd0 => 0x639b,
    0xd1 => 0x7f6b, 0xd2 => 0x4e56, 0xd3 => 0x5080, 0xd4 => 0x584a,
    0xd5 => 0x58de, 0xd6 => 0x602a, 0xd7 => 0x6127, 0xd8 => 0x62d0,
    0xd9 => 0x69d0, 0xda => 0x9b41, 0xdb => 0x5b8f, 0xdc => 0x7d18,
    0xdd => 0x80b1, 0xde => 0x8f5f, 0xdf => 0x4ea4, 0xe0 => 0x50d1,
    0xe1 => 0x54ac, 0xe2 => 0x55ac, 0xe3 => 0x5b0c, 0xe4 => 0x5da0,
    0xe5 => 0x5de7, 0xe6 => 0x652a, 0xe7 => 0x654e, 0xe8 => 0x6821,
    0xe9 => 0x6a4b, 0xea => 0x72e1, 0xeb => 0x768e, 0xec => 0x77ef,
    0xed => 0x7d5e, 0xee => 0x7ff9, 0xef => 0x81a0, 0xf0 => 0x854e,
    0xf1 => 0x86df, 0xf2 => 0x8f03, 0xf3 => 0x8f4e, 0xf4 => 0x90ca,
    0xf5 => 0x9903, 0xf6 => 0x9a55, 0xf7 => 0x9bab, 0xf8 => 0x4e18,
    0xf9 => 0x4e45, 0xfa => 0x4e5d, 0xfb => 0x4ec7, 0xfc => 0x4ff1,
    0xfd => 0x5177, 0xfe => 0x52fe,
  },
  0xcf => {
    0xa1 => 0x5340, 0xa2 => 0x53e3, 0xa3 => 0x53e5, 0xa4 => 0x548e,
    0xa5 => 0x5614, 0xa6 => 0x5775, 0xa7 => 0x57a2, 0xa8 => 0x5bc7,
    0xa9 => 0x5d87, 0xaa => 0x5ed0, 0xab => 0x61fc, 0xac => 0x62d8,
    0xad => 0x6551, 0xae => 0x67b8, 0xaf => 0x67e9, 0xb0 => 0x69cb,
    0xb1 => 0x6b50, 0xb2 => 0x6bc6, 0xb3 => 0x6bec, 0xb4 => 0x6c42,
    0xb5 => 0x6e9d, 0xb6 => 0x7078, 0xb7 => 0x72d7, 0xb8 => 0x7396,
    0xb9 => 0x7403, 0xba => 0x77bf, 0xbb => 0x77e9, 0xbc => 0x7a76,
    0xbd => 0x7d7f, 0xbe => 0x8009, 0xbf => 0x81fc, 0xc0 => 0x8205,
    0xc1 => 0x820a, 0xc2 => 0x82df, 0xc3 => 0x8862, 0xc4 => 0x8b33,
    0xc5 => 0x8cfc, 0xc6 => 0x8ec0, 0xc7 => 0x9011, 0xc8 => 0x90b1,
    0xc9 => 0x9264, 0xca => 0x92b6, 0xcb => 0x99d2, 0xcc => 0x9a45,
    0xcd => 0x9ce9, 0xce => 0x9dd7, 0xcf => 0x9f9c, 0xd0 => 0x570b,
    0xd1 => 0x5c40, 0xd2 => 0x83ca, 0xd3 => 0x97a0, 0xd4 => 0x97ab,
    0xd5 => 0x9eb4, 0xd6 => 0x541b, 0xd7 => 0x7a98, 0xd8 => 0x7fa4,
    0xd9 => 0x88d9, 0xda => 0x8ecd, 0xdb => 0x90e1, 0xdc => 0x5800,
    0xdd => 0x5c48, 0xde => 0x6398, 0xdf => 0x7a9f, 0xe0 => 0x5bae,
    0xe1 => 0x5f13, 0xe2 => 0x7a79, 0xe3 => 0x7aae, 0xe4 => 0x828e,
    0xe5 => 0x8eac, 0xe6 => 0x5026, 0xe7 => 0x5238, 0xe8 => 0x52f8,
    0xe9 => 0x5377, 0xea => 0x5708, 0xeb => 0x62f3, 0xec => 0x6372,
    0xed => 0x6b0a, 0xee => 0x6dc3, 0xef => 0x7737, 0xf0 => 0x53a5,
    0xf1 => 0x7357, 0xf2 => 0x8568, 0xf3 => 0x8e76, 0xf4 => 0x95d5,
    0xf5 => 0x673a, 0xf6 => 0x6ac3, 0xf7 => 0x6f70, 0xf8 => 0x8a6d,
    0xf9 => 0x8ecc, 0xfa => 0x994b, 0xfb => 0xf906, 0xfc => 0x6677,
    0xfd => 0x6b78, 0xfe => 0x8cb4,
  },
  0xd0 => {
    0xa1 => 0x9b3c, 0xa2 => 0xf907, 0xa3 => 0x53eb, 0xa4 => 0x572d,
    0xa5 => 0x594e, 0xa6 => 0x63c6, 0xa7 => 0x69fb, 0xa8 => 0x73ea,
    0xa9 => 0x7845, 0xaa => 0x7aba, 0xab => 0x7ac5, 0xac => 0x7cfe,
    0xad => 0x8475, 0xae => 0x898f, 0xaf => 0x8d73, 0xb0 => 0x9035,
    0xb1 => 0x95a8, 0xb2 => 0x52fb, 0xb3 => 0x5747, 0xb4 => 0x7547,
    0xb5 => 0x7b60, 0xb6 => 0x83cc, 0xb7 => 0x921e, 0xb8 => 0xf908,
    0xb9 => 0x6a58, 0xba => 0x514b, 0xbb => 0x524b, 0xbc => 0x5287,
    0xbd => 0x621f, 0xbe => 0x68d8, 0xbf => 0x6975, 0xc0 => 0x9699,
    0xc1 => 0x50c5, 0xc2 => 0x52a4, 0xc3 => 0x52e4, 0xc4 => 0x61c3,
    0xc5 => 0x65a4, 0xc6 => 0x6839, 0xc7 => 0x69ff, 0xc8 => 0x747e,
    0xc9 => 0x7b4b, 0xca => 0x82b9, 0xcb => 0x83eb, 0xcc => 0x89b2,
    0xcd => 0x8b39, 0xce => 0x8fd1, 0xcf => 0x9949, 0xd0 => 0xf909,
    0xd1 => 0x4eca, 0xd2 => 0x5997, 0xd3 => 0x64d2, 0xd4 => 0x6611,
    0xd5 => 0x6a8e, 0xd6 => 0x7434, 0xd7 => 0x7981, 0xd8 => 0x79bd,
    0xd9 => 0x82a9, 0xda => 0x887e, 0xdb => 0x887f, 0xdc => 0x895f,
    0xdd => 0xf90a, 0xde => 0x9326, 0xdf => 0x4f0b, 0xe0 => 0x53ca,
    0xe1 => 0x6025, 0xe2 => 0x6271, 0xe3 => 0x6c72, 0xe4 => 0x7d1a,
    0xe5 => 0x7d66, 0xe6 => 0x4e98, 0xe7 => 0x5162, 0xe8 => 0x77dc,
    0xe9 => 0x80af, 0xea => 0x4f01, 0xeb => 0x4f0e, 0xec => 0x5176,
    0xed => 0x5180, 0xee => 0x55dc, 0xef => 0x5668, 0xf0 => 0x573b,
    0xf1 => 0x57fa, 0xf2 => 0x57fc, 0xf3 => 0x5914, 0xf4 => 0x5947,
    0xf5 => 0x5993, 0xf6 => 0x5bc4, 0xf7 => 0x5c90, 0xf8 => 0x5d0e,
    0xf9 => 0x5df1, 0xfa => 0x5e7e, 0xfb => 0x5fcc, 0xfc => 0x6280,
    0xfd => 0x65d7, 0xfe => 0x65e3,
  },
  0xd1 => {
    0xa1 => 0x671e, 0xa2 => 0x671f, 0xa3 => 0x675e, 0xa4 => 0x68cb,
    0xa5 => 0x68c4, 0xa6 => 0x6a5f, 0xa7 => 0x6b3a, 0xa8 => 0x6c23,
    0xa9 => 0x6c7d, 0xaa => 0x6c82, 0xab => 0x6dc7, 0xac => 0x7398,
    0xad => 0x7426, 0xae => 0x742a, 0xaf => 0x7482, 0xb0 => 0x74a3,
    0xb1 => 0x7578, 0xb2 => 0x757f, 0xb3 => 0x7881, 0xb4 => 0x78ef,
    0xb5 => 0x7941, 0xb6 => 0x7947, 0xb7 => 0x7948, 0xb8 => 0x797a,
    0xb9 => 0x7b95, 0xba => 0x7d00, 0xbb => 0x7dba, 0xbc => 0x7f88,
    0xbd => 0x8006, 0xbe => 0x802d, 0xbf => 0x808c, 0xc0 => 0x8a18,
    0xc1 => 0x8b4f, 0xc2 => 0x8c48, 0xc3 => 0x8d77, 0xc4 => 0x9321,
    0xc5 => 0x9324, 0xc6 => 0x98e2, 0xc7 => 0x9951, 0xc8 => 0x9a0e,
    0xc9 => 0x9a0f, 0xca => 0x9a65, 0xcb => 0x9e92, 0xcc => 0x7dca,
    0xcd => 0x4f76, 0xce => 0x5409, 0xcf => 0x62ee, 0xd0 => 0x6854,
    0xd1 => 0x91d1, 0xd2 => 0x55ab, 0xd3 => 0x513a, 0xd4 => 0xf90b,
    0xd5 => 0xf90c, 0xd6 => 0x5a1c, 0xd7 => 0x61e6, 0xd8 => 0xf90d,
    0xd9 => 0x62cf, 0xda => 0x62ff, 0xdb => 0xf90e, 0xdc => 0xf90f,
    0xdd => 0xf910, 0xde => 0xf911, 0xdf => 0xf912, 0xe0 => 0xf913,
    0xe1 => 0x90a3, 0xe2 => 0xf914, 0xe3 => 0xf915, 0xe4 => 0xf916,
    0xe5 => 0xf917, 0xe6 => 0xf918, 0xe7 => 0x8afe, 0xe8 => 0xf919,
    0xe9 => 0xf91a, 0xea => 0xf91b, 0xeb => 0xf91c, 0xec => 0x6696,
    0xed => 0xf91d, 0xee => 0x7156, 0xef => 0xf91e, 0xf0 => 0xf91f,
    0xf1 => 0x96e3, 0xf2 => 0xf920, 0xf3 => 0x634f, 0xf4 => 0x637a,
    0xf5 => 0x5357, 0xf6 => 0xf921, 0xf7 => 0x678f, 0xf8 => 0x6960,
    0xf9 => 0x6e73, 0xfa => 0xf922, 0xfb => 0x7537, 0xfc => 0xf923,
    0xfd => 0xf924, 0xfe => 0xf925,
  },
  0xd2 => {
    0xa1 => 0x7d0d, 0xa2 => 0xf926, 0xa3 => 0xf927, 0xa4 => 0x8872,
    0xa5 => 0x56ca, 0xa6 => 0x5a18, 0xa7 => 0xf928, 0xa8 => 0xf929,
    0xa9 => 0xf92a, 0xaa => 0xf92b, 0xab => 0xf92c, 0xac => 0x4e43,
    0xad => 0xf92d, 0xae => 0x5167, 0xaf => 0x5948, 0xb0 => 0x67f0,
    0xb1 => 0x8010, 0xb2 => 0xf92e, 0xb3 => 0x5973, 0xb4 => 0x5e74,
    0xb5 => 0x649a, 0xb6 => 0x79ca, 0xb7 => 0x5ff5, 0xb8 => 0x606c,
    0xb9 => 0x62c8, 0xba => 0x637b, 0xbb => 0x5be7, 0xbc => 0x5bd7,
    0xbd => 0x52aa, 0xbe => 0xf92f, 0xbf => 0x5974, 0xc0 => 0x5f29,
    0xc1 => 0x6012, 0xc2 => 0xf930, 0xc3 => 0xf931, 0xc4 => 0xf932,
    0xc5 => 0x7459, 0xc6 => 0xf933, 0xc7 => 0xf934, 0xc8 => 0xf935,
    0xc9 => 0xf936, 0xca => 0xf937, 0xcb => 0xf938, 0xcc => 0x99d1,
    0xcd => 0xf939, 0xce => 0xf93a, 0xcf => 0xf93b, 0xd0 => 0xf93c,
    0xd1 => 0xf93d, 0xd2 => 0xf93e, 0xd3 => 0xf93f, 0xd4 => 0xf940,
    0xd5 => 0xf941, 0xd6 => 0xf942, 0xd7 => 0xf943, 0xd8 => 0x6fc3,
    0xd9 => 0xf944, 0xda => 0xf945, 0xdb => 0x81bf, 0xdc => 0x8fb2,
    0xdd => 0x60f1, 0xde => 0xf946, 0xdf => 0xf947, 0xe0 => 0x8166,
    0xe1 => 0xf948, 0xe2 => 0xf949, 0xe3 => 0x5c3f, 0xe4 => 0xf94a,
    0xe5 => 0xf94b, 0xe6 => 0xf94c, 0xe7 => 0xf94d, 0xe8 => 0xf94e,
    0xe9 => 0xf94f, 0xea => 0xf950, 0xeb => 0xf951, 0xec => 0x5ae9,
    0xed => 0x8a25, 0xee => 0x677b, 0xef => 0x7d10, 0xf0 => 0xf952,
    0xf1 => 0xf953, 0xf2 => 0xf954, 0xf3 => 0xf955, 0xf4 => 0xf956,
    0xf5 => 0xf957, 0xf6 => 0x80fd, 0xf7 => 0xf958, 0xf8 => 0xf959,
    0xf9 => 0x5c3c, 0xfa => 0x6ce5, 0xfb => 0x533f, 0xfc => 0x6eba,
    0xfd => 0x591a, 0xfe => 0x8336,
  },
  0xd3 => {
    0xa1 => 0x4e39, 0xa2 => 0x4eb6, 0xa3 => 0x4f46, 0xa4 => 0x55ae,
    0xa5 => 0x5718, 0xa6 => 0x58c7, 0xa7 => 0x5f56, 0xa8 => 0x65b7,
    0xa9 => 0x65e6, 0xaa => 0x6a80, 0xab => 0x6bb5, 0xac => 0x6e4d,
    0xad => 0x77ed, 0xae => 0x7aef, 0xaf => 0x7c1e, 0xb0 => 0x7dde,
    0xb1 => 0x86cb, 0xb2 => 0x8892, 0xb3 => 0x9132, 0xb4 => 0x935b,
    0xb5 => 0x64bb, 0xb6 => 0x6fbe, 0xb7 => 0x737a, 0xb8 => 0x75b8,
    0xb9 => 0x9054, 0xba => 0x5556, 0xbb => 0x574d, 0xbc => 0x61ba,
    0xbd => 0x64d4, 0xbe => 0x66c7, 0xbf => 0x6de1, 0xc0 => 0x6e5b,
    0xc1 => 0x6f6d, 0xc2 => 0x6fb9, 0xc3 => 0x75f0, 0xc4 => 0x8043,
    0xc5 => 0x81bd, 0xc6 => 0x8541, 0xc7 => 0x8983, 0xc8 => 0x8ac7,
    0xc9 => 0x8b5a, 0xca => 0x931f, 0xcb => 0x6c93, 0xcc => 0x7553,
    0xcd => 0x7b54, 0xce => 0x8e0f, 0xcf => 0x905d, 0xd0 => 0x5510,
    0xd1 => 0x5802, 0xd2 => 0x5858, 0xd3 => 0x5e62, 0xd4 => 0x6207,
    0xd5 => 0x649e, 0xd6 => 0x68e0, 0xd7 => 0x7576, 0xd8 => 0x7cd6,
    0xd9 => 0x87b3, 0xda => 0x9ee8, 0xdb => 0x4ee3, 0xdc => 0x5788,
    0xdd => 0x576e, 0xde => 0x5927, 0xdf => 0x5c0d, 0xe0 => 0x5cb1,
    0xe1 => 0x5e36, 0xe2 => 0x5f85, 0xe3 => 0x6234, 0xe4 => 0x64e1,
    0xe5 => 0x73b3, 0xe6 => 0x81fa, 0xe7 => 0x888b, 0xe8 => 0x8cb8,
    0xe9 => 0x968a, 0xea => 0x9edb, 0xeb => 0x5b85, 0xec => 0x5fb7,
    0xed => 0x60b3, 0xee => 0x5012, 0xef => 0x5200, 0xf0 => 0x5230,
    0xf1 => 0x5716, 0xf2 => 0x5835, 0xf3 => 0x5857, 0xf4 => 0x5c0e,
    0xf5 => 0x5c60, 0xf6 => 0x5cf6, 0xf7 => 0x5d8b, 0xf8 => 0x5ea6,
    0xf9 => 0x5f92, 0xfa => 0x60bc, 0xfb => 0x6311, 0xfc => 0x6389,
    0xfd => 0x6417, 0xfe => 0x6843,
  },
  0xd4 => {
    0xa1 => 0x68f9, 0xa2 => 0x6ac2, 0xa3 => 0x6dd8, 0xa4 => 0x6e21,
    0xa5 => 0x6ed4, 0xa6 => 0x6fe4, 0xa7 => 0x71fe, 0xa8 => 0x76dc,
    0xa9 => 0x7779, 0xaa => 0x79b1, 0xab => 0x7a3b, 0xac => 0x8404,
    0xad => 0x89a9, 0xae => 0x8ced, 0xaf => 0x8df3, 0xb0 => 0x8e48,
    0xb1 => 0x9003, 0xb2 => 0x9014, 0xb3 => 0x9053, 0xb4 => 0x90fd,
    0xb5 => 0x934d, 0xb6 => 0x9676, 0xb7 => 0x97dc, 0xb8 => 0x6bd2,
    0xb9 => 0x7006, 0xba => 0x7258, 0xbb => 0x72a2, 0xbc => 0x7368,
    0xbd => 0x7763, 0xbe => 0x79bf, 0xbf => 0x7be4, 0xc0 => 0x7e9b,
    0xc1 => 0x8b80, 0xc2 => 0x58a9, 0xc3 => 0x60c7, 0xc4 => 0x6566,
    0xc5 => 0x65fd, 0xc6 => 0x66be, 0xc7 => 0x6c8c, 0xc8 => 0x711e,
    0xc9 => 0x71c9, 0xca => 0x8c5a, 0xcb => 0x9813, 0xcc => 0x4e6d,
    0xcd => 0x7a81, 0xce => 0x4edd, 0xcf => 0x51ac, 0xd0 => 0x51cd,
    0xd1 => 0x52d5, 0xd2 => 0x540c, 0xd3 => 0x61a7, 0xd4 => 0x6771,
    0xd5 => 0x6850, 0xd6 => 0x68df, 0xd7 => 0x6d1e, 0xd8 => 0x6f7c,
    0xd9 => 0x75bc, 0xda => 0x77b3, 0xdb => 0x7ae5, 0xdc => 0x80f4,
    0xdd => 0x8463, 0xde => 0x9285, 0xdf => 0x515c, 0xe0 => 0x6597,
    0xe1 => 0x675c, 0xe2 => 0x6793, 0xe3 => 0x75d8, 0xe4 => 0x7ac7,
    0xe5 => 0x8373, 0xe6 => 0xf95a, 0xe7 => 0x8c46, 0xe8 => 0x9017,
    0xe9 => 0x982d, 0xea => 0x5c6f, 0xeb => 0x81c0, 0xec => 0x829a,
    0xed => 0x9041, 0xee => 0x906f, 0xef => 0x920d, 0xf0 => 0x5f97,
    0xf1 => 0x5d9d, 0xf2 => 0x6a59, 0xf3 => 0x71c8, 0xf4 => 0x767b,
    0xf5 => 0x7b49, 0xf6 => 0x85e4, 0xf7 => 0x8b04, 0xf8 => 0x9127,
    0xf9 => 0x9a30, 0xfa => 0x5587, 0xfb => 0x61f6, 0xfc => 0xf95b,
    0xfd => 0x7669, 0xfe => 0x7f85,
  },
  0xd5 => {
    0xa1 => 0x863f, 0xa2 => 0x87ba, 0xa3 => 0x88f8, 0xa4 => 0x908f,
    0xa5 => 0xf95c, 0xa6 => 0x6d1b, 0xa7 => 0x70d9, 0xa8 => 0x73de,
    0xa9 => 0x7d61, 0xaa => 0x843d, 0xab => 0xf95d, 0xac => 0x916a,
    0xad => 0x99f1, 0xae => 0xf95e, 0xaf => 0x4e82, 0xb0 => 0x5375,
    0xb1 => 0x6b04, 0xb2 => 0x6b12, 0xb3 => 0x703e, 0xb4 => 0x721b,
    0xb5 => 0x862d, 0xb6 => 0x9e1e, 0xb7 => 0x524c, 0xb8 => 0x8fa3,
    0xb9 => 0x5d50, 0xba => 0x64e5, 0xbb => 0x652c, 0xbc => 0x6b16,
    0xbd => 0x6feb, 0xbe => 0x7c43, 0xbf => 0x7e9c, 0xc0 => 0x85cd,
    0xc1 => 0x8964, 0xc2 => 0x89bd, 0xc3 => 0x62c9, 0xc4 => 0x81d8,
    0xc5 => 0x881f, 0xc6 => 0x5eca, 0xc7 => 0x6717, 0xc8 => 0x6d6a,
    0xc9 => 0x72fc, 0xca => 0x7405, 0xcb => 0x746f, 0xcc => 0x8782,
    0xcd => 0x90de, 0xce => 0x4f86, 0xcf => 0x5d0d, 0xd0 => 0x5fa0,
    0xd1 => 0x840a, 0xd2 => 0x51b7, 0xd3 => 0x63a0, 0xd4 => 0x7565,
    0xd5 => 0x4eae, 0xd6 => 0x5006, 0xd7 => 0x5169, 0xd8 => 0x51c9,
    0xd9 => 0x6881, 0xda => 0x6a11, 0xdb => 0x7cae, 0xdc => 0x7cb1,
    0xdd => 0x7ce7, 0xde => 0x826f, 0xdf => 0x8ad2, 0xe0 => 0x8f1b,
    0xe1 => 0x91cf, 0xe2 => 0x4fb6, 0xe3 => 0x5137, 0xe4 => 0x52f5,
    0xe5 => 0x5442, 0xe6 => 0x5eec, 0xe7 => 0x616e, 0xe8 => 0x623e,
    0xe9 => 0x65c5, 0xea => 0x6ada, 0xeb => 0x6ffe, 0xec => 0x792a,
    0xed => 0x85dc, 0xee => 0x8823, 0xef => 0x95ad, 0xf0 => 0x9a62,
    0xf1 => 0x9a6a, 0xf2 => 0x9e97, 0xf3 => 0x9ece, 0xf4 => 0x529b,
    0xf5 => 0x66c6, 0xf6 => 0x6b77, 0xf7 => 0x701d, 0xf8 => 0x792b,
    0xf9 => 0x8f62, 0xfa => 0x9742, 0xfb => 0x6190, 0xfc => 0x6200,
    0xfd => 0x6523, 0xfe => 0x6f23,
  },
  0xd6 => {
    0xa1 => 0x7149, 0xa2 => 0x7489, 0xa3 => 0x7df4, 0xa4 => 0x806f,
    0xa5 => 0x84ee, 0xa6 => 0x8f26, 0xa7 => 0x9023, 0xa8 => 0x934a,
    0xa9 => 0x51bd, 0xaa => 0x5217, 0xab => 0x52a3, 0xac => 0x6d0c,
    0xad => 0x70c8, 0xae => 0x88c2, 0xaf => 0x5ec9, 0xb0 => 0x6582,
    0xb1 => 0x6bae, 0xb2 => 0x6fc2, 0xb3 => 0x7c3e, 0xb4 => 0x7375,
    0xb5 => 0x4ee4, 0xb6 => 0x4f36, 0xb7 => 0x56f9, 0xb8 => 0xf95f,
    0xb9 => 0x5cba, 0xba => 0x5dba, 0xbb => 0x601c, 0xbc => 0x73b2,
    0xbd => 0x7b2d, 0xbe => 0x7f9a, 0xbf => 0x7fce, 0xc0 => 0x8046,
    0xc1 => 0x901e, 0xc2 => 0x9234, 0xc3 => 0x96f6, 0xc4 => 0x9748,
    0xc5 => 0x9818, 0xc6 => 0x9f61, 0xc7 => 0x4f8b, 0xc8 => 0x6fa7,
    0xc9 => 0x79ae, 0xca => 0x91b4, 0xcb => 0x96b7, 0xcc => 0x52de,
    0xcd => 0xf960, 0xce => 0x6488, 0xcf => 0x64c4, 0xd0 => 0x6ad3,
    0xd1 => 0x6f5e, 0xd2 => 0x7018, 0xd3 => 0x7210, 0xd4 => 0x76e7,
    0xd5 => 0x8001, 0xd6 => 0x8606, 0xd7 => 0x865c, 0xd8 => 0x8def,
    0xd9 => 0x8f05, 0xda => 0x9732, 0xdb => 0x9b6f, 0xdc => 0x9dfa,
    0xdd => 0x9e75, 0xde => 0x788c, 0xdf => 0x797f, 0xe0 => 0x7da0,
    0xe1 => 0x83c9, 0xe2 => 0x9304, 0xe3 => 0x9e7f, 0xe4 => 0x9e93,
    0xe5 => 0x8ad6, 0xe6 => 0x58df, 0xe7 => 0x5f04, 0xe8 => 0x6727,
    0xe9 => 0x7027, 0xea => 0x74cf, 0xeb => 0x7c60, 0xec => 0x807e,
    0xed => 0x5121, 0xee => 0x7028, 0xef => 0x7262, 0xf0 => 0x78ca,
    0xf1 => 0x8cc2, 0xf2 => 0x8cda, 0xf3 => 0x8cf4, 0xf4 => 0x96f7,
    0xf5 => 0x4e86, 0xf6 => 0x50da, 0xf7 => 0x5bee, 0xf8 => 0x5ed6,
    0xf9 => 0x6599, 0xfa => 0x71ce, 0xfb => 0x7642, 0xfc => 0x77ad,
    0xfd => 0x804a, 0xfe => 0x84fc,
  },
  0xd7 => {
    0xa1 => 0x907c, 0xa2 => 0x9b27, 0xa3 => 0x9f8d, 0xa4 => 0x58d8,
    0xa5 => 0x5a41, 0xa6 => 0x5c62, 0xa7 => 0x6a13, 0xa8 => 0x6dda,
    0xa9 => 0x6f0f, 0xaa => 0x763b, 0xab => 0x7d2f, 0xac => 0x7e37,
    0xad => 0x851e, 0xae => 0x8938, 0xaf => 0x93e4, 0xb0 => 0x964b,
    0xb1 => 0x5289, 0xb2 => 0x65d2, 0xb3 => 0x67f3, 0xb4 => 0x69b4,
    0xb5 => 0x6d41, 0xb6 => 0x6e9c, 0xb7 => 0x700f, 0xb8 => 0x7409,
    0xb9 => 0x7460, 0xba => 0x7559, 0xbb => 0x7624, 0xbc => 0x786b,
    0xbd => 0x8b2c, 0xbe => 0x985e, 0xbf => 0x516d, 0xc0 => 0x622e,
    0xc1 => 0x9678, 0xc2 => 0x4f96, 0xc3 => 0x502b, 0xc4 => 0x5d19,
    0xc5 => 0x6dea, 0xc6 => 0x7db8, 0xc7 => 0x8f2a, 0xc8 => 0x5f8b,
    0xc9 => 0x6144, 0xca => 0x6817, 0xcb => 0xf961, 0xcc => 0x9686,
    0xcd => 0x52d2, 0xce => 0x808b, 0xcf => 0x51dc, 0xd0 => 0x51cc,
    0xd1 => 0x695e, 0xd2 => 0x7a1c, 0xd3 => 0x7dbe, 0xd4 => 0x83f1,
    0xd5 => 0x9675, 0xd6 => 0x4fda, 0xd7 => 0x5229, 0xd8 => 0x5398,
    0xd9 => 0x540f, 0xda => 0x550e, 0xdb => 0x5c65, 0xdc => 0x60a7,
    0xdd => 0x674e, 0xde => 0x68a8, 0xdf => 0x6d6c, 0xe0 => 0x7281,
    0xe1 => 0x72f8, 0xe2 => 0x7406, 0xe3 => 0x7483, 0xe4 => 0xf962,
    0xe5 => 0x75e2, 0xe6 => 0x7c6c, 0xe7 => 0x7f79, 0xe8 => 0x7fb8,
    0xe9 => 0x8389, 0xea => 0x88cf, 0xeb => 0x88e1, 0xec => 0x91cc,
    0xed => 0x91d0, 0xee => 0x96e2, 0xef => 0x9bc9, 0xf0 => 0x541d,
    0xf1 => 0x6f7e, 0xf2 => 0x71d0, 0xf3 => 0x7498, 0xf4 => 0x85fa,
    0xf5 => 0x8eaa, 0xf6 => 0x96a3, 0xf7 => 0x9c57, 0xf8 => 0x9e9f,
    0xf9 => 0x6797, 0xfa => 0x6dcb, 0xfb => 0x7433, 0xfc => 0x81e8,
    0xfd => 0x9716, 0xfe => 0x782c,
  },
  0xd8 => {
    0xa1 => 0x7acb, 0xa2 => 0x7b20, 0xa3 => 0x7c92, 0xa4 => 0x6469,
    0xa5 => 0x746a, 0xa6 => 0x75f2, 0xa7 => 0x78bc, 0xa8 => 0x78e8,
    0xa9 => 0x99ac, 0xaa => 0x9b54, 0xab => 0x9ebb, 0xac => 0x5bde,
    0xad => 0x5e55, 0xae => 0x6f20, 0xaf => 0x819c, 0xb0 => 0x83ab,
    0xb1 => 0x9088, 0xb2 => 0x4e07, 0xb3 => 0x534d, 0xb4 => 0x5a29,
    0xb5 => 0x5dd2, 0xb6 => 0x5f4e, 0xb7 => 0x6162, 0xb8 => 0x633d,
    0xb9 => 0x6669, 0xba => 0x66fc, 0xbb => 0x6eff, 0xbc => 0x6f2b,
    0xbd => 0x7063, 0xbe => 0x779e, 0xbf => 0x842c, 0xc0 => 0x8513,
    0xc1 => 0x883b, 0xc2 => 0x8f13, 0xc3 => 0x9945, 0xc4 => 0x9c3b,
    0xc5 => 0x551c, 0xc6 => 0x62b9, 0xc7 => 0x672b, 0xc8 => 0x6cab,
    0xc9 => 0x8309, 0xca => 0x896a, 0xcb => 0x977a, 0xcc => 0x4ea1,
    0xcd => 0x5984, 0xce => 0x5fd8, 0xcf => 0x5fd9, 0xd0 => 0x671b,
    0xd1 => 0x7db2, 0xd2 => 0x7f54, 0xd3 => 0x8292, 0xd4 => 0x832b,
    0xd5 => 0x83bd, 0xd6 => 0x8f1e, 0xd7 => 0x9099, 0xd8 => 0x57cb,
    0xd9 => 0x59b9, 0xda => 0x5a92, 0xdb => 0x5bd0, 0xdc => 0x6627,
    0xdd => 0x679a, 0xde => 0x6885, 0xdf => 0x6bcf, 0xe0 => 0x7164,
    0xe1 => 0x7f75, 0xe2 => 0x8cb7, 0xe3 => 0x8ce3, 0xe4 => 0x9081,
    0xe5 => 0x9b45, 0xe6 => 0x8108, 0xe7 => 0x8c8a, 0xe8 => 0x964c,
    0xe9 => 0x9a40, 0xea => 0x9ea5, 0xeb => 0x5b5f, 0xec => 0x6c13,
    0xed => 0x731b, 0xee => 0x76f2, 0xef => 0x76df, 0xf0 => 0x840c,
    0xf1 => 0x51aa, 0xf2 => 0x8993, 0xf3 => 0x514d, 0xf4 => 0x5195,
    0xf5 => 0x52c9, 0xf6 => 0x68c9, 0xf7 => 0x6c94, 0xf8 => 0x7704,
    0xf9 => 0x7720, 0xfa => 0x7dbf, 0xfb => 0x7dec, 0xfc => 0x9762,
    0xfd => 0x9eb5, 0xfe => 0x6ec5,
  },
  0xd9 => {
    0xa1 => 0x8511, 0xa2 => 0x51a5, 0xa3 => 0x540d, 0xa4 => 0x547d,
    0xa5 => 0x660e, 0xa6 => 0x669d, 0xa7 => 0x6927, 0xa8 => 0x6e9f,
    0xa9 => 0x76bf, 0xaa => 0x7791, 0xab => 0x8317, 0xac => 0x84c2,
    0xad => 0x879f, 0xae => 0x9169, 0xaf => 0x9298, 0xb0 => 0x9cf4,
    0xb1 => 0x8882, 0xb2 => 0x4fae, 0xb3 => 0x5192, 0xb4 => 0x52df,
    0xb5 => 0x59c6, 0xb6 => 0x5e3d, 0xb7 => 0x6155, 0xb8 => 0x6478,
    0xb9 => 0x6479, 0xba => 0x66ae, 0xbb => 0x67d0, 0xbc => 0x6a21,
    0xbd => 0x6bcd, 0xbe => 0x6bdb, 0xbf => 0x725f, 0xc0 => 0x7261,
    0xc1 => 0x7441, 0xc2 => 0x7738, 0xc3 => 0x77db, 0xc4 => 0x8017,
    0xc5 => 0x82bc, 0xc6 => 0x8305, 0xc7 => 0x8b00, 0xc8 => 0x8b28,
    0xc9 => 0x8c8c, 0xca => 0x6728, 0xcb => 0x6c90, 0xcc => 0x7267,
    0xcd => 0x76ee, 0xce => 0x7766, 0xcf => 0x7a46, 0xd0 => 0x9da9,
    0xd1 => 0x6b7f, 0xd2 => 0x6c92, 0xd3 => 0x5922, 0xd4 => 0x6726,
    0xd5 => 0x8499, 0xd6 => 0x536f, 0xd7 => 0x5893, 0xd8 => 0x5999,
    0xd9 => 0x5edf, 0xda => 0x63cf, 0xdb => 0x6634, 0xdc => 0x6773,
    0xdd => 0x6e3a, 0xde => 0x732b, 0xdf => 0x7ad7, 0xe0 => 0x82d7,
    0xe1 => 0x9328, 0xe2 => 0x52d9, 0xe3 => 0x5deb, 0xe4 => 0x61ae,
    0xe5 => 0x61cb, 0xe6 => 0x620a, 0xe7 => 0x62c7, 0xe8 => 0x64ab,
    0xe9 => 0x65e0, 0xea => 0x6959, 0xeb => 0x6b66, 0xec => 0x6bcb,
    0xed => 0x7121, 0xee => 0x73f7, 0xef => 0x755d, 0xf0 => 0x7e46,
    0xf1 => 0x821e, 0xf2 => 0x8302, 0xf3 => 0x856a, 0xf4 => 0x8aa3,
    0xf5 => 0x8cbf, 0xf6 => 0x9727, 0xf7 => 0x9d61, 0xf8 => 0x58a8,
    0xf9 => 0x9ed8, 0xfa => 0x5011, 0xfb => 0x520e, 0xfc => 0x543b,
    0xfd => 0x554f, 0xfe => 0x6587,
  },
  0xda => {
    0xa1 => 0x6c76, 0xa2 => 0x7d0a, 0xa3 => 0x7d0b, 0xa4 => 0x805e,
    0xa5 => 0x868a, 0xa6 => 0x9580, 0xa7 => 0x96ef, 0xa8 => 0x52ff,
    0xa9 => 0x6c95, 0xaa => 0x7269, 0xab => 0x5473, 0xac => 0x5a9a,
    0xad => 0x5c3e, 0xae => 0x5d4b, 0xaf => 0x5f4c, 0xb0 => 0x5fae,
    0xb1 => 0x672a, 0xb2 => 0x68b6, 0xb3 => 0x6963, 0xb4 => 0x6e3c,
    0xb5 => 0x6e44, 0xb6 => 0x7709, 0xb7 => 0x7c73, 0xb8 => 0x7f8e,
    0xb9 => 0x8587, 0xba => 0x8b0e, 0xbb => 0x8ff7, 0xbc => 0x9761,
    0xbd => 0x9ef4, 0xbe => 0x5cb7, 0xbf => 0x60b6, 0xc0 => 0x610d,
    0xc1 => 0x61ab, 0xc2 => 0x654f, 0xc3 => 0x65fb, 0xc4 => 0x65fc,
    0xc5 => 0x6c11, 0xc6 => 0x6cef, 0xc7 => 0x739f, 0xc8 => 0x73c9,
    0xc9 => 0x7de1, 0xca => 0x9594, 0xcb => 0x5bc6, 0xcc => 0x871c,
    0xcd => 0x8b10, 0xce => 0x525d, 0xcf => 0x535a, 0xd0 => 0x62cd,
    0xd1 => 0x640f, 0xd2 => 0x64b2, 0xd3 => 0x6734, 0xd4 => 0x6a38,
    0xd5 => 0x6cca, 0xd6 => 0x73c0, 0xd7 => 0x749e, 0xd8 => 0x7b94,
    0xd9 => 0x7c95, 0xda => 0x7e1b, 0xdb => 0x818a, 0xdc => 0x8236,
    0xdd => 0x8584, 0xde => 0x8feb, 0xdf => 0x96f9, 0xe0 => 0x99c1,
    0xe1 => 0x4f34, 0xe2 => 0x534a, 0xe3 => 0x53cd, 0xe4 => 0x53db,
    0xe5 => 0x62cc, 0xe6 => 0x642c, 0xe7 => 0x6500, 0xe8 => 0x6591,
    0xe9 => 0x69c3, 0xea => 0x6cee, 0xeb => 0x6f58, 0xec => 0x73ed,
    0xed => 0x7554, 0xee => 0x7622, 0xef => 0x76e4, 0xf0 => 0x76fc,
    0xf1 => 0x78d0, 0xf2 => 0x78fb, 0xf3 => 0x792c, 0xf4 => 0x7d46,
    0xf5 => 0x822c, 0xf6 => 0x87e0, 0xf7 => 0x8fd4, 0xf8 => 0x9812,
    0xf9 => 0x98ef, 0xfa => 0x52c3, 0xfb => 0x62d4, 0xfc => 0x64a5,
    0xfd => 0x6e24, 0xfe => 0x6f51,
  },
  0xdb => {
    0xa1 => 0x767c, 0xa2 => 0x8dcb, 0xa3 => 0x91b1, 0xa4 => 0x9262,
    0xa5 => 0x9aee, 0xa6 => 0x9b43, 0xa7 => 0x5023, 0xa8 => 0x508d,
    0xa9 => 0x574a, 0xaa => 0x59a8, 0xab => 0x5c28, 0xac => 0x5e47,
    0xad => 0x5f77, 0xae => 0x623f, 0xaf => 0x653e, 0xb0 => 0x65b9,
    0xb1 => 0x65c1, 0xb2 => 0x6609, 0xb3 => 0x678b, 0xb4 => 0x699c,
    0xb5 => 0x6ec2, 0xb6 => 0x78c5, 0xb7 => 0x7d21, 0xb8 => 0x80aa,
    0xb9 => 0x8180, 0xba => 0x822b, 0xbb => 0x82b3, 0xbc => 0x84a1,
    0xbd => 0x868c, 0xbe => 0x8a2a, 0xbf => 0x8b17, 0xc0 => 0x90a6,
    0xc1 => 0x9632, 0xc2 => 0x9f90, 0xc3 => 0x500d, 0xc4 => 0x4ff3,
    0xc5 => 0xf963, 0xc6 => 0x57f9, 0xc7 => 0x5f98, 0xc8 => 0x62dc,
    0xc9 => 0x6392, 0xca => 0x676f, 0xcb => 0x6e43, 0xcc => 0x7119,
    0xcd => 0x76c3, 0xce => 0x80cc, 0xcf => 0x80da, 0xd0 => 0x88f4,
    0xd1 => 0x88f5, 0xd2 => 0x8919, 0xd3 => 0x8ce0, 0xd4 => 0x8f29,
    0xd5 => 0x914d, 0xd6 => 0x966a, 0xd7 => 0x4f2f, 0xd8 => 0x4f70,
    0xd9 => 0x5e1b, 0xda => 0x67cf, 0xdb => 0x6822, 0xdc => 0x767d,
    0xdd => 0x767e, 0xde => 0x9b44, 0xdf => 0x5e61, 0xe0 => 0x6a0a,
    0xe1 => 0x7169, 0xe2 => 0x71d4, 0xe3 => 0x756a, 0xe4 => 0xf964,
    0xe5 => 0x7e41, 0xe6 => 0x8543, 0xe7 => 0x85e9, 0xe8 => 0x98dc,
    0xe9 => 0x4f10, 0xea => 0x7b4f, 0xeb => 0x7f70, 0xec => 0x95a5,
    0xed => 0x51e1, 0xee => 0x5e06, 0xef => 0x68b5, 0xf0 => 0x6c3e,
    0xf1 => 0x6c4e, 0xf2 => 0x6cdb, 0xf3 => 0x72af, 0xf4 => 0x7bc4,
    0xf5 => 0x8303, 0xf6 => 0x6cd5, 0xf7 => 0x743a, 0xf8 => 0x50fb,
    0xf9 => 0x5288, 0xfa => 0x58c1, 0xfb => 0x64d8, 0xfc => 0x6a97,
    0xfd => 0x74a7, 0xfe => 0x7656,
  },
  0xdc => {
    0xa1 => 0x78a7, 0xa2 => 0x8617, 0xa3 => 0x95e2, 0xa4 => 0x9739,
    0xa5 => 0xf965, 0xa6 => 0x535e, 0xa7 => 0x5f01, 0xa8 => 0x8b8a,
    0xa9 => 0x8fa8, 0xaa => 0x8faf, 0xab => 0x908a, 0xac => 0x5225,
    0xad => 0x77a5, 0xae => 0x9c49, 0xaf => 0x9f08, 0xb0 => 0x4e19,
    0xb1 => 0x5002, 0xb2 => 0x5175, 0xb3 => 0x5c5b, 0xb4 => 0x5e77,
    0xb5 => 0x661e, 0xb6 => 0x663a, 0xb7 => 0x67c4, 0xb8 => 0x68c5,
    0xb9 => 0x70b3, 0xba => 0x7501, 0xbb => 0x75c5, 0xbc => 0x79c9,
    0xbd => 0x7add, 0xbe => 0x8f27, 0xbf => 0x9920, 0xc0 => 0x9a08,
    0xc1 => 0x4fdd, 0xc2 => 0x5821, 0xc3 => 0x5831, 0xc4 => 0x5bf6,
    0xc5 => 0x666e, 0xc6 => 0x6b65, 0xc7 => 0x6d11, 0xc8 => 0x6e7a,
    0xc9 => 0x6f7d, 0xca => 0x73e4, 0xcb => 0x752b, 0xcc => 0x83e9,
    0xcd => 0x88dc, 0xce => 0x8913, 0xcf => 0x8b5c, 0xd0 => 0x8f14,
    0xd1 => 0x4f0f, 0xd2 => 0x50d5, 0xd3 => 0x5310, 0xd4 => 0x535c,
    0xd5 => 0x5b93, 0xd6 => 0x5fa9, 0xd7 => 0x670d, 0xd8 => 0x798f,
    0xd9 => 0x8179, 0xda => 0x832f, 0xdb => 0x8514, 0xdc => 0x8907,
    0xdd => 0x8986, 0xde => 0x8f39, 0xdf => 0x8f3b, 0xe0 => 0x99a5,
    0xe1 => 0x9c12, 0xe2 => 0x672c, 0xe3 => 0x4e76, 0xe4 => 0x4ff8,
    0xe5 => 0x5949, 0xe6 => 0x5c01, 0xe7 => 0x5cef, 0xe8 => 0x5cf0,
    0xe9 => 0x6367, 0xea => 0x68d2, 0xeb => 0x70fd, 0xec => 0x71a2,
    0xed => 0x742b, 0xee => 0x7e2b, 0xef => 0x84ec, 0xf0 => 0x8702,
    0xf1 => 0x9022, 0xf2 => 0x92d2, 0xf3 => 0x9cf3, 0xf4 => 0x4e0d,
    0xf5 => 0x4ed8, 0xf6 => 0x4fef, 0xf7 => 0x5085, 0xf8 => 0x5256,
    0xf9 => 0x526f, 0xfa => 0x5426, 0xfb => 0x5490, 0xfc => 0x57e0,
    0xfd => 0x592b, 0xfe => 0x5a66,
  },
  0xdd => {
    0xa1 => 0x5b5a, 0xa2 => 0x5b75, 0xa3 => 0x5bcc, 0xa4 => 0x5e9c,
    0xa5 => 0xf966, 0xa6 => 0x6276, 0xa7 => 0x6577, 0xa8 => 0x65a7,
    0xa9 => 0x6d6e, 0xaa => 0x6ea5, 0xab => 0x7236, 0xac => 0x7b26,
    0xad => 0x7c3f, 0xae => 0x7f36, 0xaf => 0x8150, 0xb0 => 0x8151,
    0xb1 => 0x819a, 0xb2 => 0x8240, 0xb3 => 0x8299, 0xb4 => 0x83a9,
    0xb5 => 0x8a03, 0xb6 => 0x8ca0, 0xb7 => 0x8ce6, 0xb8 => 0x8cfb,
    0xb9 => 0x8d74, 0xba => 0x8dba, 0xbb => 0x90e8, 0xbc => 0x91dc,
    0xbd => 0x961c, 0xbe => 0x9644, 0xbf => 0x99d9, 0xc0 => 0x9ce7,
    0xc1 => 0x5317, 0xc2 => 0x5206, 0xc3 => 0x5429, 0xc4 => 0x5674,
    0xc5 => 0x58b3, 0xc6 => 0x5954, 0xc7 => 0x596e, 0xc8 => 0x5fff,
    0xc9 => 0x61a4, 0xca => 0x626e, 0xcb => 0x6610, 0xcc => 0x6c7e,
    0xcd => 0x711a, 0xce => 0x76c6, 0xcf => 0x7c89, 0xd0 => 0x7cde,
    0xd1 => 0x7d1b, 0xd2 => 0x82ac, 0xd3 => 0x8cc1, 0xd4 => 0x96f0,
    0xd5 => 0xf967, 0xd6 => 0x4f5b, 0xd7 => 0x5f17, 0xd8 => 0x5f7f,
    0xd9 => 0x62c2, 0xda => 0x5d29, 0xdb => 0x670b, 0xdc => 0x68da,
    0xdd => 0x787c, 0xde => 0x7e43, 0xdf => 0x9d6c, 0xe0 => 0x4e15,
    0xe1 => 0x5099, 0xe2 => 0x5315, 0xe3 => 0x532a, 0xe4 => 0x5351,
    0xe5 => 0x5983, 0xe6 => 0x5a62, 0xe7 => 0x5e87, 0xe8 => 0x60b2,
    0xe9 => 0x618a, 0xea => 0x6249, 0xeb => 0x6279, 0xec => 0x6590,
    0xed => 0x6787, 0xee => 0x69a7, 0xef => 0x6bd4, 0xf0 => 0x6bd6,
    0xf1 => 0x6bd7, 0xf2 => 0x6bd8, 0xf3 => 0x6cb8, 0xf4 => 0xf968,
    0xf5 => 0x7435, 0xf6 => 0x75fa, 0xf7 => 0x7812, 0xf8 => 0x7891,
    0xf9 => 0x79d5, 0xfa => 0x79d8, 0xfb => 0x7c83, 0xfc => 0x7dcb,
    0xfd => 0x7fe1, 0xfe => 0x80a5,
  },
  0xde => {
    0xa1 => 0x813e, 0xa2 => 0x81c2, 0xa3 => 0x83f2, 0xa4 => 0x871a,
    0xa5 => 0x88e8, 0xa6 => 0x8ab9, 0xa7 => 0x8b6c, 0xa8 => 0x8cbb,
    0xa9 => 0x9119, 0xaa => 0x975e, 0xab => 0x98db, 0xac => 0x9f3b,
    0xad => 0x56ac, 0xae => 0x5b2a, 0xaf => 0x5f6c, 0xb0 => 0x658c,
    0xb1 => 0x6ab3, 0xb2 => 0x6baf, 0xb3 => 0x6d5c, 0xb4 => 0x6ff1,
    0xb5 => 0x7015, 0xb6 => 0x725d, 0xb7 => 0x73ad, 0xb8 => 0x8ca7,
    0xb9 => 0x8cd3, 0xba => 0x983b, 0xbb => 0x6191, 0xbc => 0x6c37,
    0xbd => 0x8058, 0xbe => 0x9a01, 0xbf => 0x4e4d, 0xc0 => 0x4e8b,
    0xc1 => 0x4e9b, 0xc2 => 0x4ed5, 0xc3 => 0x4f3a, 0xc4 => 0x4f3c,
    0xc5 => 0x4f7f, 0xc6 => 0x4fdf, 0xc7 => 0x50ff, 0xc8 => 0x53f2,
    0xc9 => 0x53f8, 0xca => 0x5506, 0xcb => 0x55e3, 0xcc => 0x56db,
    0xcd => 0x58eb, 0xce => 0x5962, 0xcf => 0x5a11, 0xd0 => 0x5beb,
    0xd1 => 0x5bfa, 0xd2 => 0x5c04, 0xd3 => 0x5df3, 0xd4 => 0x5e2b,
    0xd5 => 0x5f99, 0xd6 => 0x601d, 0xd7 => 0x6368, 0xd8 => 0x659c,
    0xd9 => 0x65af, 0xda => 0x67f6, 0xdb => 0x67fb, 0xdc => 0x68ad,
    0xdd => 0x6b7b, 0xde => 0x6c99, 0xdf => 0x6cd7, 0xe0 => 0x6e23,
    0xe1 => 0x7009, 0xe2 => 0x7345, 0xe3 => 0x7802, 0xe4 => 0x793e,
    0xe5 => 0x7940, 0xe6 => 0x7960, 0xe7 => 0x79c1, 0xe8 => 0x7be9,
    0xe9 => 0x7d17, 0xea => 0x7d72, 0xeb => 0x8086, 0xec => 0x820d,
    0xed => 0x838e, 0xee => 0x84d1, 0xef => 0x86c7, 0xf0 => 0x88df,
    0xf1 => 0x8a50, 0xf2 => 0x8a5e, 0xf3 => 0x8b1d, 0xf4 => 0x8cdc,
    0xf5 => 0x8d66, 0xf6 => 0x8fad, 0xf7 => 0x90aa, 0xf8 => 0x98fc,
    0xf9 => 0x99df, 0xfa => 0x9e9d, 0xfb => 0x524a, 0xfc => 0xf969,
    0xfd => 0x6714, 0xfe => 0xf96a,
  },
  0xdf => {
    0xa1 => 0x5098, 0xa2 => 0x522a, 0xa3 => 0x5c71, 0xa4 => 0x6563,
    0xa5 => 0x6c55, 0xa6 => 0x73ca, 0xa7 => 0x7523, 0xa8 => 0x759d,
    0xa9 => 0x7b97, 0xaa => 0x849c, 0xab => 0x9178, 0xac => 0x9730,
    0xad => 0x4e77, 0xae => 0x6492, 0xaf => 0x6bba, 0xb0 => 0x715e,
    0xb1 => 0x85a9, 0xb2 => 0x4e09, 0xb3 => 0xf96b, 0xb4 => 0x6749,
    0xb5 => 0x68ee, 0xb6 => 0x6e17, 0xb7 => 0x829f, 0xb8 => 0x8518,
    0xb9 => 0x886b, 0xba => 0x63f7, 0xbb => 0x6f81, 0xbc => 0x9212,
    0xbd => 0x98af, 0xbe => 0x4e0a, 0xbf => 0x50b7, 0xc0 => 0x50cf,
    0xc1 => 0x511f, 0xc2 => 0x5546, 0xc3 => 0x55aa, 0xc4 => 0x5617,
    0xc5 => 0x5b40, 0xc6 => 0x5c19, 0xc7 => 0x5ce0, 0xc8 => 0x5e38,
    0xc9 => 0x5e8a, 0xca => 0x5ea0, 0xcb => 0x5ec2, 0xcc => 0x60f3,
    0xcd => 0x6851, 0xce => 0x6a61, 0xcf => 0x6e58, 0xd0 => 0x723d,
    0xd1 => 0x7240, 0xd2 => 0x72c0, 0xd3 => 0x76f8, 0xd4 => 0x7965,
    0xd5 => 0x7bb1, 0xd6 => 0x7fd4, 0xd7 => 0x88f3, 0xd8 => 0x89f4,
    0xd9 => 0x8a73, 0xda => 0x8c61, 0xdb => 0x8cde, 0xdc => 0x971c,
    0xdd => 0x585e, 0xde => 0x74bd, 0xdf => 0x8cfd, 0xe0 => 0x55c7,
    0xe1 => 0xf96c, 0xe2 => 0x7a61, 0xe3 => 0x7d22, 0xe4 => 0x8272,
    0xe5 => 0x7272, 0xe6 => 0x751f, 0xe7 => 0x7525, 0xe8 => 0xf96d,
    0xe9 => 0x7b19, 0xea => 0x5885, 0xeb => 0x58fb, 0xec => 0x5dbc,
    0xed => 0x5e8f, 0xee => 0x5eb6, 0xef => 0x5f90, 0xf0 => 0x6055,
    0xf1 => 0x6292, 0xf2 => 0x637f, 0xf3 => 0x654d, 0xf4 => 0x6691,
    0xf5 => 0x66d9, 0xf6 => 0x66f8, 0xf7 => 0x6816, 0xf8 => 0x68f2,
    0xf9 => 0x7280, 0xfa => 0x745e, 0xfb => 0x7b6e, 0xfc => 0x7d6e,
    0xfd => 0x7dd6, 0xfe => 0x7f72,
  },
  0xe0 => {
    0xa1 => 0x80e5, 0xa2 => 0x8212, 0xa3 => 0x85af, 0xa4 => 0x897f,
    0xa5 => 0x8a93, 0xa6 => 0x901d, 0xa7 => 0x92e4, 0xa8 => 0x9ecd,
    0xa9 => 0x9f20, 0xaa => 0x5915, 0xab => 0x596d, 0xac => 0x5e2d,
    0xad => 0x60dc, 0xae => 0x6614, 0xaf => 0x6673, 0xb0 => 0x6790,
    0xb1 => 0x6c50, 0xb2 => 0x6dc5, 0xb3 => 0x6f5f, 0xb4 => 0x77f3,
    0xb5 => 0x78a9, 0xb6 => 0x84c6, 0xb7 => 0x91cb, 0xb8 => 0x932b,
    0xb9 => 0x4ed9, 0xba => 0x50ca, 0xbb => 0x5148, 0xbc => 0x5584,
    0xbd => 0x5b0b, 0xbe => 0x5ba3, 0xbf => 0x6247, 0xc0 => 0x657e,
    0xc1 => 0x65cb, 0xc2 => 0x6e32, 0xc3 => 0x717d, 0xc4 => 0x7401,
    0xc5 => 0x7444, 0xc6 => 0x7487, 0xc7 => 0x74bf, 0xc8 => 0x766c,
    0xc9 => 0x79aa, 0xca => 0x7dda, 0xcb => 0x7e55, 0xcc => 0x7fa8,
    0xcd => 0x817a, 0xce => 0x81b3, 0xcf => 0x8239, 0xd0 => 0x861a,
    0xd1 => 0x87ec, 0xd2 => 0x8a75, 0xd3 => 0x8de3, 0xd4 => 0x9078,
    0xd5 => 0x9291, 0xd6 => 0x9425, 0xd7 => 0x994d, 0xd8 => 0x9bae,
    0xd9 => 0x5368, 0xda => 0x5c51, 0xdb => 0x6954, 0xdc => 0x6cc4,
    0xdd => 0x6d29, 0xde => 0x6e2b, 0xdf => 0x820c, 0xe0 => 0x859b,
    0xe1 => 0x893b, 0xe2 => 0x8a2d, 0xe3 => 0x8aaa, 0xe4 => 0x96ea,
    0xe5 => 0x9f67, 0xe6 => 0x5261, 0xe7 => 0x66b9, 0xe8 => 0x6bb2,
    0xe9 => 0x7e96, 0xea => 0x87fe, 0xeb => 0x8d0d, 0xec => 0x9583,
    0xed => 0x965d, 0xee => 0x651d, 0xef => 0x6d89, 0xf0 => 0x71ee,
    0xf1 => 0xf96e, 0xf2 => 0x57ce, 0xf3 => 0x59d3, 0xf4 => 0x5bac,
    0xf5 => 0x6027, 0xf6 => 0x60fa, 0xf7 => 0x6210, 0xf8 => 0x661f,
    0xf9 => 0x665f, 0xfa => 0x7329, 0xfb => 0x73f9, 0xfc => 0x76db,
    0xfd => 0x7701, 0xfe => 0x7b6c,
  },
  0xe1 => {
    0xa1 => 0x8056, 0xa2 => 0x8072, 0xa3 => 0x8165, 0xa4 => 0x8aa0,
    0xa5 => 0x9192, 0xa6 => 0x4e16, 0xa7 => 0x52e2, 0xa8 => 0x6b72,
    0xa9 => 0x6d17, 0xaa => 0x7a05, 0xab => 0x7b39, 0xac => 0x7d30,
    0xad => 0xf96f, 0xae => 0x8cb0, 0xaf => 0x53ec, 0xb0 => 0x562f,
    0xb1 => 0x5851, 0xb2 => 0x5bb5, 0xb3 => 0x5c0f, 0xb4 => 0x5c11,
    0xb5 => 0x5de2, 0xb6 => 0x6240, 0xb7 => 0x6383, 0xb8 => 0x6414,
    0xb9 => 0x662d, 0xba => 0x68b3, 0xbb => 0x6cbc, 0xbc => 0x6d88,
    0xbd => 0x6eaf, 0xbe => 0x701f, 0xbf => 0x70a4, 0xc0 => 0x71d2,
    0xc1 => 0x7526, 0xc2 => 0x758f, 0xc3 => 0x758e, 0xc4 => 0x7619,
    0xc5 => 0x7b11, 0xc6 => 0x7be0, 0xc7 => 0x7c2b, 0xc8 => 0x7d20,
    0xc9 => 0x7d39, 0xca => 0x852c, 0xcb => 0x856d, 0xcc => 0x8607,
    0xcd => 0x8a34, 0xce => 0x900d, 0xcf => 0x9061, 0xd0 => 0x90b5,
    0xd1 => 0x92b7, 0xd2 => 0x97f6, 0xd3 => 0x9a37, 0xd4 => 0x4fd7,
    0xd5 => 0x5c6c, 0xd6 => 0x675f, 0xd7 => 0x6d91, 0xd8 => 0x7c9f,
    0xd9 => 0x7e8c, 0xda => 0x8b16, 0xdb => 0x8d16, 0xdc => 0x901f,
    0xdd => 0x5b6b, 0xde => 0x5dfd, 0xdf => 0x640d, 0xe0 => 0x84c0,
    0xe1 => 0x905c, 0xe2 => 0x98e1, 0xe3 => 0x7387, 0xe4 => 0x5b8b,
    0xe5 => 0x609a, 0xe6 => 0x677e, 0xe7 => 0x6dde, 0xe8 => 0x8a1f,
    0xe9 => 0x8aa6, 0xea => 0x9001, 0xeb => 0x980c, 0xec => 0x5237,
    0xed => 0xf970, 0xee => 0x7051, 0xef => 0x788e, 0xf0 => 0x9396,
    0xf1 => 0x8870, 0xf2 => 0x91d7, 0xf3 => 0x4fee, 0xf4 => 0x53d7,
    0xf5 => 0x55fd, 0xf6 => 0x56da, 0xf7 => 0x5782, 0xf8 => 0x58fd,
    0xf9 => 0x5ac2, 0xfa => 0x5b88, 0xfb => 0x5cab, 0xfc => 0x5cc0,
    0xfd => 0x5e25, 0xfe => 0x6101,
  },
  0xe2 => {
    0xa1 => 0x620d, 0xa2 => 0x624b, 0xa3 => 0x6388, 0xa4 => 0x641c,
    0xa5 => 0x6536, 0xa6 => 0x6578, 0xa7 => 0x6a39, 0xa8 => 0x6b8a,
    0xa9 => 0x6c34, 0xaa => 0x6d19, 0xab => 0x6f31, 0xac => 0x71e7,
    0xad => 0x72e9, 0xae => 0x7378, 0xaf => 0x7407, 0xb0 => 0x74b2,
    0xb1 => 0x7626, 0xb2 => 0x7761, 0xb3 => 0x79c0, 0xb4 => 0x7a57,
    0xb5 => 0x7aea, 0xb6 => 0x7cb9, 0xb7 => 0x7d8f, 0xb8 => 0x7dac,
    0xb9 => 0x7e61, 0xba => 0x7f9e, 0xbb => 0x8129, 0xbc => 0x8331,
    0xbd => 0x8490, 0xbe => 0x84da, 0xbf => 0x85ea, 0xc0 => 0x8896,
    0xc1 => 0x8ab0, 0xc2 => 0x8b90, 0xc3 => 0x8f38, 0xc4 => 0x9042,
    0xc5 => 0x9083, 0xc6 => 0x916c, 0xc7 => 0x9296, 0xc8 => 0x92b9,
    0xc9 => 0x968b, 0xca => 0x96a7, 0xcb => 0x96a8, 0xcc => 0x96d6,
    0xcd => 0x9700, 0xce => 0x9808, 0xcf => 0x9996, 0xd0 => 0x9ad3,
    0xd1 => 0x9b1a, 0xd2 => 0x53d4, 0xd3 => 0x587e, 0xd4 => 0x5919,
    0xd5 => 0x5b70, 0xd6 => 0x5bbf, 0xd7 => 0x6dd1, 0xd8 => 0x6f5a,
    0xd9 => 0x719f, 0xda => 0x7421, 0xdb => 0x74b9, 0xdc => 0x8085,
    0xdd => 0x83fd, 0xde => 0x5de1, 0xdf => 0x5f87, 0xe0 => 0x5faa,
    0xe1 => 0x6042, 0xe2 => 0x65ec, 0xe3 => 0x6812, 0xe4 => 0x696f,
    0xe5 => 0x6a53, 0xe6 => 0x6b89, 0xe7 => 0x6d35, 0xe8 => 0x6df3,
    0xe9 => 0x73e3, 0xea => 0x76fe, 0xeb => 0x77ac, 0xec => 0x7b4d,
    0xed => 0x7d14, 0xee => 0x8123, 0xef => 0x821c, 0xf0 => 0x8340,
    0xf1 => 0x84f4, 0xf2 => 0x8563, 0xf3 => 0x8a62, 0xf4 => 0x8ac4,
    0xf5 => 0x9187, 0xf6 => 0x931e, 0xf7 => 0x9806, 0xf8 => 0x99b4,
    0xf9 => 0x620c, 0xfa => 0x8853, 0xfb => 0x8ff0, 0xfc => 0x9265,
    0xfd => 0x5d07, 0xfe => 0x5d27,
  },
  0xe3 => {
    0xa1 => 0x5d69, 0xa2 => 0x745f, 0xa3 => 0x819d, 0xa4 => 0x8768,
    0xa5 => 0x6fd5, 0xa6 => 0x62fe, 0xa7 => 0x7fd2, 0xa8 => 0x8936,
    0xa9 => 0x8972, 0xaa => 0x4e1e, 0xab => 0x4e58, 0xac => 0x50e7,
    0xad => 0x52dd, 0xae => 0x5347, 0xaf => 0x627f, 0xb0 => 0x6607,
    0xb1 => 0x7e69, 0xb2 => 0x8805, 0xb3 => 0x965e, 0xb4 => 0x4f8d,
    0xb5 => 0x5319, 0xb6 => 0x5636, 0xb7 => 0x59cb, 0xb8 => 0x5aa4,
    0xb9 => 0x5c38, 0xba => 0x5c4e, 0xbb => 0x5c4d, 0xbc => 0x5e02,
    0xbd => 0x5f11, 0xbe => 0x6043, 0xbf => 0x65bd, 0xc0 => 0x662f,
    0xc1 => 0x6642, 0xc2 => 0x67be, 0xc3 => 0x67f4, 0xc4 => 0x731c,
    0xc5 => 0x77e2, 0xc6 => 0x793a, 0xc7 => 0x7fc5, 0xc8 => 0x8494,
    0xc9 => 0x84cd, 0xca => 0x8996, 0xcb => 0x8a66, 0xcc => 0x8a69,
    0xcd => 0x8ae1, 0xce => 0x8c55, 0xcf => 0x8c7a, 0xd0 => 0x57f4,
    0xd1 => 0x5bd4, 0xd2 => 0x5f0f, 0xd3 => 0x606f, 0xd4 => 0x62ed,
    0xd5 => 0x690d, 0xd6 => 0x6b96, 0xd7 => 0x6e5c, 0xd8 => 0x7184,
    0xd9 => 0x7bd2, 0xda => 0x8755, 0xdb => 0x8b58, 0xdc => 0x8efe,
    0xdd => 0x98df, 0xde => 0x98fe, 0xdf => 0x4f38, 0xe0 => 0x4f81,
    0xe1 => 0x4fe1, 0xe2 => 0x547b, 0xe3 => 0x5a20, 0xe4 => 0x5bb8,
    0xe5 => 0x613c, 0xe6 => 0x65b0, 0xe7 => 0x6668, 0xe8 => 0x71fc,
    0xe9 => 0x7533, 0xea => 0x795e, 0xeb => 0x7d33, 0xec => 0x814e,
    0xed => 0x81e3, 0xee => 0x8398, 0xef => 0x85aa, 0xf0 => 0x85ce,
    0xf1 => 0x8703, 0xf2 => 0x8a0a, 0xf3 => 0x8eab, 0xf4 => 0x8f9b,
    0xf5 => 0xf971, 0xf6 => 0x8fc5, 0xf7 => 0x5931, 0xf8 => 0x5ba4,
    0xf9 => 0x5be6, 0xfa => 0x6089, 0xfb => 0x5be9, 0xfc => 0x5c0b,
    0xfd => 0x5fc3, 0xfe => 0x6c81,
  },
  0xe4 => {
    0xa1 => 0xf972, 0xa2 => 0x6df1, 0xa3 => 0x700b, 0xa4 => 0x751a,
    0xa5 => 0x82af, 0xa6 => 0x8af6, 0xa7 => 0x4ec0, 0xa8 => 0x5341,
    0xa9 => 0xf973, 0xaa => 0x96d9, 0xab => 0x6c0f, 0xac => 0x4e9e,
    0xad => 0x4fc4, 0xae => 0x5152, 0xaf => 0x555e, 0xb0 => 0x5a25,
    0xb1 => 0x5ce8, 0xb2 => 0x6211, 0xb3 => 0x7259, 0xb4 => 0x82bd,
    0xb5 => 0x83aa, 0xb6 => 0x86fe, 0xb7 => 0x8859, 0xb8 => 0x8a1d,
    0xb9 => 0x963f, 0xba => 0x96c5, 0xbb => 0x9913, 0xbc => 0x9d09,
    0xbd => 0x9d5d, 0xbe => 0x580a, 0xbf => 0x5cb3, 0xc0 => 0x5dbd,
    0xc1 => 0x5e44, 0xc2 => 0x60e1, 0xc3 => 0x6115, 0xc4 => 0x63e1,
    0xc5 => 0x6a02, 0xc6 => 0x6e25, 0xc7 => 0x9102, 0xc8 => 0x9354,
    0xc9 => 0x984e, 0xca => 0x9c10, 0xcb => 0x9f77, 0xcc => 0x5b89,
    0xcd => 0x5cb8, 0xce => 0x6309, 0xcf => 0x664f, 0xd0 => 0x6848,
    0xd1 => 0x773c, 0xd2 => 0x96c1, 0xd3 => 0x978d, 0xd4 => 0x9854,
    0xd5 => 0x9b9f, 0xd6 => 0x65a1, 0xd7 => 0x8b01, 0xd8 => 0x8ecb,
    0xd9 => 0x95bc, 0xda => 0x5535, 0xdb => 0x5ca9, 0xdc => 0x5dd6,
    0xdd => 0x5eb5, 0xde => 0x6697, 0xdf => 0x764c, 0xe0 => 0x83f4,
    0xe1 => 0x95c7, 0xe2 => 0x58d3, 0xe3 => 0x62bc, 0xe4 => 0x72ce,
    0xe5 => 0x9d28, 0xe6 => 0x4ef0, 0xe7 => 0x592e, 0xe8 => 0x600f,
    0xe9 => 0x663b, 0xea => 0x6b83, 0xeb => 0x79e7, 0xec => 0x9d26,
    0xed => 0x5393, 0xee => 0x54c0, 0xef => 0x57c3, 0xf0 => 0x5d16,
    0xf1 => 0x611b, 0xf2 => 0x66d6, 0xf3 => 0x6daf, 0xf4 => 0x788d,
    0xf5 => 0x827e, 0xf6 => 0x9698, 0xf7 => 0x9744, 0xf8 => 0x5384,
    0xf9 => 0x627c, 0xfa => 0x6396, 0xfb => 0x6db2, 0xfc => 0x7e0a,
    0xfd => 0x814b, 0xfe => 0x984d,
  },
  0xe5 => {
    0xa1 => 0x6afb, 0xa2 => 0x7f4c, 0xa3 => 0x9daf, 0xa4 => 0x9e1a,
    0xa5 => 0x4e5f, 0xa6 => 0x503b, 0xa7 => 0x51b6, 0xa8 => 0x591c,
    0xa9 => 0x60f9, 0xaa => 0x63f6, 0xab => 0x6930, 0xac => 0x723a,
    0xad => 0x8036, 0xae => 0xf974, 0xaf => 0x91ce, 0xb0 => 0x5f31,
    0xb1 => 0xf975, 0xb2 => 0xf976, 0xb3 => 0x7d04, 0xb4 => 0x82e5,
    0xb5 => 0x846f, 0xb6 => 0x84bb, 0xb7 => 0x85e5, 0xb8 => 0x8e8d,
    0xb9 => 0xf977, 0xba => 0x4f6f, 0xbb => 0xf978, 0xbc => 0xf979,
    0xbd => 0x58e4, 0xbe => 0x5b43, 0xbf => 0x6059, 0xc0 => 0x63da,
    0xc1 => 0x6518, 0xc2 => 0x656d, 0xc3 => 0x6698, 0xc4 => 0xf97a,
    0xc5 => 0x694a, 0xc6 => 0x6a23, 0xc7 => 0x6d0b, 0xc8 => 0x7001,
    0xc9 => 0x716c, 0xca => 0x75d2, 0xcb => 0x760d, 0xcc => 0x79b3,
    0xcd => 0x7a70, 0xce => 0xf97b, 0xcf => 0x7f8a, 0xd0 => 0xf97c,
    0xd1 => 0x8944, 0xd2 => 0xf97d, 0xd3 => 0x8b93, 0xd4 => 0x91c0,
    0xd5 => 0x967d, 0xd6 => 0xf97e, 0xd7 => 0x990a, 0xd8 => 0x5704,
    0xd9 => 0x5fa1, 0xda => 0x65bc, 0xdb => 0x6f01, 0xdc => 0x7600,
    0xdd => 0x79a6, 0xde => 0x8a9e, 0xdf => 0x99ad, 0xe0 => 0x9b5a,
    0xe1 => 0x9f6c, 0xe2 => 0x5104, 0xe3 => 0x61b6, 0xe4 => 0x6291,
    0xe5 => 0x6a8d, 0xe6 => 0x81c6, 0xe7 => 0x5043, 0xe8 => 0x5830,
    0xe9 => 0x5f66, 0xea => 0x7109, 0xeb => 0x8a00, 0xec => 0x8afa,
    0xed => 0x5b7c, 0xee => 0x8616, 0xef => 0x4ffa, 0xf0 => 0x513c,
    0xf1 => 0x56b4, 0xf2 => 0x5944, 0xf3 => 0x63a9, 0xf4 => 0x6df9,
    0xf5 => 0x5daa, 0xf6 => 0x696d, 0xf7 => 0x5186, 0xf8 => 0x4e88,
    0xf9 => 0x4f59, 0xfa => 0xf97f, 0xfb => 0xf980, 0xfc => 0xf981,
    0xfd => 0x5982, 0xfe => 0xf982,
  },
  0xe6 => {
    0xa1 => 0xf983, 0xa2 => 0x6b5f, 0xa3 => 0x6c5d, 0xa4 => 0xf984,
    0xa5 => 0x74b5, 0xa6 => 0x7916, 0xa7 => 0xf985, 0xa8 => 0x8207,
    0xa9 => 0x8245, 0xaa => 0x8339, 0xab => 0x8f3f, 0xac => 0x8f5d,
    0xad => 0xf986, 0xae => 0x9918, 0xaf => 0xf987, 0xb0 => 0xf988,
    0xb1 => 0xf989, 0xb2 => 0x4ea6, 0xb3 => 0xf98a, 0xb4 => 0x57df,
    0xb5 => 0x5f79, 0xb6 => 0x6613, 0xb7 => 0xf98b, 0xb8 => 0xf98c,
    0xb9 => 0x75ab, 0xba => 0x7e79, 0xbb => 0x8b6f, 0xbc => 0xf98d,
    0xbd => 0x9006, 0xbe => 0x9a5b, 0xbf => 0x56a5, 0xc0 => 0x5827,
    0xc1 => 0x59f8, 0xc2 => 0x5a1f, 0xc3 => 0x5bb4, 0xc4 => 0xf98e,
    0xc5 => 0x5ef6, 0xc6 => 0xf98f, 0xc7 => 0xf990, 0xc8 => 0x6350,
    0xc9 => 0x633b, 0xca => 0xf991, 0xcb => 0x693d, 0xcc => 0x6c87,
    0xcd => 0x6cbf, 0xce => 0x6d8e, 0xcf => 0x6d93, 0xd0 => 0x6df5,
    0xd1 => 0x6f14, 0xd2 => 0xf992, 0xd3 => 0x70df, 0xd4 => 0x7136,
    0xd5 => 0x7159, 0xd6 => 0xf993, 0xd7 => 0x71c3, 0xd8 => 0x71d5,
    0xd9 => 0xf994, 0xda => 0x784f, 0xdb => 0x786f, 0xdc => 0xf995,
    0xdd => 0x7b75, 0xde => 0x7de3, 0xdf => 0xf996, 0xe0 => 0x7e2f,
    0xe1 => 0xf997, 0xe2 => 0x884d, 0xe3 => 0x8edf, 0xe4 => 0xf998,
    0xe5 => 0xf999, 0xe6 => 0xf99a, 0xe7 => 0x925b, 0xe8 => 0xf99b,
    0xe9 => 0x9cf6, 0xea => 0xf99c, 0xeb => 0xf99d, 0xec => 0xf99e,
    0xed => 0x6085, 0xee => 0x6d85, 0xef => 0xf99f, 0xf0 => 0x71b1,
    0xf1 => 0xf9a0, 0xf2 => 0xf9a1, 0xf3 => 0x95b1, 0xf4 => 0x53ad,
    0xf5 => 0xf9a2, 0xf6 => 0xf9a3, 0xf7 => 0xf9a4, 0xf8 => 0x67d3,
    0xf9 => 0xf9a5, 0xfa => 0x708e, 0xfb => 0x7130, 0xfc => 0x7430,
    0xfd => 0x8276, 0xfe => 0x82d2,
  },
  0xe7 => {
    0xa1 => 0xf9a6, 0xa2 => 0x95bb, 0xa3 => 0x9ae5, 0xa4 => 0x9e7d,
    0xa5 => 0x66c4, 0xa6 => 0xf9a7, 0xa7 => 0x71c1, 0xa8 => 0x8449,
    0xa9 => 0xf9a8, 0xaa => 0xf9a9, 0xab => 0x584b, 0xac => 0xf9aa,
    0xad => 0xf9ab, 0xae => 0x5db8, 0xaf => 0x5f71, 0xb0 => 0xf9ac,
    0xb1 => 0x6620, 0xb2 => 0x668e, 0xb3 => 0x6979, 0xb4 => 0x69ae,
    0xb5 => 0x6c38, 0xb6 => 0x6cf3, 0xb7 => 0x6e36, 0xb8 => 0x6f41,
    0xb9 => 0x6fda, 0xba => 0x701b, 0xbb => 0x702f, 0xbc => 0x7150,
    0xbd => 0x71df, 0xbe => 0x7370, 0xbf => 0xf9ad, 0xc0 => 0x745b,
    0xc1 => 0xf9ae, 0xc2 => 0x74d4, 0xc3 => 0x76c8, 0xc4 => 0x7a4e,
    0xc5 => 0x7e93, 0xc6 => 0xf9af, 0xc7 => 0xf9b0, 0xc8 => 0x82f1,
    0xc9 => 0x8a60, 0xca => 0x8fce, 0xcb => 0xf9b1, 0xcc => 0x9348,
    0xcd => 0xf9b2, 0xce => 0x9719, 0xcf => 0xf9b3, 0xd0 => 0xf9b4,
    0xd1 => 0x4e42, 0xd2 => 0x502a, 0xd3 => 0xf9b5, 0xd4 => 0x5208,
    0xd5 => 0x53e1, 0xd6 => 0x66f3, 0xd7 => 0x6c6d, 0xd8 => 0x6fca,
    0xd9 => 0x730a, 0xda => 0x777f, 0xdb => 0x7a62, 0xdc => 0x82ae,
    0xdd => 0x85dd, 0xde => 0x8602, 0xdf => 0xf9b6, 0xe0 => 0x88d4,
    0xe1 => 0x8a63, 0xe2 => 0x8b7d, 0xe3 => 0x8c6b, 0xe4 => 0xf9b7,
    0xe5 => 0x92b3, 0xe6 => 0xf9b8, 0xe7 => 0x9713, 0xe8 => 0x9810,
    0xe9 => 0x4e94, 0xea => 0x4f0d, 0xeb => 0x4fc9, 0xec => 0x50b2,
    0xed => 0x5348, 0xee => 0x543e, 0xef => 0x5433, 0xf0 => 0x55da,
    0xf1 => 0x5862, 0xf2 => 0x58ba, 0xf3 => 0x5967, 0xf4 => 0x5a1b,
    0xf5 => 0x5be4, 0xf6 => 0x609f, 0xf7 => 0xf9b9, 0xf8 => 0x61ca,
    0xf9 => 0x6556, 0xfa => 0x65ff, 0xfb => 0x6664, 0xfc => 0x68a7,
    0xfd => 0x6c5a, 0xfe => 0x6fb3,
  },
  0xe8 => {
    0xa1 => 0x70cf, 0xa2 => 0x71ac, 0xa3 => 0x7352, 0xa4 => 0x7b7d,
    0xa5 => 0x8708, 0xa6 => 0x8aa4, 0xa7 => 0x9c32, 0xa8 => 0x9f07,
    0xa9 => 0x5c4b, 0xaa => 0x6c83, 0xab => 0x7344, 0xac => 0x7389,
    0xad => 0x923a, 0xae => 0x6eab, 0xaf => 0x7465, 0xb0 => 0x761f,
    0xb1 => 0x7a69, 0xb2 => 0x7e15, 0xb3 => 0x860a, 0xb4 => 0x5140,
    0xb5 => 0x58c5, 0xb6 => 0x64c1, 0xb7 => 0x74ee, 0xb8 => 0x7515,
    0xb9 => 0x7670, 0xba => 0x7fc1, 0xbb => 0x9095, 0xbc => 0x96cd,
    0xbd => 0x9954, 0xbe => 0x6e26, 0xbf => 0x74e6, 0xc0 => 0x7aa9,
    0xc1 => 0x7aaa, 0xc2 => 0x81e5, 0xc3 => 0x86d9, 0xc4 => 0x8778,
    0xc5 => 0x8a1b, 0xc6 => 0x5a49, 0xc7 => 0x5b8c, 0xc8 => 0x5b9b,
    0xc9 => 0x68a1, 0xca => 0x6900, 0xcb => 0x6d63, 0xcc => 0x73a9,
    0xcd => 0x7413, 0xce => 0x742c, 0xcf => 0x7897, 0xd0 => 0x7de9,
    0xd1 => 0x7feb, 0xd2 => 0x8118, 0xd3 => 0x8155, 0xd4 => 0x839e,
    0xd5 => 0x8c4c, 0xd6 => 0x962e, 0xd7 => 0x9811, 0xd8 => 0x66f0,
    0xd9 => 0x5f80, 0xda => 0x65fa, 0xdb => 0x6789, 0xdc => 0x6c6a,
    0xdd => 0x738b, 0xde => 0x502d, 0xdf => 0x5a03, 0xe0 => 0x6b6a,
    0xe1 => 0x77ee, 0xe2 => 0x5916, 0xe3 => 0x5d6c, 0xe4 => 0x5dcd,
    0xe5 => 0x7325, 0xe6 => 0x754f, 0xe7 => 0xf9ba, 0xe8 => 0xf9bb,
    0xe9 => 0x50e5, 0xea => 0x51f9, 0xeb => 0x582f, 0xec => 0x592d,
    0xed => 0x5996, 0xee => 0x59da, 0xef => 0x5be5, 0xf0 => 0xf9bc,
    0xf1 => 0xf9bd, 0xf2 => 0x5da2, 0xf3 => 0x62d7, 0xf4 => 0x6416,
    0xf5 => 0x6493, 0xf6 => 0x64fe, 0xf7 => 0xf9be, 0xf8 => 0x66dc,
    0xf9 => 0xf9bf, 0xfa => 0x6a48, 0xfb => 0xf9c0, 0xfc => 0x71ff,
    0xfd => 0x7464, 0xfe => 0xf9c1,
  },
  0xe9 => {
    0xa1 => 0x7a88, 0xa2 => 0x7aaf, 0xa3 => 0x7e47, 0xa4 => 0x7e5e,
    0xa5 => 0x8000, 0xa6 => 0x8170, 0xa7 => 0xf9c2, 0xa8 => 0x87ef,
    0xa9 => 0x8981, 0xaa => 0x8b20, 0xab => 0x9059, 0xac => 0xf9c3,
    0xad => 0x9080, 0xae => 0x9952, 0xaf => 0x617e, 0xb0 => 0x6b32,
    0xb1 => 0x6d74, 0xb2 => 0x7e1f, 0xb3 => 0x8925, 0xb4 => 0x8fb1,
    0xb5 => 0x4fd1, 0xb6 => 0x50ad, 0xb7 => 0x5197, 0xb8 => 0x52c7,
    0xb9 => 0x57c7, 0xba => 0x5889, 0xbb => 0x5bb9, 0xbc => 0x5eb8,
    0xbd => 0x6142, 0xbe => 0x6995, 0xbf => 0x6d8c, 0xc0 => 0x6e67,
    0xc1 => 0x6eb6, 0xc2 => 0x7194, 0xc3 => 0x7462, 0xc4 => 0x7528,
    0xc5 => 0x752c, 0xc6 => 0x8073, 0xc7 => 0x8338, 0xc8 => 0x84c9,
    0xc9 => 0x8e0a, 0xca => 0x9394, 0xcb => 0x93de, 0xcc => 0xf9c4,
    0xcd => 0x4e8e, 0xce => 0x4f51, 0xcf => 0x5076, 0xd0 => 0x512a,
    0xd1 => 0x53c8, 0xd2 => 0x53cb, 0xd3 => 0x53f3, 0xd4 => 0x5b87,
    0xd5 => 0x5bd3, 0xd6 => 0x5c24, 0xd7 => 0x611a, 0xd8 => 0x6182,
    0xd9 => 0x65f4, 0xda => 0x725b, 0xdb => 0x7397, 0xdc => 0x7440,
    0xdd => 0x76c2, 0xde => 0x7950, 0xdf => 0x7991, 0xe0 => 0x79b9,
    0xe1 => 0x7d06, 0xe2 => 0x7fbd, 0xe3 => 0x828b, 0xe4 => 0x85d5,
    0xe5 => 0x865e, 0xe6 => 0x8fc2, 0xe7 => 0x9047, 0xe8 => 0x90f5,
    0xe9 => 0x91ea, 0xea => 0x9685, 0xeb => 0x96e8, 0xec => 0x96e9,
    0xed => 0x52d6, 0xee => 0x5f67, 0xef => 0x65ed, 0xf0 => 0x6631,
    0xf1 => 0x682f, 0xf2 => 0x715c, 0xf3 => 0x7a36, 0xf4 => 0x90c1,
    0xf5 => 0x980a, 0xf6 => 0x4e91, 0xf7 => 0xf9c5, 0xf8 => 0x6a52,
    0xf9 => 0x6b9e, 0xfa => 0x6f90, 0xfb => 0x7189, 0xfc => 0x8018,
    0xfd => 0x82b8, 0xfe => 0x8553,
  },
  0xea => {
    0xa1 => 0x904b, 0xa2 => 0x9695, 0xa3 => 0x96f2, 0xa4 => 0x97fb,
    0xa5 => 0x851a, 0xa6 => 0x9b31, 0xa7 => 0x4e90, 0xa8 => 0x718a,
    0xa9 => 0x96c4, 0xaa => 0x5143, 0xab => 0x539f, 0xac => 0x54e1,
    0xad => 0x5713, 0xae => 0x5712, 0xaf => 0x57a3, 0xb0 => 0x5a9b,
    0xb1 => 0x5ac4, 0xb2 => 0x5bc3, 0xb3 => 0x6028, 0xb4 => 0x613f,
    0xb5 => 0x63f4, 0xb6 => 0x6c85, 0xb7 => 0x6d39, 0xb8 => 0x6e72,
    0xb9 => 0x6e90, 0xba => 0x7230, 0xbb => 0x733f, 0xbc => 0x7457,
    0xbd => 0x82d1, 0xbe => 0x8881, 0xbf => 0x8f45, 0xc0 => 0x9060,
    0xc1 => 0xf9c6, 0xc2 => 0x9662, 0xc3 => 0x9858, 0xc4 => 0x9d1b,
    0xc5 => 0x6708, 0xc6 => 0x8d8a, 0xc7 => 0x925e, 0xc8 => 0x4f4d,
    0xc9 => 0x5049, 0xca => 0x50de, 0xcb => 0x5371, 0xcc => 0x570d,
    0xcd => 0x59d4, 0xce => 0x5a01, 0xcf => 0x5c09, 0xd0 => 0x6170,
    0xd1 => 0x6690, 0xd2 => 0x6e2d, 0xd3 => 0x7232, 0xd4 => 0x744b,
    0xd5 => 0x7def, 0xd6 => 0x80c3, 0xd7 => 0x840e, 0xd8 => 0x8466,
    0xd9 => 0x853f, 0xda => 0x875f, 0xdb => 0x885b, 0xdc => 0x8918,
    0xdd => 0x8b02, 0xde => 0x9055, 0xdf => 0x97cb, 0xe0 => 0x9b4f,
    0xe1 => 0x4e73, 0xe2 => 0x4f91, 0xe3 => 0x5112, 0xe4 => 0x516a,
    0xe5 => 0xf9c7, 0xe6 => 0x552f, 0xe7 => 0x55a9, 0xe8 => 0x5b7a,
    0xe9 => 0x5ba5, 0xea => 0x5e7c, 0xeb => 0x5e7d, 0xec => 0x5ebe,
    0xed => 0x60a0, 0xee => 0x60df, 0xef => 0x6108, 0xf0 => 0x6109,
    0xf1 => 0x63c4, 0xf2 => 0x6538, 0xf3 => 0x6709, 0xf4 => 0xf9c8,
    0xf5 => 0x67d4, 0xf6 => 0x67da, 0xf7 => 0xf9c9, 0xf8 => 0x6961,
    0xf9 => 0x6962, 0xfa => 0x6cb9, 0xfb => 0x6d27, 0xfc => 0xf9ca,
    0xfd => 0x6e38, 0xfe => 0xf9cb,
  },
  0xeb => {
    0xa1 => 0x6fe1, 0xa2 => 0x7336, 0xa3 => 0x7337, 0xa4 => 0xf9cc,
    0xa5 => 0x745c, 0xa6 => 0x7531, 0xa7 => 0xf9cd, 0xa8 => 0x7652,
    0xa9 => 0xf9ce, 0xaa => 0xf9cf, 0xab => 0x7dad, 0xac => 0x81fe,
    0xad => 0x8438, 0xae => 0x88d5, 0xaf => 0x8a98, 0xb0 => 0x8adb,
    0xb1 => 0x8aed, 0xb2 => 0x8e30, 0xb3 => 0x8e42, 0xb4 => 0x904a,
    0xb5 => 0x903e, 0xb6 => 0x907a, 0xb7 => 0x9149, 0xb8 => 0x91c9,
    0xb9 => 0x936e, 0xba => 0xf9d0, 0xbb => 0xf9d1, 0xbc => 0x5809,
    0xbd => 0xf9d2, 0xbe => 0x6bd3, 0xbf => 0x8089, 0xc0 => 0x80b2,
    0xc1 => 0xf9d3, 0xc2 => 0xf9d4, 0xc3 => 0x5141, 0xc4 => 0x596b,
    0xc5 => 0x5c39, 0xc6 => 0xf9d5, 0xc7 => 0xf9d6, 0xc8 => 0x6f64,
    0xc9 => 0x73a7, 0xca => 0x80e4, 0xcb => 0x8d07, 0xcc => 0xf9d7,
    0xcd => 0x9217, 0xce => 0x958f, 0xcf => 0xf9d8, 0xd0 => 0xf9d9,
    0xd1 => 0xf9da, 0xd2 => 0xf9db, 0xd3 => 0x807f, 0xd4 => 0x620e,
    0xd5 => 0x701c, 0xd6 => 0x7d68, 0xd7 => 0x878d, 0xd8 => 0xf9dc,
    0xd9 => 0x57a0, 0xda => 0x6069, 0xdb => 0x6147, 0xdc => 0x6bb7,
    0xdd => 0x8abe, 0xde => 0x9280, 0xdf => 0x96b1, 0xe0 => 0x4e59,
    0xe1 => 0x541f, 0xe2 => 0x6deb, 0xe3 => 0x852d, 0xe4 => 0x9670,
    0xe5 => 0x97f3, 0xe6 => 0x98ee, 0xe7 => 0x63d6, 0xe8 => 0x6ce3,
    0xe9 => 0x9091, 0xea => 0x51dd, 0xeb => 0x61c9, 0xec => 0x81ba,
    0xed => 0x9df9, 0xee => 0x4f9d, 0xef => 0x501a, 0xf0 => 0x5100,
    0xf1 => 0x5b9c, 0xf2 => 0x610f, 0xf3 => 0x61ff, 0xf4 => 0x64ec,
    0xf5 => 0x6905, 0xf6 => 0x6bc5, 0xf7 => 0x7591, 0xf8 => 0x77e3,
    0xf9 => 0x7fa9, 0xfa => 0x8264, 0xfb => 0x858f, 0xfc => 0x87fb,
    0xfd => 0x8863, 0xfe => 0x8abc,
  },
  0xec => {
    0xa1 => 0x8b70, 0xa2 => 0x91ab, 0xa3 => 0x4e8c, 0xa4 => 0x4ee5,
    0xa5 => 0x4f0a, 0xa6 => 0xf9dd, 0xa7 => 0xf9de, 0xa8 => 0x5937,
    0xa9 => 0x59e8, 0xaa => 0xf9df, 0xab => 0x5df2, 0xac => 0x5f1b,
    0xad => 0x5f5b, 0xae => 0x6021, 0xaf => 0xf9e0, 0xb0 => 0xf9e1,
    0xb1 => 0xf9e2, 0xb2 => 0xf9e3, 0xb3 => 0x723e, 0xb4 => 0x73e5,
    0xb5 => 0xf9e4, 0xb6 => 0x7570, 0xb7 => 0x75cd, 0xb8 => 0xf9e5,
    0xb9 => 0x79fb, 0xba => 0xf9e6, 0xbb => 0x800c, 0xbc => 0x8033,
    0xbd => 0x8084, 0xbe => 0x82e1, 0xbf => 0x8351, 0xc0 => 0xf9e7,
    0xc1 => 0xf9e8, 0xc2 => 0x8cbd, 0xc3 => 0x8cb3, 0xc4 => 0x9087,
    0xc5 => 0xf9e9, 0xc6 => 0xf9ea, 0xc7 => 0x98f4, 0xc8 => 0x990c,
    0xc9 => 0xf9eb, 0xca => 0xf9ec, 0xcb => 0x7037, 0xcc => 0x76ca,
    0xcd => 0x7fca, 0xce => 0x7fcc, 0xcf => 0x7ffc, 0xd0 => 0x8b1a,
    0xd1 => 0x4eba, 0xd2 => 0x4ec1, 0xd3 => 0x5203, 0xd4 => 0x5370,
    0xd5 => 0xf9ed, 0xd6 => 0x54bd, 0xd7 => 0x56e0, 0xd8 => 0x59fb,
    0xd9 => 0x5bc5, 0xda => 0x5f15, 0xdb => 0x5fcd, 0xdc => 0x6e6e,
    0xdd => 0xf9ee, 0xde => 0xf9ef, 0xdf => 0x7d6a, 0xe0 => 0x8335,
    0xe1 => 0xf9f0, 0xe2 => 0x8693, 0xe3 => 0x8a8d, 0xe4 => 0xf9f1,
    0xe5 => 0x976d, 0xe6 => 0x9777, 0xe7 => 0xf9f2, 0xe8 => 0xf9f3,
    0xe9 => 0x4e00, 0xea => 0x4f5a, 0xeb => 0x4f7e, 0xec => 0x58f9,
    0xed => 0x65e5, 0xee => 0x6ea2, 0xef => 0x9038, 0xf0 => 0x93b0,
    0xf1 => 0x99b9, 0xf2 => 0x4efb, 0xf3 => 0x58ec, 0xf4 => 0x598a,
    0xf5 => 0x59d9, 0xf6 => 0x6041, 0xf7 => 0xf9f4, 0xf8 => 0xf9f5,
    0xf9 => 0x7a14, 0xfa => 0xf9f6, 0xfb => 0x834f, 0xfc => 0x8cc3,
    0xfd => 0x5165, 0xfe => 0x5344,
  },
  0xed => {
    0xa1 => 0xf9f7, 0xa2 => 0xf9f8, 0xa3 => 0xf9f9, 0xa4 => 0x4ecd,
    0xa5 => 0x5269, 0xa6 => 0x5b55, 0xa7 => 0x82bf, 0xa8 => 0x4ed4,
    0xa9 => 0x523a, 0xaa => 0x54a8, 0xab => 0x59c9, 0xac => 0x59ff,
    0xad => 0x5b50, 0xae => 0x5b57, 0xaf => 0x5b5c, 0xb0 => 0x6063,
    0xb1 => 0x6148, 0xb2 => 0x6ecb, 0xb3 => 0x7099, 0xb4 => 0x716e,
    0xb5 => 0x7386, 0xb6 => 0x74f7, 0xb7 => 0x75b5, 0xb8 => 0x78c1,
    0xb9 => 0x7d2b, 0xba => 0x8005, 0xbb => 0x81ea, 0xbc => 0x8328,
    0xbd => 0x8517, 0xbe => 0x85c9, 0xbf => 0x8aee, 0xc0 => 0x8cc7,
    0xc1 => 0x96cc, 0xc2 => 0x4f5c, 0xc3 => 0x52fa, 0xc4 => 0x56bc,
    0xc5 => 0x65ab, 0xc6 => 0x6628, 0xc7 => 0x707c, 0xc8 => 0x70b8,
    0xc9 => 0x7235, 0xca => 0x7dbd, 0xcb => 0x828d, 0xcc => 0x914c,
    0xcd => 0x96c0, 0xce => 0x9d72, 0xcf => 0x5b71, 0xd0 => 0x68e7,
    0xd1 => 0x6b98, 0xd2 => 0x6f7a, 0xd3 => 0x76de, 0xd4 => 0x5c91,
    0xd5 => 0x66ab, 0xd6 => 0x6f5b, 0xd7 => 0x7bb4, 0xd8 => 0x7c2a,
    0xd9 => 0x8836, 0xda => 0x96dc, 0xdb => 0x4e08, 0xdc => 0x4ed7,
    0xdd => 0x5320, 0xde => 0x5834, 0xdf => 0x58bb, 0xe0 => 0x58ef,
    0xe1 => 0x596c, 0xe2 => 0x5c07, 0xe3 => 0x5e33, 0xe4 => 0x5e84,
    0xe5 => 0x5f35, 0xe6 => 0x638c, 0xe7 => 0x66b2, 0xe8 => 0x6756,
    0xe9 => 0x6a1f, 0xea => 0x6aa3, 0xeb => 0x6b0c, 0xec => 0x6f3f,
    0xed => 0x7246, 0xee => 0xf9fa, 0xef => 0x7350, 0xf0 => 0x748b,
    0xf1 => 0x7ae0, 0xf2 => 0x7ca7, 0xf3 => 0x8178, 0xf4 => 0x81df,
    0xf5 => 0x81e7, 0xf6 => 0x838a, 0xf7 => 0x846c, 0xf8 => 0x8523,
    0xf9 => 0x8594, 0xfa => 0x85cf, 0xfb => 0x88dd, 0xfc => 0x8d13,
    0xfd => 0x91ac, 0xfe => 0x9577,
  },
  0xee => {
    0xa1 => 0x969c, 0xa2 => 0x518d, 0xa3 => 0x54c9, 0xa4 => 0x5728,
    0xa5 => 0x5bb0, 0xa6 => 0x624d, 0xa7 => 0x6750, 0xa8 => 0x683d,
    0xa9 => 0x6893, 0xaa => 0x6e3d, 0xab => 0x6ed3, 0xac => 0x707d,
    0xad => 0x7e21, 0xae => 0x88c1, 0xaf => 0x8ca1, 0xb0 => 0x8f09,
    0xb1 => 0x9f4b, 0xb2 => 0x9f4e, 0xb3 => 0x722d, 0xb4 => 0x7b8f,
    0xb5 => 0x8acd, 0xb6 => 0x931a, 0xb7 => 0x4f47, 0xb8 => 0x4f4e,
    0xb9 => 0x5132, 0xba => 0x5480, 0xbb => 0x59d0, 0xbc => 0x5e95,
    0xbd => 0x62b5, 0xbe => 0x6775, 0xbf => 0x696e, 0xc0 => 0x6a17,
    0xc1 => 0x6cae, 0xc2 => 0x6e1a, 0xc3 => 0x72d9, 0xc4 => 0x732a,
    0xc5 => 0x75bd, 0xc6 => 0x7bb8, 0xc7 => 0x7d35, 0xc8 => 0x82e7,
    0xc9 => 0x83f9, 0xca => 0x8457, 0xcb => 0x85f7, 0xcc => 0x8a5b,
    0xcd => 0x8caf, 0xce => 0x8e87, 0xcf => 0x9019, 0xd0 => 0x90b8,
    0xd1 => 0x96ce, 0xd2 => 0x9f5f, 0xd3 => 0x52e3, 0xd4 => 0x540a,
    0xd5 => 0x5ae1, 0xd6 => 0x5bc2, 0xd7 => 0x6458, 0xd8 => 0x6575,
    0xd9 => 0x6ef4, 0xda => 0x72c4, 0xdb => 0xf9fb, 0xdc => 0x7684,
    0xdd => 0x7a4d, 0xde => 0x7b1b, 0xdf => 0x7c4d, 0xe0 => 0x7e3e,
    0xe1 => 0x7fdf, 0xe2 => 0x837b, 0xe3 => 0x8b2b, 0xe4 => 0x8cca,
    0xe5 => 0x8d64, 0xe6 => 0x8de1, 0xe7 => 0x8e5f, 0xe8 => 0x8fea,
    0xe9 => 0x8ff9, 0xea => 0x9069, 0xeb => 0x93d1, 0xec => 0x4f43,
    0xed => 0x4f7a, 0xee => 0x50b3, 0xef => 0x5168, 0xf0 => 0x5178,
    0xf1 => 0x524d, 0xf2 => 0x526a, 0xf3 => 0x5861, 0xf4 => 0x587c,
    0xf5 => 0x5960, 0xf6 => 0x5c08, 0xf7 => 0x5c55, 0xf8 => 0x5edb,
    0xf9 => 0x609b, 0xfa => 0x6230, 0xfb => 0x6813, 0xfc => 0x6bbf,
    0xfd => 0x6c08, 0xfe => 0x6fb1,
  },
  0xef => {
    0xa1 => 0x714e, 0xa2 => 0x7420, 0xa3 => 0x7530, 0xa4 => 0x7538,
    0xa5 => 0x7551, 0xa6 => 0x7672, 0xa7 => 0x7b4c, 0xa8 => 0x7b8b,
    0xa9 => 0x7bad, 0xaa => 0x7bc6, 0xab => 0x7e8f, 0xac => 0x8a6e,
    0xad => 0x8f3e, 0xae => 0x8f49, 0xaf => 0x923f, 0xb0 => 0x9293,
    0xb1 => 0x9322, 0xb2 => 0x942b, 0xb3 => 0x96fb, 0xb4 => 0x985a,
    0xb5 => 0x986b, 0xb6 => 0x991e, 0xb7 => 0x5207, 0xb8 => 0x622a,
    0xb9 => 0x6298, 0xba => 0x6d59, 0xbb => 0x7664, 0xbc => 0x7aca,
    0xbd => 0x7bc0, 0xbe => 0x7d76, 0xbf => 0x5360, 0xc0 => 0x5cbe,
    0xc1 => 0x5e97, 0xc2 => 0x6f38, 0xc3 => 0x70b9, 0xc4 => 0x7c98,
    0xc5 => 0x9711, 0xc6 => 0x9b8e, 0xc7 => 0x9ede, 0xc8 => 0x63a5,
    0xc9 => 0x647a, 0xca => 0x8776, 0xcb => 0x4e01, 0xcc => 0x4e95,
    0xcd => 0x4ead, 0xce => 0x505c, 0xcf => 0x5075, 0xd0 => 0x5448,
    0xd1 => 0x59c3, 0xd2 => 0x5b9a, 0xd3 => 0x5e40, 0xd4 => 0x5ead,
    0xd5 => 0x5ef7, 0xd6 => 0x5f81, 0xd7 => 0x60c5, 0xd8 => 0x633a,
    0xd9 => 0x653f, 0xda => 0x6574, 0xdb => 0x65cc, 0xdc => 0x6676,
    0xdd => 0x6678, 0xde => 0x67fe, 0xdf => 0x6968, 0xe0 => 0x6a89,
    0xe1 => 0x6b63, 0xe2 => 0x6c40, 0xe3 => 0x6dc0, 0xe4 => 0x6de8,
    0xe5 => 0x6e1f, 0xe6 => 0x6e5e, 0xe7 => 0x701e, 0xe8 => 0x70a1,
    0xe9 => 0x738e, 0xea => 0x73fd, 0xeb => 0x753a, 0xec => 0x775b,
    0xed => 0x7887, 0xee => 0x798e, 0xef => 0x7a0b, 0xf0 => 0x7a7d,
    0xf1 => 0x7cbe, 0xf2 => 0x7d8e, 0xf3 => 0x8247, 0xf4 => 0x8a02,
    0xf5 => 0x8aea, 0xf6 => 0x8c9e, 0xf7 => 0x912d, 0xf8 => 0x914a,
    0xf9 => 0x91d8, 0xfa => 0x9266, 0xfb => 0x92cc, 0xfc => 0x9320,
    0xfd => 0x9706, 0xfe => 0x9756,
  },
  0xf0 => {
    0xa1 => 0x975c, 0xa2 => 0x9802, 0xa3 => 0x9f0e, 0xa4 => 0x5236,
    0xa5 => 0x5291, 0xa6 => 0x557c, 0xa7 => 0x5824, 0xa8 => 0x5e1d,
    0xa9 => 0x5f1f, 0xaa => 0x608c, 0xab => 0x63d0, 0xac => 0x68af,
    0xad => 0x6fdf, 0xae => 0x796d, 0xaf => 0x7b2c, 0xb0 => 0x81cd,
    0xb1 => 0x85ba, 0xb2 => 0x88fd, 0xb3 => 0x8af8, 0xb4 => 0x8e44,
    0xb5 => 0x918d, 0xb6 => 0x9664, 0xb7 => 0x969b, 0xb8 => 0x973d,
    0xb9 => 0x984c, 0xba => 0x9f4a, 0xbb => 0x4fce, 0xbc => 0x5146,
    0xbd => 0x51cb, 0xbe => 0x52a9, 0xbf => 0x5632, 0xc0 => 0x5f14,
    0xc1 => 0x5f6b, 0xc2 => 0x63aa, 0xc3 => 0x64cd, 0xc4 => 0x65e9,
    0xc5 => 0x6641, 0xc6 => 0x66fa, 0xc7 => 0x66f9, 0xc8 => 0x671d,
    0xc9 => 0x689d, 0xca => 0x68d7, 0xcb => 0x69fd, 0xcc => 0x6f15,
    0xcd => 0x6f6e, 0xce => 0x7167, 0xcf => 0x71e5, 0xd0 => 0x722a,
    0xd1 => 0x74aa, 0xd2 => 0x773a, 0xd3 => 0x7956, 0xd4 => 0x795a,
    0xd5 => 0x79df, 0xd6 => 0x7a20, 0xd7 => 0x7a95, 0xd8 => 0x7c97,
    0xd9 => 0x7cdf, 0xda => 0x7d44, 0xdb => 0x7e70, 0xdc => 0x8087,
    0xdd => 0x85fb, 0xde => 0x86a4, 0xdf => 0x8a54, 0xe0 => 0x8abf,
    0xe1 => 0x8d99, 0xe2 => 0x8e81, 0xe3 => 0x9020, 0xe4 => 0x906d,
    0xe5 => 0x91e3, 0xe6 => 0x963b, 0xe7 => 0x96d5, 0xe8 => 0x9ce5,
    0xe9 => 0x65cf, 0xea => 0x7c07, 0xeb => 0x8db3, 0xec => 0x93c3,
    0xed => 0x5b58, 0xee => 0x5c0a, 0xef => 0x5352, 0xf0 => 0x62d9,
    0xf1 => 0x731d, 0xf2 => 0x5027, 0xf3 => 0x5b97, 0xf4 => 0x5f9e,
    0xf5 => 0x60b0, 0xf6 => 0x616b, 0xf7 => 0x68d5, 0xf8 => 0x6dd9,
    0xf9 => 0x742e, 0xfa => 0x7a2e, 0xfb => 0x7d42, 0xfc => 0x7d9c,
    0xfd => 0x7e31, 0xfe => 0x816b,
  },
  0xf1 => {
    0xa1 => 0x8e2a, 0xa2 => 0x8e35, 0xa3 => 0x937e, 0xa4 => 0x9418,
    0xa5 => 0x4f50, 0xa6 => 0x5750, 0xa7 => 0x5de6, 0xa8 => 0x5ea7,
    0xa9 => 0x632b, 0xaa => 0x7f6a, 0xab => 0x4e3b, 0xac => 0x4f4f,
    0xad => 0x4f8f, 0xae => 0x505a, 0xaf => 0x59dd, 0xb0 => 0x80c4,
    0xb1 => 0x546a, 0xb2 => 0x5468, 0xb3 => 0x55fe, 0xb4 => 0x594f,
    0xb5 => 0x5b99, 0xb6 => 0x5dde, 0xb7 => 0x5eda, 0xb8 => 0x665d,
    0xb9 => 0x6731, 0xba => 0x67f1, 0xbb => 0x682a, 0xbc => 0x6ce8,
    0xbd => 0x6d32, 0xbe => 0x6e4a, 0xbf => 0x6f8d, 0xc0 => 0x70b7,
    0xc1 => 0x73e0, 0xc2 => 0x7587, 0xc3 => 0x7c4c, 0xc4 => 0x7d02,
    0xc5 => 0x7d2c, 0xc6 => 0x7da2, 0xc7 => 0x821f, 0xc8 => 0x86db,
    0xc9 => 0x8a3b, 0xca => 0x8a85, 0xcb => 0x8d70, 0xcc => 0x8e8a,
    0xcd => 0x8f33, 0xce => 0x9031, 0xcf => 0x914e, 0xd0 => 0x9152,
    0xd1 => 0x9444, 0xd2 => 0x99d0, 0xd3 => 0x7af9, 0xd4 => 0x7ca5,
    0xd5 => 0x4fca, 0xd6 => 0x5101, 0xd7 => 0x51c6, 0xd8 => 0x57c8,
    0xd9 => 0x5bef, 0xda => 0x5cfb, 0xdb => 0x6659, 0xdc => 0x6a3d,
    0xdd => 0x6d5a, 0xde => 0x6e96, 0xdf => 0x6fec, 0xe0 => 0x710c,
    0xe1 => 0x756f, 0xe2 => 0x7ae3, 0xe3 => 0x8822, 0xe4 => 0x9021,
    0xe5 => 0x9075, 0xe6 => 0x96cb, 0xe7 => 0x99ff, 0xe8 => 0x8301,
    0xe9 => 0x4e2d, 0xea => 0x4ef2, 0xeb => 0x8846, 0xec => 0x91cd,
    0xed => 0x537d, 0xee => 0x6adb, 0xef => 0x696b, 0xf0 => 0x6c41,
    0xf1 => 0x847a, 0xf2 => 0x589e, 0xf3 => 0x618e, 0xf4 => 0x66fe,
    0xf5 => 0x62ef, 0xf6 => 0x70dd, 0xf7 => 0x7511, 0xf8 => 0x75c7,
    0xf9 => 0x7e52, 0xfa => 0x84b8, 0xfb => 0x8b49, 0xfc => 0x8d08,
    0xfd => 0x4e4b, 0xfe => 0x53ea,
  },
  0xf2 => {
    0xa1 => 0x54ab, 0xa2 => 0x5730, 0xa3 => 0x5740, 0xa4 => 0x5fd7,
    0xa5 => 0x6301, 0xa6 => 0x6307, 0xa7 => 0x646f, 0xa8 => 0x652f,
    0xa9 => 0x65e8, 0xaa => 0x667a, 0xab => 0x679d, 0xac => 0x67b3,
    0xad => 0x6b62, 0xae => 0x6c60, 0xaf => 0x6c9a, 0xb0 => 0x6f2c,
    0xb1 => 0x77e5, 0xb2 => 0x7825, 0xb3 => 0x7949, 0xb4 => 0x7957,
    0xb5 => 0x7d19, 0xb6 => 0x80a2, 0xb7 => 0x8102, 0xb8 => 0x81f3,
    0xb9 => 0x829d, 0xba => 0x82b7, 0xbb => 0x8718, 0xbc => 0x8a8c,
    0xbd => 0xf9fc, 0xbe => 0x8d04, 0xbf => 0x8dbe, 0xc0 => 0x9072,
    0xc1 => 0x76f4, 0xc2 => 0x7a19, 0xc3 => 0x7a37, 0xc4 => 0x7e54,
    0xc5 => 0x8077, 0xc6 => 0x5507, 0xc7 => 0x55d4, 0xc8 => 0x5875,
    0xc9 => 0x632f, 0xca => 0x6422, 0xcb => 0x6649, 0xcc => 0x664b,
    0xcd => 0x686d, 0xce => 0x699b, 0xcf => 0x6b84, 0xd0 => 0x6d25,
    0xd1 => 0x6eb1, 0xd2 => 0x73cd, 0xd3 => 0x7468, 0xd4 => 0x74a1,
    0xd5 => 0x755b, 0xd6 => 0x75b9, 0xd7 => 0x76e1, 0xd8 => 0x771e,
    0xd9 => 0x778b, 0xda => 0x79e6, 0xdb => 0x7e09, 0xdc => 0x7e1d,
    0xdd => 0x81fb, 0xde => 0x852f, 0xdf => 0x8897, 0xe0 => 0x8a3a,
    0xe1 => 0x8cd1, 0xe2 => 0x8eeb, 0xe3 => 0x8fb0, 0xe4 => 0x9032,
    0xe5 => 0x93ad, 0xe6 => 0x9663, 0xe7 => 0x9673, 0xe8 => 0x9707,
    0xe9 => 0x4f84, 0xea => 0x53f1, 0xeb => 0x59ea, 0xec => 0x5ac9,
    0xed => 0x5e19, 0xee => 0x684e, 0xef => 0x74c6, 0xf0 => 0x75be,
    0xf1 => 0x79e9, 0xf2 => 0x7a92, 0xf3 => 0x81a3, 0xf4 => 0x86ed,
    0xf5 => 0x8cea, 0xf6 => 0x8dcc, 0xf7 => 0x8fed, 0xf8 => 0x659f,
    0xf9 => 0x6715, 0xfa => 0xf9fd, 0xfb => 0x57f7, 0xfc => 0x6f57,
    0xfd => 0x7ddd, 0xfe => 0x8f2f,
  },
  0xf3 => {
    0xa1 => 0x93f6, 0xa2 => 0x96c6, 0xa3 => 0x5fb5, 0xa4 => 0x61f2,
    0xa5 => 0x6f84, 0xa6 => 0x4e14, 0xa7 => 0x4f98, 0xa8 => 0x501f,
    0xa9 => 0x53c9, 0xaa => 0x55df, 0xab => 0x5d6f, 0xac => 0x5dee,
    0xad => 0x6b21, 0xae => 0x6b64, 0xaf => 0x78cb, 0xb0 => 0x7b9a,
    0xb1 => 0xf9fe, 0xb2 => 0x8e49, 0xb3 => 0x8eca, 0xb4 => 0x906e,
    0xb5 => 0x6349, 0xb6 => 0x643e, 0xb7 => 0x7740, 0xb8 => 0x7a84,
    0xb9 => 0x932f, 0xba => 0x947f, 0xbb => 0x9f6a, 0xbc => 0x64b0,
    0xbd => 0x6faf, 0xbe => 0x71e6, 0xbf => 0x74a8, 0xc0 => 0x74da,
    0xc1 => 0x7ac4, 0xc2 => 0x7c12, 0xc3 => 0x7e82, 0xc4 => 0x7cb2,
    0xc5 => 0x7e98, 0xc6 => 0x8b9a, 0xc7 => 0x8d0a, 0xc8 => 0x947d,
    0xc9 => 0x9910, 0xca => 0x994c, 0xcb => 0x5239, 0xcc => 0x5bdf,
    0xcd => 0x64e6, 0xce => 0x672d, 0xcf => 0x7d2e, 0xd0 => 0x50ed,
    0xd1 => 0x53c3, 0xd2 => 0x5879, 0xd3 => 0x6158, 0xd4 => 0x6159,
    0xd5 => 0x61fa, 0xd6 => 0x65ac, 0xd7 => 0x7ad9, 0xd8 => 0x8b92,
    0xd9 => 0x8b96, 0xda => 0x5009, 0xdb => 0x5021, 0xdc => 0x5275,
    0xdd => 0x5531, 0xde => 0x5a3c, 0xdf => 0x5ee0, 0xe0 => 0x5f70,
    0xe1 => 0x6134, 0xe2 => 0x655e, 0xe3 => 0x660c, 0xe4 => 0x6636,
    0xe5 => 0x66a2, 0xe6 => 0x69cd, 0xe7 => 0x6ec4, 0xe8 => 0x6f32,
    0xe9 => 0x7316, 0xea => 0x7621, 0xeb => 0x7a93, 0xec => 0x8139,
    0xed => 0x8259, 0xee => 0x83d6, 0xef => 0x84bc, 0xf0 => 0x50b5,
    0xf1 => 0x57f0, 0xf2 => 0x5bc0, 0xf3 => 0x5be8, 0xf4 => 0x5f69,
    0xf5 => 0x63a1, 0xf6 => 0x7826, 0xf7 => 0x7db5, 0xf8 => 0x83dc,
    0xf9 => 0x8521, 0xfa => 0x91c7, 0xfb => 0x91f5, 0xfc => 0x518a,
    0xfd => 0x67f5, 0xfe => 0x7b56,
  },
  0xf4 => {
    0xa1 => 0x8cac, 0xa2 => 0x51c4, 0xa3 => 0x59bb, 0xa4 => 0x60bd,
    0xa5 => 0x8655, 0xa6 => 0x501c, 0xa7 => 0xf9ff, 0xa8 => 0x5254,
    0xa9 => 0x5c3a, 0xaa => 0x617d, 0xab => 0x621a, 0xac => 0x62d3,
    0xad => 0x64f2, 0xae => 0x65a5, 0xaf => 0x6ecc, 0xb0 => 0x7620,
    0xb1 => 0x810a, 0xb2 => 0x8e60, 0xb3 => 0x965f, 0xb4 => 0x96bb,
    0xb5 => 0x4edf, 0xb6 => 0x5343, 0xb7 => 0x5598, 0xb8 => 0x5929,
    0xb9 => 0x5ddd, 0xba => 0x64c5, 0xbb => 0x6cc9, 0xbc => 0x6dfa,
    0xbd => 0x7394, 0xbe => 0x7a7f, 0xbf => 0x821b, 0xc0 => 0x85a6,
    0xc1 => 0x8ce4, 0xc2 => 0x8e10, 0xc3 => 0x9077, 0xc4 => 0x91e7,
    0xc5 => 0x95e1, 0xc6 => 0x9621, 0xc7 => 0x97c6, 0xc8 => 0x51f8,
    0xc9 => 0x54f2, 0xca => 0x5586, 0xcb => 0x5fb9, 0xcc => 0x64a4,
    0xcd => 0x6f88, 0xce => 0x7db4, 0xcf => 0x8f1f, 0xd0 => 0x8f4d,
    0xd1 => 0x9435, 0xd2 => 0x50c9, 0xd3 => 0x5c16, 0xd4 => 0x6cbe,
    0xd5 => 0x6dfb, 0xd6 => 0x751b, 0xd7 => 0x77bb, 0xd8 => 0x7c3d,
    0xd9 => 0x7c64, 0xda => 0x8a79, 0xdb => 0x8ac2, 0xdc => 0x581e,
    0xdd => 0x59be, 0xde => 0x5e16, 0xdf => 0x6377, 0xe0 => 0x7252,
    0xe1 => 0x758a, 0xe2 => 0x776b, 0xe3 => 0x8adc, 0xe4 => 0x8cbc,
    0xe5 => 0x8f12, 0xe6 => 0x5ef3, 0xe7 => 0x6674, 0xe8 => 0x6df8,
    0xe9 => 0x807d, 0xea => 0x83c1, 0xeb => 0x8acb, 0xec => 0x9751,
    0xed => 0x9bd6, 0xee => 0xfa00, 0xef => 0x5243, 0xf0 => 0x66ff,
    0xf1 => 0x6d95, 0xf2 => 0x6eef, 0xf3 => 0x7de0, 0xf4 => 0x8ae6,
    0xf5 => 0x902e, 0xf6 => 0x905e, 0xf7 => 0x9ad4, 0xf8 => 0x521d,
    0xf9 => 0x527f, 0xfa => 0x54e8, 0xfb => 0x6194, 0xfc => 0x6284,
    0xfd => 0x62db, 0xfe => 0x68a2,
  },
  0xf5 => {
    0xa1 => 0x6912, 0xa2 => 0x695a, 0xa3 => 0x6a35, 0xa4 => 0x7092,
    0xa5 => 0x7126, 0xa6 => 0x785d, 0xa7 => 0x7901, 0xa8 => 0x790e,
    0xa9 => 0x79d2, 0xaa => 0x7a0d, 0xab => 0x8096, 0xac => 0x8278,
    0xad => 0x82d5, 0xae => 0x8349, 0xaf => 0x8549, 0xb0 => 0x8c82,
    0xb1 => 0x8d85, 0xb2 => 0x9162, 0xb3 => 0x918b, 0xb4 => 0x91ae,
    0xb5 => 0x4fc3, 0xb6 => 0x56d1, 0xb7 => 0x71ed, 0xb8 => 0x77d7,
    0xb9 => 0x8700, 0xba => 0x89f8, 0xbb => 0x5bf8, 0xbc => 0x5fd6,
    0xbd => 0x6751, 0xbe => 0x90a8, 0xbf => 0x53e2, 0xc0 => 0x585a,
    0xc1 => 0x5bf5, 0xc2 => 0x60a4, 0xc3 => 0x6181, 0xc4 => 0x6460,
    0xc5 => 0x7e3d, 0xc6 => 0x8070, 0xc7 => 0x8525, 0xc8 => 0x9283,
    0xc9 => 0x64ae, 0xca => 0x50ac, 0xcb => 0x5d14, 0xcc => 0x6700,
    0xcd => 0x589c, 0xce => 0x62bd, 0xcf => 0x63a8, 0xd0 => 0x690e,
    0xd1 => 0x6978, 0xd2 => 0x6a1e, 0xd3 => 0x6e6b, 0xd4 => 0x76ba,
    0xd5 => 0x79cb, 0xd6 => 0x82bb, 0xd7 => 0x8429, 0xd8 => 0x8acf,
    0xd9 => 0x8da8, 0xda => 0x8ffd, 0xdb => 0x9112, 0xdc => 0x914b,
    0xdd => 0x919c, 0xde => 0x9310, 0xdf => 0x9318, 0xe0 => 0x939a,
    0xe1 => 0x96db, 0xe2 => 0x9a36, 0xe3 => 0x9c0d, 0xe4 => 0x4e11,
    0xe5 => 0x755c, 0xe6 => 0x795d, 0xe7 => 0x7afa, 0xe8 => 0x7b51,
    0xe9 => 0x7bc9, 0xea => 0x7e2e, 0xeb => 0x84c4, 0xec => 0x8e59,
    0xed => 0x8e74, 0xee => 0x8ef8, 0xef => 0x9010, 0xf0 => 0x6625,
    0xf1 => 0x693f, 0xf2 => 0x7443, 0xf3 => 0x51fa, 0xf4 => 0x672e,
    0xf5 => 0x9edc, 0xf6 => 0x5145, 0xf7 => 0x5fe0, 0xf8 => 0x6c96,
    0xf9 => 0x87f2, 0xfa => 0x885d, 0xfb => 0x8877, 0xfc => 0x60b4,
    0xfd => 0x81b5, 0xfe => 0x8403,
  },
  0xf6 => {
    0xa1 => 0x8d05, 0xa2 => 0x53d6, 0xa3 => 0x5439, 0xa4 => 0x5634,
    0xa5 => 0x5a36, 0xa6 => 0x5c31, 0xa7 => 0x708a, 0xa8 => 0x7fe0,
    0xa9 => 0x805a, 0xaa => 0x8106, 0xab => 0x81ed, 0xac => 0x8da3,
    0xad => 0x9189, 0xae => 0x9a5f, 0xaf => 0x9df2, 0xb0 => 0x5074,
    0xb1 => 0x4ec4, 0xb2 => 0x53a0, 0xb3 => 0x60fb, 0xb4 => 0x6e2c,
    0xb5 => 0x5c64, 0xb6 => 0x4f88, 0xb7 => 0x5024, 0xb8 => 0x55e4,
    0xb9 => 0x5cd9, 0xba => 0x5e5f, 0xbb => 0x6065, 0xbc => 0x6894,
    0xbd => 0x6cbb, 0xbe => 0x6dc4, 0xbf => 0x71be, 0xc0 => 0x75d4,
    0xc1 => 0x75f4, 0xc2 => 0x7661, 0xc3 => 0x7a1a, 0xc4 => 0x7a49,
    0xc5 => 0x7dc7, 0xc6 => 0x7dfb, 0xc7 => 0x7f6e, 0xc8 => 0x81f4,
    0xc9 => 0x86a9, 0xca => 0x8f1c, 0xcb => 0x96c9, 0xcc => 0x99b3,
    0xcd => 0x9f52, 0xce => 0x5247, 0xcf => 0x52c5, 0xd0 => 0x98ed,
    0xd1 => 0x89aa, 0xd2 => 0x4e03, 0xd3 => 0x67d2, 0xd4 => 0x6f06,
    0xd5 => 0x4fb5, 0xd6 => 0x5be2, 0xd7 => 0x6795, 0xd8 => 0x6c88,
    0xd9 => 0x6d78, 0xda => 0x741b, 0xdb => 0x7827, 0xdc => 0x91dd,
    0xdd => 0x937c, 0xde => 0x87c4, 0xdf => 0x79e4, 0xe0 => 0x7a31,
    0xe1 => 0x5feb, 0xe2 => 0x4ed6, 0xe3 => 0x54a4, 0xe4 => 0x553e,
    0xe5 => 0x58ae, 0xe6 => 0x59a5, 0xe7 => 0x60f0, 0xe8 => 0x6253,
    0xe9 => 0x62d6, 0xea => 0x6736, 0xeb => 0x6955, 0xec => 0x8235,
    0xed => 0x9640, 0xee => 0x99b1, 0xef => 0x99dd, 0xf0 => 0x502c,
    0xf1 => 0x5353, 0xf2 => 0x5544, 0xf3 => 0x577c, 0xf4 => 0xfa01,
    0xf5 => 0x6258, 0xf6 => 0xfa02, 0xf7 => 0x64e2, 0xf8 => 0x666b,
    0xf9 => 0x67dd, 0xfa => 0x6fc1, 0xfb => 0x6fef, 0xfc => 0x7422,
    0xfd => 0x7438, 0xfe => 0x8a17,
  },
  0xf7 => {
    0xa1 => 0x9438, 0xa2 => 0x5451, 0xa3 => 0x5606, 0xa4 => 0x5766,
    0xa5 => 0x5f48, 0xa6 => 0x619a, 0xa7 => 0x6b4e, 0xa8 => 0x7058,
    0xa9 => 0x70ad, 0xaa => 0x7dbb, 0xab => 0x8a95, 0xac => 0x596a,
    0xad => 0x812b, 0xae => 0x63a2, 0xaf => 0x7708, 0xb0 => 0x803d,
    0xb1 => 0x8caa, 0xb2 => 0x5854, 0xb3 => 0x642d, 0xb4 => 0x69bb,
    0xb5 => 0x5b95, 0xb6 => 0x5e11, 0xb7 => 0x6e6f, 0xb8 => 0xfa03,
    0xb9 => 0x8569, 0xba => 0x514c, 0xbb => 0x53f0, 0xbc => 0x592a,
    0xbd => 0x6020, 0xbe => 0x614b, 0xbf => 0x6b86, 0xc0 => 0x6c70,
    0xc1 => 0x6cf0, 0xc2 => 0x7b1e, 0xc3 => 0x80ce, 0xc4 => 0x82d4,
    0xc5 => 0x8dc6, 0xc6 => 0x90b0, 0xc7 => 0x98b1, 0xc8 => 0xfa04,
    0xc9 => 0x64c7, 0xca => 0x6fa4, 0xcb => 0x6491, 0xcc => 0x6504,
    0xcd => 0x514e, 0xce => 0x5410, 0xcf => 0x571f, 0xd0 => 0x8a0e,
    0xd1 => 0x615f, 0xd2 => 0x6876, 0xd3 => 0xfa05, 0xd4 => 0x75db,
    0xd5 => 0x7b52, 0xd6 => 0x7d71, 0xd7 => 0x901a, 0xd8 => 0x5806,
    0xd9 => 0x69cc, 0xda => 0x817f, 0xdb => 0x892a, 0xdc => 0x9000,
    0xdd => 0x9839, 0xde => 0x5078, 0xdf => 0x5957, 0xe0 => 0x59ac,
    0xe1 => 0x6295, 0xe2 => 0x900f, 0xe3 => 0x9b2a, 0xe4 => 0x615d,
    0xe5 => 0x7279, 0xe6 => 0x95d6, 0xe7 => 0x5761, 0xe8 => 0x5a46,
    0xe9 => 0x5df4, 0xea => 0x628a, 0xeb => 0x64ad, 0xec => 0x64fa,
    0xed => 0x6777, 0xee => 0x6ce2, 0xef => 0x6d3e, 0xf0 => 0x722c,
    0xf1 => 0x7436, 0xf2 => 0x7834, 0xf3 => 0x7f77, 0xf4 => 0x82ad,
    0xf5 => 0x8ddb, 0xf6 => 0x9817, 0xf7 => 0x5224, 0xf8 => 0x5742,
    0xf9 => 0x677f, 0xfa => 0x7248, 0xfb => 0x74e3, 0xfc => 0x8ca9,
    0xfd => 0x8fa6, 0xfe => 0x9211,
  },
  0xf8 => {
    0xa1 => 0x962a, 0xa2 => 0x516b, 0xa3 => 0x53ed, 0xa4 => 0x634c,
    0xa5 => 0x4f69, 0xa6 => 0x5504, 0xa7 => 0x6096, 0xa8 => 0x6557,
    0xa9 => 0x6c9b, 0xaa => 0x6d7f, 0xab => 0x724c, 0xac => 0x72fd,
    0xad => 0x7a17, 0xae => 0x8987, 0xaf => 0x8c9d, 0xb0 => 0x5f6d,
    0xb1 => 0x6f8e, 0xb2 => 0x70f9, 0xb3 => 0x81a8, 0xb4 => 0x610e,
    0xb5 => 0x4fbf, 0xb6 => 0x504f, 0xb7 => 0x6241, 0xb8 => 0x7247,
    0xb9 => 0x7bc7, 0xba => 0x7de8, 0xbb => 0x7fe9, 0xbc => 0x904d,
    0xbd => 0x97ad, 0xbe => 0x9a19, 0xbf => 0x8cb6, 0xc0 => 0x576a,
    0xc1 => 0x5e73, 0xc2 => 0x67b0, 0xc3 => 0x840d, 0xc4 => 0x8a55,
    0xc5 => 0x5420, 0xc6 => 0x5b16, 0xc7 => 0x5e63, 0xc8 => 0x5ee2,
    0xc9 => 0x5f0a, 0xca => 0x6583, 0xcb => 0x80ba, 0xcc => 0x853d,
    0xcd => 0x9589, 0xce => 0x965b, 0xcf => 0x4f48, 0xd0 => 0x5305,
    0xd1 => 0x530d, 0xd2 => 0x530f, 0xd3 => 0x5486, 0xd4 => 0x54fa,
    0xd5 => 0x5703, 0xd6 => 0x5e03, 0xd7 => 0x6016, 0xd8 => 0x629b,
    0xd9 => 0x62b1, 0xda => 0x6355, 0xdb => 0xfa06, 0xdc => 0x6ce1,
    0xdd => 0x6d66, 0xde => 0x75b1, 0xdf => 0x7832, 0xe0 => 0x80de,
    0xe1 => 0x812f, 0xe2 => 0x82de, 0xe3 => 0x8461, 0xe4 => 0x84b2,
    0xe5 => 0x888d, 0xe6 => 0x8912, 0xe7 => 0x900b, 0xe8 => 0x92ea,
    0xe9 => 0x98fd, 0xea => 0x9b91, 0xeb => 0x5e45, 0xec => 0x66b4,
    0xed => 0x66dd, 0xee => 0x7011, 0xef => 0x7206, 0xf0 => 0xfa07,
    0xf1 => 0x4ff5, 0xf2 => 0x527d, 0xf3 => 0x5f6a, 0xf4 => 0x6153,
    0xf5 => 0x6753, 0xf6 => 0x6a19, 0xf7 => 0x6f02, 0xf8 => 0x74e2,
    0xf9 => 0x7968, 0xfa => 0x8868, 0xfb => 0x8c79, 0xfc => 0x98c7,
    0xfd => 0x98c4, 0xfe => 0x9a43,
  },
  0xf9 => {
    0xa1 => 0x54c1, 0xa2 => 0x7a1f, 0xa3 => 0x6953, 0xa4 => 0x8af7,
    0xa5 => 0x8c4a, 0xa6 => 0x98a8, 0xa7 => 0x99ae, 0xa8 => 0x5f7c,
    0xa9 => 0x62ab, 0xaa => 0x75b2, 0xab => 0x76ae, 0xac => 0x88ab,
    0xad => 0x907f, 0xae => 0x9642, 0xaf => 0x5339, 0xb0 => 0x5f3c,
    0xb1 => 0x5fc5, 0xb2 => 0x6ccc, 0xb3 => 0x73cc, 0xb4 => 0x7562,
    0xb5 => 0x758b, 0xb6 => 0x7b46, 0xb7 => 0x82fe, 0xb8 => 0x999d,
    0xb9 => 0x4e4f, 0xba => 0x903c, 0xbb => 0x4e0b, 0xbc => 0x4f55,
    0xbd => 0x53a6, 0xbe => 0x590f, 0xbf => 0x5ec8, 0xc0 => 0x6630,
    0xc1 => 0x6cb3, 0xc2 => 0x7455, 0xc3 => 0x8377, 0xc4 => 0x8766,
    0xc5 => 0x8cc0, 0xc6 => 0x9050, 0xc7 => 0x971e, 0xc8 => 0x9c15,
    0xc9 => 0x58d1, 0xca => 0x5b78, 0xcb => 0x8650, 0xcc => 0x8b14,
    0xcd => 0x9db4, 0xce => 0x5bd2, 0xcf => 0x6068, 0xd0 => 0x608d,
    0xd1 => 0x65f1, 0xd2 => 0x6c57, 0xd3 => 0x6f22, 0xd4 => 0x6fa3,
    0xd5 => 0x701a, 0xd6 => 0x7f55, 0xd7 => 0x7ff0, 0xd8 => 0x9591,
    0xd9 => 0x9592, 0xda => 0x9650, 0xdb => 0x97d3, 0xdc => 0x5272,
    0xdd => 0x8f44, 0xde => 0x51fd, 0xdf => 0x542b, 0xe0 => 0x54b8,
    0xe1 => 0x5563, 0xe2 => 0x558a, 0xe3 => 0x6abb, 0xe4 => 0x6db5,
    0xe5 => 0x7dd8, 0xe6 => 0x8266, 0xe7 => 0x929c, 0xe8 => 0x9677,
    0xe9 => 0x9e79, 0xea => 0x5408, 0xeb => 0x54c8, 0xec => 0x76d2,
    0xed => 0x86e4, 0xee => 0x95a4, 0xef => 0x95d4, 0xf0 => 0x965c,
    0xf1 => 0x4ea2, 0xf2 => 0x4f09, 0xf3 => 0x59ee, 0xf4 => 0x5ae6,
    0xf5 => 0x5df7, 0xf6 => 0x6052, 0xf7 => 0x6297, 0xf8 => 0x676d,
    0xf9 => 0x6841, 0xfa => 0x6c86, 0xfb => 0x6e2f, 0xfc => 0x7f38,
    0xfd => 0x809b, 0xfe => 0x822a,
  },
  0xfa => {
    0xa1 => 0xfa08, 0xa2 => 0xfa09, 0xa3 => 0x9805, 0xa4 => 0x4ea5,
    0xa5 => 0x5055, 0xa6 => 0x54b3, 0xa7 => 0x5793, 0xa8 => 0x595a,
    0xa9 => 0x5b69, 0xaa => 0x5bb3, 0xab => 0x61c8, 0xac => 0x6977,
    0xad => 0x6d77, 0xae => 0x7023, 0xaf => 0x87f9, 0xb0 => 0x89e3,
    0xb1 => 0x8a72, 0xb2 => 0x8ae7, 0xb3 => 0x9082, 0xb4 => 0x99ed,
    0xb5 => 0x9ab8, 0xb6 => 0x52be, 0xb7 => 0x6838, 0xb8 => 0x5016,
    0xb9 => 0x5e78, 0xba => 0x674f, 0xbb => 0x8347, 0xbc => 0x884c,
    0xbd => 0x4eab, 0xbe => 0x5411, 0xbf => 0x56ae, 0xc0 => 0x73e6,
    0xc1 => 0x9115, 0xc2 => 0x97ff, 0xc3 => 0x9909, 0xc4 => 0x9957,
    0xc5 => 0x9999, 0xc6 => 0x5653, 0xc7 => 0x589f, 0xc8 => 0x865b,
    0xc9 => 0x8a31, 0xca => 0x61b2, 0xcb => 0x6af6, 0xcc => 0x737b,
    0xcd => 0x8ed2, 0xce => 0x6b47, 0xcf => 0x96aa, 0xd0 => 0x9a57,
    0xd1 => 0x5955, 0xd2 => 0x7200, 0xd3 => 0x8d6b, 0xd4 => 0x9769,
    0xd5 => 0x4fd4, 0xd6 => 0x5cf4, 0xd7 => 0x5f26, 0xd8 => 0x61f8,
    0xd9 => 0x665b, 0xda => 0x6ceb, 0xdb => 0x70ab, 0xdc => 0x7384,
    0xdd => 0x73b9, 0xde => 0x73fe, 0xdf => 0x7729, 0xe0 => 0x774d,
    0xe1 => 0x7d43, 0xe2 => 0x7d62, 0xe3 => 0x7e23, 0xe4 => 0x8237,
    0xe5 => 0x8852, 0xe6 => 0xfa0a, 0xe7 => 0x8ce2, 0xe8 => 0x9249,
    0xe9 => 0x986f, 0xea => 0x5b51, 0xeb => 0x7a74, 0xec => 0x8840,
    0xed => 0x9801, 0xee => 0x5acc, 0xef => 0x4fe0, 0xf0 => 0x5354,
    0xf1 => 0x593e, 0xf2 => 0x5cfd, 0xf3 => 0x633e, 0xf4 => 0x6d79,
    0xf5 => 0x72f9, 0xf6 => 0x8105, 0xf7 => 0x8107, 0xf8 => 0x83a2,
    0xf9 => 0x92cf, 0xfa => 0x9830, 0xfb => 0x4ea8, 0xfc => 0x5144,
    0xfd => 0x5211, 0xfe => 0x578b,
  },
  0xfb => {
    0xa1 => 0x5f62, 0xa2 => 0x6cc2, 0xa3 => 0x6ece, 0xa4 => 0x7005,
    0xa5 => 0x7050, 0xa6 => 0x70af, 0xa7 => 0x7192, 0xa8 => 0x73e9,
    0xa9 => 0x7469, 0xaa => 0x834a, 0xab => 0x87a2, 0xac => 0x8861,
    0xad => 0x9008, 0xae => 0x90a2, 0xaf => 0x93a3, 0xb0 => 0x99a8,
    0xb1 => 0x516e, 0xb2 => 0x5f57, 0xb3 => 0x60e0, 0xb4 => 0x6167,
    0xb5 => 0x66b3, 0xb6 => 0x8559, 0xb7 => 0x8e4a, 0xb8 => 0x91af,
    0xb9 => 0x978b, 0xba => 0x4e4e, 0xbb => 0x4e92, 0xbc => 0x547c,
    0xbd => 0x58d5, 0xbe => 0x58fa, 0xbf => 0x597d, 0xc0 => 0x5cb5,
    0xc1 => 0x5f27, 0xc2 => 0x6236, 0xc3 => 0x6248, 0xc4 => 0x660a,
    0xc5 => 0x6667, 0xc6 => 0x6beb, 0xc7 => 0x6d69, 0xc8 => 0x6dcf,
    0xc9 => 0x6e56, 0xca => 0x6ef8, 0xcb => 0x6f94, 0xcc => 0x6fe0,
    0xcd => 0x6fe9, 0xce => 0x705d, 0xcf => 0x72d0, 0xd0 => 0x7425,
    0xd1 => 0x745a, 0xd2 => 0x74e0, 0xd3 => 0x7693, 0xd4 => 0x795c,
    0xd5 => 0x7cca, 0xd6 => 0x7e1e, 0xd7 => 0x80e1, 0xd8 => 0x82a6,
    0xd9 => 0x846b, 0xda => 0x84bf, 0xdb => 0x864e, 0xdc => 0x865f,
    0xdd => 0x8774, 0xde => 0x8b77, 0xdf => 0x8c6a, 0xe0 => 0x93ac,
    0xe1 => 0x9800, 0xe2 => 0x9865, 0xe3 => 0x60d1, 0xe4 => 0x6216,
    0xe5 => 0x9177, 0xe6 => 0x5a5a, 0xe7 => 0x660f, 0xe8 => 0x6df7,
    0xe9 => 0x6e3e, 0xea => 0x743f, 0xeb => 0x9b42, 0xec => 0x5ffd,
    0xed => 0x60da, 0xee => 0x7b0f, 0xef => 0x54c4, 0xf0 => 0x5f18,
    0xf1 => 0x6c5e, 0xf2 => 0x6cd3, 0xf3 => 0x6d2a, 0xf4 => 0x70d8,
    0xf5 => 0x7d05, 0xf6 => 0x8679, 0xf7 => 0x8a0c, 0xf8 => 0x9d3b,
    0xf9 => 0x5316, 0xfa => 0x548c, 0xfb => 0x5b05, 0xfc => 0x6a3a,
    0xfd => 0x706b, 0xfe => 0x7575,
  },
  0xfc => {
    0xa1 => 0x798d, 0xa2 => 0x79be, 0xa3 => 0x82b1, 0xa4 => 0x83ef,
    0xa5 => 0x8a71, 0xa6 => 0x8b41, 0xa7 => 0x8ca8, 0xa8 => 0x9774,
    0xa9 => 0xfa0b, 0xaa => 0x64f4, 0xab => 0x652b, 0xac => 0x78ba,
    0xad => 0x78bb, 0xae => 0x7a6b, 0xaf => 0x4e38, 0xb0 => 0x559a,
    0xb1 => 0x5950, 0xb2 => 0x5ba6, 0xb3 => 0x5e7b, 0xb4 => 0x60a3,
    0xb5 => 0x63db, 0xb6 => 0x6b61, 0xb7 => 0x6665, 0xb8 => 0x6853,
    0xb9 => 0x6e19, 0xba => 0x7165, 0xbb => 0x74b0, 0xbc => 0x7d08,
    0xbd => 0x9084, 0xbe => 0x9a69, 0xbf => 0x9c25, 0xc0 => 0x6d3b,
    0xc1 => 0x6ed1, 0xc2 => 0x733e, 0xc3 => 0x8c41, 0xc4 => 0x95ca,
    0xc5 => 0x51f0, 0xc6 => 0x5e4c, 0xc7 => 0x5fa8, 0xc8 => 0x604d,
    0xc9 => 0x60f6, 0xca => 0x6130, 0xcb => 0x614c, 0xcc => 0x6643,
    0xcd => 0x6644, 0xce => 0x69a5, 0xcf => 0x6cc1, 0xd0 => 0x6e5f,
    0xd1 => 0x6ec9, 0xd2 => 0x6f62, 0xd3 => 0x714c, 0xd4 => 0x749c,
    0xd5 => 0x7687, 0xd6 => 0x7bc1, 0xd7 => 0x7c27, 0xd8 => 0x8352,
    0xd9 => 0x8757, 0xda => 0x9051, 0xdb => 0x968d, 0xdc => 0x9ec3,
    0xdd => 0x532f, 0xde => 0x56de, 0xdf => 0x5efb, 0xe0 => 0x5f8a,
    0xe1 => 0x6062, 0xe2 => 0x6094, 0xe3 => 0x61f7, 0xe4 => 0x6666,
    0xe5 => 0x6703, 0xe6 => 0x6a9c, 0xe7 => 0x6dee, 0xe8 => 0x6fae,
    0xe9 => 0x7070, 0xea => 0x736a, 0xeb => 0x7e6a, 0xec => 0x81be,
    0xed => 0x8334, 0xee => 0x86d4, 0xef => 0x8aa8, 0xf0 => 0x8cc4,
    0xf1 => 0x5283, 0xf2 => 0x7372, 0xf3 => 0x5b96, 0xf4 => 0x6a6b,
    0xf5 => 0x9404, 0xf6 => 0x54ee, 0xf7 => 0x5686, 0xf8 => 0x5b5d,
    0xf9 => 0x6548, 0xfa => 0x6585, 0xfb => 0x66c9, 0xfc => 0x689f,
    0xfd => 0x6d8d, 0xfe => 0x6dc6,
  },
  0xfd => {
    0xa1 => 0x723b, 0xa2 => 0x80b4, 0xa3 => 0x9175, 0xa4 => 0x9a4d,
    0xa5 => 0x4faf, 0xa6 => 0x5019, 0xa7 => 0x539a, 0xa8 => 0x540e,
    0xa9 => 0x543c, 0xaa => 0x5589, 0xab => 0x55c5, 0xac => 0x5e3f,
    0xad => 0x5f8c, 0xae => 0x673d, 0xaf => 0x7166, 0xb0 => 0x73dd,
    0xb1 => 0x9005, 0xb2 => 0x52db, 0xb3 => 0x52f3, 0xb4 => 0x5864,
    0xb5 => 0x58ce, 0xb6 => 0x7104, 0xb7 => 0x718f, 0xb8 => 0x71fb,
    0xb9 => 0x85b0, 0xba => 0x8a13, 0xbb => 0x6688, 0xbc => 0x85a8,
    0xbd => 0x55a7, 0xbe => 0x6684, 0xbf => 0x714a, 0xc0 => 0x8431,
    0xc1 => 0x5349, 0xc2 => 0x5599, 0xc3 => 0x6bc1, 0xc4 => 0x5f59,
    0xc5 => 0x5fbd, 0xc6 => 0x63ee, 0xc7 => 0x6689, 0xc8 => 0x7147,
    0xc9 => 0x8af1, 0xca => 0x8f1d, 0xcb => 0x9ebe, 0xcc => 0x4f11,
    0xcd => 0x643a, 0xce => 0x70cb, 0xcf => 0x7566, 0xd0 => 0x8667,
    0xd1 => 0x6064, 0xd2 => 0x8b4e, 0xd3 => 0x9df8, 0xd4 => 0x5147,
    0xd5 => 0x51f6, 0xd6 => 0x5308, 0xd7 => 0x6d36, 0xd8 => 0x80f8,
    0xd9 => 0x9ed1, 0xda => 0x6615, 0xdb => 0x6b23, 0xdc => 0x7098,
    0xdd => 0x75d5, 0xde => 0x5403, 0xdf => 0x5c79, 0xe0 => 0x7d07,
    0xe1 => 0x8a16, 0xe2 => 0x6b20, 0xe3 => 0x6b3d, 0xe4 => 0x6b46,
    0xe5 => 0x5438, 0xe6 => 0x6070, 0xe7 => 0x6d3d, 0xe8 => 0x7fd5,
    0xe9 => 0x8208, 0xea => 0x50d6, 0xeb => 0x51de, 0xec => 0x559c,
    0xed => 0x566b, 0xee => 0x56cd, 0xef => 0x59ec, 0xf0 => 0x5b09,
    0xf1 => 0x5e0c, 0xf2 => 0x6199, 0xf3 => 0x6198, 0xf4 => 0x6231,
    0xf5 => 0x665e, 0xf6 => 0x66e6, 0xf7 => 0x7199, 0xf8 => 0x71b9,
    0xf9 => 0x71ba, 0xfa => 0x72a7, 0xfb => 0x79a7, 0xfc => 0x7a00,
    0xfd => 0x7fb2, 0xfe => 0x8a70,
  },
);

1; # end
