#!/usr/bin/env python3
"""
Simple metadata writing test
"""

import os
import subprocess
import mutagen
from mutagen.id3 import ID3, TIT2, TPE1, COMM, TXXX

def find_exiftool():
    """Find ExifTool executable"""
    # Check local directory first
    script_dir = os.path.dirname(os.path.abspath(__file__))
    local_paths = [
        os.path.join(script_dir, 'exiftool-13.32_64', 'exiftool.exe'),
        os.path.join(script_dir, 'exiftool-13.32_64', 'exiftool(-k).exe'),
        os.path.join(script_dir, 'exiftool', 'exiftool.exe'),
        os.path.join(script_dir, 'exiftool', 'exiftool(-k).exe')
    ]

    for path in local_paths:
        if os.path.exists(path):
            print(f"Found local ExifTool: {path}")
            return path

    # Check PATH
    try:
        result = subprocess.run(['exiftool', '-ver'], capture_output=True, text=True, timeout=5)
        if result.returncode == 0:
            print("Found ExifTool in PATH")
            return 'exiftool'
    except:
        pass

    print("ExifTool not found")
    return None

def test_exiftool():
    print("=== Testing ExifTool ===")
    exiftool_path = find_exiftool()
    if not exiftool_path:
        print("✗ ExifTool not found")
        return False, None

    try:
        result = subprocess.run([exiftool_path, '-ver'], capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✓ ExifTool version: {result.stdout.strip()}")
            return True, exiftool_path
        else:
            print("✗ ExifTool not working")
            return False, None
    except Exception as e:
        print(f"✗ ExifTool error: {e}")
        return False, None

def test_mutagen_mp3(file_path):
    print(f"\n=== Testing Mutagen MP3: {file_path} ===")
    try:
        # Open MP3 file
        audiofile = mutagen.File(file_path)
        if audiofile is None:
            print("✗ Could not open file")
            return False

        print(f"✓ File type: {type(audiofile)}")

        # Clear and add new tags
        audiofile.delete()
        audiofile.add_tags()

        # Add simple metadata
        audiofile.tags.add(TIT2(encoding=3, text="Test Title"))
        audiofile.tags.add(TPE1(encoding=3, text="Test Artist"))
        audiofile.tags.add(COMM(encoding=3, lang='eng', desc='', text="Test Comment"))
        audiofile.tags.add(TXXX(encoding=3, desc="CustomField", text="Test Custom"))

        # Save
        audiofile.save()
        print("✓ Metadata written successfully")

        # Verify
        audiofile = mutagen.File(file_path)
        if audiofile.tags:
            print("✓ Verification:")
            for tag in audiofile.tags.values():
                print(f"  {tag}")

        return True

    except Exception as e:
        print(f"✗ Error: {e}")
        return False

def test_exiftool_write(file_path, exiftool_path):
    print(f"\n=== Testing ExifTool Write: {file_path} ===")
    try:
        cmd = [
            exiftool_path,
            '-overwrite_original',
            '-Title=Test Title ExifTool',
            '-Artist=Test Artist ExifTool',
            '-Comment=Test Comment ExifTool',
            file_path
        ]

        print(f"Command: {' '.join(cmd)}")
        result = subprocess.run(cmd, capture_output=True, text=True)
        if result.returncode == 0:
            print("✓ ExifTool write successful")

            # Verify
            verify_cmd = [exiftool_path, '-Title', '-Artist', '-Comment', file_path]
            verify_result = subprocess.run(verify_cmd, capture_output=True, text=True)
            print("✓ Verification:")
            print(verify_result.stdout)
            return True
        else:
            print(f"✗ ExifTool error: {result.stderr}")
            return False

    except Exception as e:
        print(f"✗ Error: {e}")
        return False

def main():
    print("Simple Metadata Writing Test")
    print("=" * 40)

    # Test ExifTool
    exiftool_ok, exiftool_path = test_exiftool()

    # Get test file
    file_path = input("\nEnter path to MP3 test file: ").strip().strip('"')

    if not os.path.exists(file_path):
        print("File not found!")
        return

    if not file_path.lower().endswith('.mp3'):
        print("Please use an MP3 file for this test")
        return

    # Make backup
    backup_path = file_path + ".backup"
    import shutil
    shutil.copy2(file_path, backup_path)
    print(f"✓ Backup created: {backup_path}")

    # Test methods
    print(f"\nFile: {file_path}")
    print(f"Size: {os.path.getsize(file_path)} bytes")
    print(f"Writable: {os.access(file_path, os.W_OK)}")

    # Test Mutagen
    mutagen_ok = test_mutagen_mp3(file_path)

    # Test ExifTool if available
    if exiftool_ok:
        exiftool_write_ok = test_exiftool_write(file_path, exiftool_path)

    print(f"\n=== Results ===")
    print(f"ExifTool available: {exiftool_ok}")
    print(f"Mutagen write: {mutagen_ok}")
    if exiftool_ok:
        print(f"ExifTool write: {exiftool_write_ok}")

    # Restore backup
    restore = input("\nRestore backup? (y/n): ").lower().startswith('y')
    if restore:
        shutil.move(backup_path, file_path)
        print("✓ Backup restored")
    else:
        os.remove(backup_path)
        print("✓ Backup deleted")

if __name__ == "__main__":
    main()
    input("\nPress Enter to exit...")
