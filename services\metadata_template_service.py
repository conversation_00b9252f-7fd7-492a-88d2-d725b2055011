"""
Metadata template service for providing predefined metadata field templates
"""

class MetadataTemplateService:
    def __init__(self):
        self.templates = {
            'image': {
                'Basic Info': {
                    'Title': '',
                    'Description': '',
                    'Keywords': '',
                    'Subject': '',
                    'Copyright': '',
                    'Creator': '',
                    'CreatorTool': ''
                },
                'Camera Info': {
                    'Make': '',
                    'Model': '',
                    'LensModel': '',
                    'FocalLength': '',
                    'FNumber': '',
                    'ExposureTime': '',
                    'ISO': '',
                    'Flash': ''
                },
                'Location': {
                    'GPSLatitude': '',
                    'GPSLongitude': '',
                    'GPSAltitude': '',
                    'Location': '',
                    'City': '',
                    'State': '',
                    'Country': ''
                },
                'Date & Time': {
                    'DateTime': '',
                    'DateTimeOriginal': '',
                    'DateTimeDigitized': '',
                    'CreateDate': '',
                    'ModifyDate': ''
                }
            },
            'video': {
                'Basic Info': {
                    'Title': '',
                    'Description': '',
                    'Genre': '',
                    'Director': '',
                    'Producer': '',
                    'Writer': '',
                    'Copyright': '',
                    'Comment': ''
                },
                'Technical': {
                    'Duration': '',
                    'Bitrate': '',
                    'FrameRate': '',
                    'Resolution': '',
                    'VideoCodec': '',
                    'AudioCodec': '',
                    'AspectRatio': '',
                    'ColorSpace': ''
                },
                'Production': {
                    'Studio': '',
                    'ReleaseDate': '',
                    'ProductionYear': '',
                    'Language': '',
                    'Subtitles': '',
                    'Rating': '',
                    'Budget': '',
                    'BoxOffice': ''
                }
            },
            'audio': {
                'Basic Info': {
                    'Title': '',
                    'Artist': '',
                    'Album': '',
                    'AlbumArtist': '',
                    'Genre': '',
                    'Year': '',
                    'Track': '',
                    'Disc': ''
                },
                'Details': {
                    'Composer': '',
                    'Conductor': '',
                    'Publisher': '',
                    'Label': '',
                    'ISRC': '',
                    'Catalog': '',
                    'Barcode': '',
                    'Copyright': ''
                },
                'Technical': {
                    'Duration': '',
                    'Bitrate': '',
                    'SampleRate': '',
                    'Channels': '',
                    'Codec': '',
                    'Encoder': '',
                    'Quality': '',
                    'ReplayGain': ''
                },
                'Personal': {
                    'Rating': '',
                    'PlayCount': '',
                    'LastPlayed': '',
                    'Comment': '',
                    'Mood': '',
                    'Tempo': '',
                    'Key': '',
                    'Lyrics': ''
                }
            },
            'common': {
                'General': {
                    'Title': '',
                    'Description': '',
                    'Keywords': '',
                    'Subject': '',
                    'Creator': '',
                    'Copyright': '',
                    'Language': '',
                    'Comment': ''
                },
                'Custom': {
                    'CustomField1': '',
                    'CustomField2': '',
                    'CustomField3': '',
                    'Project': '',
                    'Client': '',
                    'Status': '',
                    'Priority': '',
                    'Version': ''
                }
            }
        }
    
    def get_templates_for_file_type(self, file_type):
        """Get metadata templates for specific file type"""
        templates = {}
        
        # Add file-type specific templates
        if file_type in self.templates:
            templates.update(self.templates[file_type])
        
        # Always add common templates
        templates.update(self.templates['common'])
        
        return templates
    
    def get_all_fields_for_file_type(self, file_type):
        """Get all available fields for file type as flat list"""
        templates = self.get_templates_for_file_type(file_type)
        all_fields = []
        
        for category, fields in templates.items():
            for field_name in fields.keys():
                if field_name not in all_fields:
                    all_fields.append(field_name)
        
        return sorted(all_fields)
    
    def get_field_suggestions(self, file_type, search_term=""):
        """Get field suggestions based on search term"""
        all_fields = self.get_all_fields_for_file_type(file_type)
        
        if not search_term:
            return all_fields
        
        # Filter fields that contain search term
        search_lower = search_term.lower()
        suggestions = [field for field in all_fields 
                      if search_lower in field.lower()]
        
        return suggestions
    
    def get_popular_fields_for_file_type(self, file_type):
        """Get most commonly used fields for file type"""
        popular_fields = {
            'image': ['Title', 'Description', 'Keywords', 'Creator', 'Copyright', 
                     'Make', 'Model', 'DateTime', 'Location'],
            'video': ['Title', 'Description', 'Genre', 'Director', 'Duration', 
                     'Resolution', 'ReleaseDate', 'Copyright'],
            'audio': ['Title', 'Artist', 'Album', 'Genre', 'Year', 'Track', 
                     'Duration', 'Bitrate', 'Comment'],
            'unknown': ['Title', 'Description', 'Creator', 'Copyright', 'Comment']
        }
        
        return popular_fields.get(file_type, popular_fields['unknown'])
    
    def get_field_description(self, field_name):
        """Get description for metadata field"""
        descriptions = {
            'Title': 'Title or name of the content',
            'Description': 'Detailed description of the content',
            'Keywords': 'Keywords or tags for searching',
            'Subject': 'Subject or topic of the content',
            'Creator': 'Person or entity who created the content',
            'Copyright': 'Copyright information',
            'Artist': 'Artist or performer name',
            'Album': 'Album or collection name',
            'Genre': 'Genre or category',
            'Year': 'Year of creation or release',
            'Track': 'Track number',
            'Duration': 'Length or duration',
            'Make': 'Camera or device manufacturer',
            'Model': 'Camera or device model',
            'DateTime': 'Date and time of creation',
            'Location': 'Geographic location',
            'Director': 'Director of the video',
            'Resolution': 'Video resolution (e.g., 1920x1080)',
            'Bitrate': 'Audio/video bitrate',
            'Comment': 'Additional comments or notes'
        }
        
        return descriptions.get(field_name, f'Custom field: {field_name}')
    
    def validate_field_value(self, field_name, value, file_type):
        """Validate field value based on field type"""
        # Basic validation rules
        validation_rules = {
            'Year': lambda v: v.isdigit() and 1900 <= int(v) <= 2100,
            'Track': lambda v: v.isdigit() and int(v) > 0,
            'Duration': lambda v: ':' in v or v.isdigit(),
            'Bitrate': lambda v: v.replace('kbps', '').replace('Kbps', '').strip().isdigit(),
            'ISO': lambda v: v.isdigit(),
            'FNumber': lambda v: v.replace('f/', '').replace('F/', '').replace('.', '').isdigit()
        }
        
        if field_name in validation_rules:
            try:
                return validation_rules[field_name](value)
            except:
                return False
        
        # Default: accept any non-empty value
        return len(value.strip()) > 0
