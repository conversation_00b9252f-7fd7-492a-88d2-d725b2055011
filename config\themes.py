"""
Theme configuration for Gema application
Light theme: tone d1c2ed (light purple)
Dark theme: tone 8a6dc1 (dark purple)
"""

class ThemeConfig:
    LIGHT_THEME = {
        'name': 'light',
        'primary': '#d1c2ed',
        'primary_dark': '#b8a5d9',
        'primary_light': '#e6ddf4',
        'background': '#ffffff',
        'surface': '#f8f6fc',
        'text_primary': '#2d1b3d',
        'text_secondary': '#5a4a6a',
        'accent': '#9c7cc7',
        'border': '#d1c2ed',
        'hover': '#c4b3e3',
        'active': '#b8a5d9'
    }
    
    DARK_THEME = {
        'name': 'dark',
        'primary': '#8a6dc1',
        'primary_dark': '#6b5496',
        'primary_light': '#a085d4',
        'background': '#1a1625',
        'surface': '#2a2235',
        'text_primary': '#e6ddf4',
        'text_secondary': '#c4b3e3',
        'accent': '#9c7cc7',
        'border': '#8a6dc1',
        'hover': '#9c7cc7',
        'active': '#a085d4'
    }
    
    @classmethod
    def get_theme(cls, theme_name='light'):
        if theme_name == 'dark':
            return cls.DARK_THEME
        return cls.LIGHT_THEME
