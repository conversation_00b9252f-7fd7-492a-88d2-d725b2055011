# Panduan Template Field Metadata

## Overview

Aplikasi Gema sekarang dilengkapi dengan sistem template field metadata yang memudahkan pengguna untuk menambahkan field metadata tanpa perlu mengetik manual. Sistem ini menyediakan field yang sudah terdefinisi berdasarkan tipe file dan kategori.

## Fitur Template Field

### 1. Quick Add (⚡)
- Menambahkan field metadata yang paling populer untuk tipe file tertentu
- Satu klik untuk menambah semua field penting
- Tidak akan menambah field yang sudah ada

### 2. Add Field Dialog (➕)
Dialog pemilihan field dengan fitur lengkap:

#### Tab Categories:
- **⭐ Popular**: Field yang paling sering digunakan
- **Basic Info**: Informasi dasar (Title, Description, Creator, dll)
- **Technical**: Informasi teknis (Resolution, Bitrate, Codec, dll)
- **Camera Info**: Informasi kamera (untuk gambar)
- **Production**: Informasi produksi (untuk video)
- **Personal**: Tag personal (untuk audio)
- **General**: Field umum untuk semua tipe file

#### Fitur Dialog:
- **Search**: Cari field berdasarkan nama
- **Multiple Selection**: Pilih beberapa field sekaligus
- **Preview**: Lihat field yang dipilih sebelum menambah
- **Remove**: Hapus field dari selection dengan double-click

## Template Field Berdasarkan Tipe File

### Image Files (.jpg, .png, .tiff, dll)

#### Popular Fields:
- Title, Description, Keywords, Creator, Copyright
- Make, Model, DateTime, Location

#### Categories:
1. **Basic Info**: Title, Description, Keywords, Subject, Copyright, Creator, CreatorTool
2. **Camera Info**: Make, Model, LensModel, FocalLength, FNumber, ExposureTime, ISO, Flash
3. **Location**: GPSLatitude, GPSLongitude, GPSAltitude, Location, City, State, Country
4. **Date & Time**: DateTime, DateTimeOriginal, DateTimeDigitized, CreateDate, ModifyDate

### Video Files (.mp4, .avi, .mov, dll)

#### Popular Fields:
- Title, Description, Genre, Director, Duration
- Resolution, ReleaseDate, Copyright

#### Categories:
1. **Basic Info**: Title, Description, Genre, Director, Producer, Writer, Copyright, Comment
2. **Technical**: Duration, Bitrate, FrameRate, Resolution, VideoCodec, AudioCodec, AspectRatio, ColorSpace
3. **Production**: Studio, ReleaseDate, ProductionYear, Language, Subtitles, Rating, Budget, BoxOffice

### Audio Files (.mp3, .flac, .m4a, dll)

#### Popular Fields:
- Title, Artist, Album, Genre, Year, Track
- Duration, Bitrate, Comment

#### Categories:
1. **Basic Info**: Title, Artist, Album, AlbumArtist, Genre, Year, Track, Disc
2. **Details**: Composer, Conductor, Publisher, Label, ISRC, Catalog, Barcode, Copyright
3. **Technical**: Duration, Bitrate, SampleRate, Channels, Codec, Encoder, Quality, ReplayGain
4. **Personal**: Rating, PlayCount, LastPlayed, Comment, Mood, Tempo, Key, Lyrics

### Common Fields (Semua Tipe File)

#### General:
- Title, Description, Keywords, Subject, Creator, Copyright, Language, Comment

#### Custom:
- CustomField1, CustomField2, CustomField3, Project, Client, Status, Priority, Version

## Fitur Validasi

### Real-time Validation:
- **Visual Feedback**: Field dengan nilai tidak valid akan ditandai dengan background merah muda
- **Format Checking**: Validasi format untuk field tertentu (Year, Track, Duration, dll)
- **Type Checking**: Memastikan nilai sesuai dengan tipe field

### Validation Rules:
- **Year**: Harus angka antara 1900-2100
- **Track**: Harus angka positif
- **Duration**: Format MM:SS atau detik
- **Bitrate**: Angka dengan atau tanpa "kbps"
- **ISO**: Harus angka
- **FNumber**: Format f/2.8 atau angka

## Tooltips dan Help

### Field Descriptions:
Setiap field memiliki tooltip yang menjelaskan fungsinya:
- Hover mouse pada field key untuk melihat deskripsi
- Deskripsi dalam bahasa Indonesia yang mudah dipahami
- Contoh format untuk field yang memerlukan format khusus

### Common Field Descriptions:
- **Title**: Judul atau nama konten
- **Description**: Deskripsi detail konten
- **Keywords**: Kata kunci untuk pencarian
- **Creator**: Pembuat konten
- **Copyright**: Informasi hak cipta
- **Make/Model**: Merk dan model kamera/perangkat
- **DateTime**: Tanggal dan waktu pembuatan
- **Location**: Lokasi geografis
- **Duration**: Durasi atau panjang konten

## Tips Penggunaan

### 1. Workflow Efisien:
1. Load file media
2. Klik "⚡ Quick Add" untuk field populer
3. Gunakan "➕ Add Field" untuk field tambahan
4. Edit nilai field sesuai kebutuhan
5. Save metadata

### 2. Best Practices:
- Gunakan field standar untuk kompatibilitas maksimal
- Isi field populer terlebih dahulu
- Gunakan format yang konsisten untuk field seperti Date, Duration
- Manfaatkan validation feedback untuk memastikan format benar

### 3. Custom Fields:
- Gunakan field Custom untuk kebutuhan spesifik
- Beri nama yang deskriptif untuk field custom
- Konsisten dalam penamaan across files

## Keyboard Shortcuts

### Dalam Dialog Field Selector:
- **Enter**: Tambah field yang dipilih
- **Double-click**: Tambah field dari list
- **Escape**: Tutup dialog tanpa menambah
- **Tab**: Navigasi antar tab categories

### Dalam Metadata Editor:
- **Ctrl+A**: Quick Add popular fields
- **Ctrl+F**: Open Add Field dialog
- **Delete**: Hapus field yang dipilih

## Troubleshooting

### Field Tidak Muncul:
- Pastikan tipe file terdeteksi dengan benar
- Coba refresh dengan "📖 Read Metadata"
- Periksa format file didukung

### Validation Error:
- Periksa format nilai sesuai dengan field type
- Lihat tooltip untuk format yang benar
- Gunakan contoh format yang diberikan

### Dialog Tidak Responsif:
- Tutup dan buka ulang dialog
- Restart aplikasi jika perlu
- Periksa tidak ada dialog lain yang terbuka

## Future Enhancements

### Planned Features:
- Custom template creation
- Import/export template sets
- Field auto-completion
- Batch template application
- Template sharing between users
