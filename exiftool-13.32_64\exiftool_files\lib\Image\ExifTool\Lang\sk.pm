#------------------------------------------------------------------------------
# File:         sk.pm
#
# Description:  ExifTool Slovak language translations
#
# Notes:        This file generated automatically by Image::ExifTool::TagInfoXML
#------------------------------------------------------------------------------

package Image::ExifTool::Lang::sk;

use strict;
use vars qw($VERSION);

$VERSION = '1.01';

%Image::ExifTool::Lang::sk::Translate = (
   'A100DataOffset' => 'Korekcia údajov A100',
   'ARMIdentifier' => 'Identifikátor ARM',
   'ARMVersion' => 'Verzia ARM',
   'Acceleration' => 'Zrýchlenie',
   'ActionAdvised' => {
      Description => 'Odporúčané opatrenia',
      PrintConv => {
        'Object Append' => 'Pripojenie objektu',
        'Object Kill' => 'Zničenie objektu',
        'Object Reference' => 'Odkaz na objekt',
        'Object Replace' => 'Nahradenie objektu',
      },
    },
   'ActiveArea' => 'Aktívna oblasť',
   'AdventRevision' => 'Kontrola výskytu',
   'AdventScale' => 'Rozsah výskytu',
   'AffineTransformMat' => 'Afinná transformačná matica',
   'AliasLayerMetadata' => 'Alias ​​vrstvy metadát',
   'AlphaByteCount' => 'Veľkosť kanála alfa (v bajtoch)',
   'AlphaDataDiscard' => {
      Description => 'Vyradené údaje kanálu alfa',
      PrintConv => {
        'Flexbits Discarded' => 'Vyradené flexibilné bity',
        'Full Resolution' => 'Úplné rozlíšenie',
        'HighPass Frequency Data Discarded' => 'Vyradené údaje s vysokou frekvenciou',
        'Highpass and LowPass Frequency Data Discarded' => 'Vyradené údaje o vysokej a nízkej frekvencii',
      },
    },
   'AlphaOffset' => 'Odsadenie alfa kanála',
   'AmbientTemperature' => 'Teplota okolia',
   'AnalogBalance' => 'Analógové vyváženie',
   'Annotations' => 'Anotácie',
   'AntiAliasStrength' => 'Relatívna sila antialiasingu',
   'ApertureValue' => 'Hodnota clony',
   'ApplicationNotes' => 'Poznámky aplikácie',
   'ApplicationRecordVersion' => 'Verzia záznamu aplikácie',
   'Artist' => 'Realizátor',
   'AsShotICCProfile' => 'Profil ICC v čase snímania',
   'AsShotNeutral' => 'Neutrálna farba v čase snímania',
   'AsShotPreProfileMatrix' => 'Profil matrice v čase snímania',
   'AsShotProfileName' => 'Názov profilu v čase snímania',
   'AsShotWhiteXY' => 'Biele XY v čase snímania',
   'AudioDuration' => 'Dĺžka trvania zvuku',
   'AudioOutcue' => 'Koncový zvukový signál',
   'AudioSamplingRate' => 'Vzorkovacia frekvencia zvuku',
   'AudioSamplingResolution' => 'Dátový tok zvuku',
   'AudioType' => {
      Description => 'Typ zvuku',
      PrintConv => {
        'Mono Actuality' => 'Monofónna realita',
        'Mono Music' => 'Monofónna hudba',
        'Mono Question and Answer Session' => 'Monofónne zasadnutie otázok a odpovedí',
        'Mono Raw Sound' => 'Monofonický surový zvuk',
        'Mono Response to a Question' => 'Monofónna odpoveď na otázku',
        'Mono Scener' => 'Monofónny snímač',
        'Mono Voicer' => 'Monofónny hlasový záznamník',
        'Mono Wrap' => 'Mono zábal',
        'Stereo Actuality' => 'Stereofónna realita',
        'Stereo Music' => 'Stereofónna hudba',
        'Stereo Question and Answer Session' => 'Stereofónne zasadnutie otázok a odpovedí',
        'Stereo Raw Sound' => 'Stereofonický surový zvuk',
        'Stereo Response to a Question' => 'Stereofónna odpoveď na otázku',
        'Stereo Scener' => 'Stereofónny snímač',
        'Stereo Voicer' => 'Stereofónny hlasový záznamník',
        'Stereo Wrap' => 'Stereofónny zábal',
        'Text Only' => 'Len text',
      },
    },
   'BackgroundColorIndicator' => {
      Description => 'Indikátor farby pozadia',
      PrintConv => {
        'Specified Background Color' => 'Špecifikovaná farba pozadia',
        'Unspecified Background Color' => 'Nešpecifikovaná farba pozadia',
      },
    },
   'BackgroundColorValue' => 'Hodnota farby pozadia',
   'BadFaxLines' => 'Zlé faxové linky',
   'BaselineExposure' => 'Základná expozícia',
   'BaselineExposureOffset' => 'Korekcia základnej expozície',
   'BaselineNoise' => 'Základný šum',
   'BaselineSharpness' => 'Základná ostrosť',
   'BatteryLevel' => 'Úroveň nabitia batérie',
   'BayerGreenSplit' => 'Oddelenie zelených kanálov v Bayerovej matici',
   'BestQualityScale' => 'Optimálna stupnica',
   'BitsPerComponent' => 'Počet bitov na súčasť',
   'BitsPerExtendedRunLength' => 'Počet bitov na rozšírenú dĺžku radu',
   'BitsPerRunLength' => 'Počet bitov na dĺžku radu',
   'BitsPerSample' => 'Počet bitov na vzorku',
   'BlackLevel' => 'Úroveň čiernej',
   'BlackLevelBlue' => 'Čierna úroveň modrej',
   'BlackLevelDeltaH' => 'Delta úroveň čiernej H',
   'BlackLevelDeltaV' => 'Delta úroveň čiernej V',
   'BlackLevelGreen' => 'Čierna úroveň zelenej',
   'BlackLevelRed' => 'Čierna úroveň červenej',
   'BlackLevelRepeatDim' => 'Úroveň stlmenia čiernej farby',
   'BlueBalance' => 'Rovnováha modrej farby',
   'Brightness' => 'Jas',
   'BrightnessValue' => 'Hodnota jasu',
   'By-line' => 'Meno autora',
   'By-lineTitle' => 'Autorský názov',
   'CFALayout' => {
      Description => 'Rozloženie matice farebných filtrov',
      PrintConv => {
        'Even columns offset down 1/2 row' => 'Párne stĺpce posunuté o 1/2 riadku nadol',
        'Even columns offset up 1/2 row' => 'Párne stĺpce posunuté o 1/2 riadku nahor',
        'Even rows offset down by 1/2 row, even columns offset left by 1/2 column' => 'Párne riadky posunuté nadol o 1/2 riadka, párne stĺpce posunuté zľava o 1/2 stĺpca',
        'Even rows offset down by 1/2 row, even columns offset right by 1/2 column' => 'Párne riadky posunuté nadol o 1/2 riadku, párne stĺpce posunuté vpravo o 1/2 stĺpca',
        'Even rows offset left 1/2 column' => 'Párne riadky posunuté vľavo o 1/2 stĺpca',
        'Even rows offset right 1/2 column' => 'Párne riadky posunuté vpravo o 1/2 stĺpca',
        'Even rows offset up by 1/2 row, even columns offset left by 1/2 column' => 'Párne riadky posunuté nahor o 1/2 riadku, párne stĺpce posunuté doľava o 1/2 stĺpca',
        'Even rows offset up by 1/2 row, even columns offset right by 1/2 column' => 'Párne riadky posunuté nahor o 1/2 riadku, párne stĺpce posunuté doprava o 1/2 stĺpca',
        'Rectangular' => 'Obdĺžnikové',
      },
    },
   'CFAPattern' => {
      Description => 'Matica farebných filtrov',
      PrintConv => {
        '[Blue,Green][Green,Red]' => '[Modrá,Zelená][Zelená,Červená]',
        '[Green,Blue][Red,Green]' => '[Zelená,Modrá][Červená,Zelená]',
        '[Green,Red][Blue,Green]' => '[Zelená,Červená][Modrá,Zelená]',
        '[Red,Green][Green,Blue]' => '[Červená,Zelená][Zelená,Modrá]',
      },
    },
   'CFAPattern2' => 'Matica farebných filtrov 2',
   'CFAPlaneColor' => 'Farba roviny CFA',
   'CFARepeatPatternDim' => 'Vzor opakovania CFA Dim',
   'CIP3DataFile' => 'CIP3 - Dátový súbor',
   'CIP3Sheet' => 'CIP3 - List',
   'CIP3Side' => 'CIP3 - Strana',
   'CMYKEquivalent' => 'Ekvivalent CMYK',
   'CR2CFAPattern' => {
      Description => 'Vzorka CR2 CFA',
      PrintConv => {
        '[Blue,Green][Green,Red]' => '[Modrá,Zelená][Zelená,Červená]',
        '[Green,Blue][Red,Green]' => '[Zelená,Modrá][Červená,Zelená]',
        '[Green,Red][Blue,Green]' => '[Zelená,Červená][Modrá,Zelená]',
        '[Red,Green][Green,Blue]' => '[Červená,Zelená][Zelená,Modrá]',
      },
    },
   'CacheVersion' => 'Verzia vyrovnávacej pamäte',
   'CalibrationIlluminant1' => {
      Description => 'Kalibračné osvetlenie 1',
      PrintConv => {
        'Cloudy' => 'Zamračené',
        'Cool White Fluorescent' => 'Studená biela žiarivka',
        'Day White Fluorescent' => 'Denná biela žiarivka',
        'Daylight' => 'Denné svetlo',
        'Daylight Fluorescent' => 'Denné svetlo žiarivky',
        'Fine Weather' => 'Pekné počasie',
        'Flash' => 'Blesk',
        'Fluorescent' => 'Žiarivka',
        'ISO Studio Tungsten' => 'Štúdio ISO Tungsten',
        'Other' => 'Iné',
        'Shade' => 'Tieň',
        'Standard Light A' => 'Štandardné svetlo A',
        'Standard Light B' => 'Štandardné svetlo B',
        'Standard Light C' => 'Štandardné svetlo C',
        'Tungsten (Incandescent)' => 'Volfrámová (žiarovka)',
        'Unknown' => 'Neznáme',
        'Warm White Fluorescent' => 'Teplá biela žiarivka',
        'White Fluorescent' => 'Biela žiarivka',
      },
    },
   'CalibrationIlluminant2' => {
      Description => 'Kalibračné osvetlenie 2',
      PrintConv => {
        'Cloudy' => 'Zamračené',
        'Cool White Fluorescent' => 'Studená biela žiarivka',
        'Day White Fluorescent' => 'Denná biela žiarivka',
        'Daylight' => 'Denné svetlo',
        'Daylight Fluorescent' => 'Denné svetlo žiarivky',
        'Fine Weather' => 'Pekné počasie',
        'Flash' => 'Blesk',
        'Fluorescent' => 'Žiarivka',
        'ISO Studio Tungsten' => 'Štúdio ISO Tungsten',
        'Other' => 'Iné',
        'Shade' => 'Tieň',
        'Standard Light A' => 'Štandardné svetlo A',
        'Standard Light B' => 'Štandardné svetlo B',
        'Standard Light C' => 'Štandardné svetlo C',
        'Tungsten (Incandescent)' => 'Volfrámová (žiarovka)',
        'Unknown' => 'Neznáme',
        'Warm White Fluorescent' => 'Teplá biela žiarivka',
        'White Fluorescent' => 'Biela žiarivka',
      },
    },
   'CalibrationIlluminant3' => {
      Description => 'Kalibračné osvetlenie 3',
      PrintConv => {
        'Cloudy' => 'Zamračené',
        'Cool White Fluorescent' => 'Studená biela žiarivka',
        'Day White Fluorescent' => 'Denná biela žiarivka',
        'Daylight' => 'Denné svetlo',
        'Daylight Fluorescent' => 'Denné svetlo žiarivky',
        'Fine Weather' => 'Pekné počasie',
        'Flash' => 'Blesk',
        'Fluorescent' => 'Žiarivka',
        'ISO Studio Tungsten' => 'Štúdio ISO Tungsten',
        'Other' => 'Iné',
        'Shade' => 'Tieň',
        'Standard Light A' => 'Štandardné svetlo A',
        'Standard Light B' => 'Štandardné svetlo B',
        'Standard Light C' => 'Štandardné svetlo C',
        'Tungsten (Incandescent)' => 'Volfrámová (žiarovka)',
        'Warm White Fluorescent' => 'Teplá biela žiarivka',
        'White Fluorescent' => 'Biela žiarivka',
      },
    },
   'CameraCalibration1' => 'Kalibračný snímač kamery 1',
   'CameraCalibration2' => 'Kalibračný snímač kamery 2',
   'CameraCalibration3' => 'Kalibračný snímač kamery 3',
   'CameraCalibrationSig' => 'Kalibračný signál fotoaparátu',
   'CameraElevationAngle' => 'Uhol pohľadu fotoaparátu',
   'CameraLabel' => 'Označenie fotoaparátu',
   'CameraSerialNumber' => 'Sériové číslo fotoaparátu',
   'Caption-Abstract' => 'Podrobný popis',
   'CatalogSets' => 'Katalógové sady',
   'Category' => 'Kategória',
   'CellLength' => 'Dĺžka bunky',
   'CellWidth' => 'Šírka bunky',
   'ChromaBlurRadius' => 'Polomer vyhladzovania farieb',
   'ChromaticAberrationCorrParams' => 'Parametre korekcie chromatickej aberácie',
   'ChromaticAberrationCorrection' => {
      Description => 'Korekcia chromatickej aberácie',
      PrintConv => {
        'Auto' => 'Automatická',
        'No correction params available' => 'Nie sú dostupné žiadne parametre korekcie',
        'Off' => 'Vypnutá',
      },
    },
   'City' => 'Mesto',
   'ClassifyState' => 'Klasifikácia štruktúry',
   'CleanFaxData' => {
      Description => 'Prítomnosť prerušovaných čiar',
      PrintConv => {
        'Clean' => 'Čisté',
        'Regenerated' => 'Regenerované',
        'Unclean' => 'Nečisté',
      },
    },
   'ClipPath' => 'Obrys farbenia',
   'CodedCharacterSet' => 'Sada kódovaných znakov',
   'CodingMethods' => {
      Description => 'Metódy kódovania',
      PrintConv => {
        'Baseline JPEG' => 'Základný formát JPEG',
        'JBIG color' => 'Farebnosť JBIG',
        'Modified Huffman' => 'Upravený Huffman',
        'Modified MR' => 'Upravená MR',
        'Modified Read' => 'Upravené čítanie',
        'Unspecified compression' => 'Nešpecifikovaná kompresia',
      },
    },
   'ColorCalibrationMatrix' => 'Farebná kalibračná matrica',
   'ColorCharacterization' => 'Farebná charakteristika',
   'ColorMap' => 'Farebná mapa',
   'ColorMatrix1' => 'Farebná matrica 1',
   'ColorMatrix2' => 'Farebná matrica 2',
   'ColorMatrix3' => 'Farebná matrica 3',
   'ColorPalette' => 'Paleta farieb',
   'ColorRepresentation' => {
      Description => 'Zastúpenie farieb',
      PrintConv => {
        '3 Components, Frame Sequential in Multiple Objects' => '3 komponenty, sekvenčné snímky vo viacerých objektoch',
        '3 Components, Frame Sequential in One Object' => '3 komponenty, sekvenčné snímky v jednom objekte',
        '3 Components, Line Sequential' => '3 komponenty, sekvenčná línia',
        '3 Components, Pixel Sequential' => '3 komponenty, sekvenčný pixel',
        '3 Components, Single Frame' => '3 komponenty, jeden snímok',
        '3 Components, Special Interleaving' => '3 komponenty, špeciálne prelínanie',
        '4 Components, Frame Sequential in Multiple Objects' => '4 komponenty, sekvenčné snímky vo viacerých objektoch',
        '4 Components, Frame Sequential in One Object' => '4 komponenty, sekvenčné snímky v jednom objekte',
        '4 Components, Line Sequential' => '4 komponenty, sekvenčná línia',
        '4 Components, Pixel Sequential' => '4 komponenty, sekvenčný pixel',
        '4 Components, Single Frame' => '4 komponenty, jeden snímok',
        '4 Components, Special Interleaving' => '4 komponenty, špeciálne prelínanie',
        'Monochrome, Single Frame' => 'Monochromatický, jeden snímok',
        'No Image, Single Frame' => 'Žiadny obrázok, jeden snímok',
      },
    },
   'ColorResponseUnit' => 'Jednotky farebnej citlivosti',
   'ColorSequence' => 'Farebná postupnosť',
   'ColorSpace' => {
      Description => 'Farebný priestor',
      PrintConv => {
        'ICC Profile' => 'Profil ICC',
        'Uncalibrated' => 'Nekalibrovaný',
        'Wide Gamut RGB' => 'Široký rozsah RGB',
      },
    },
   'ColorTable' => 'Tabuľka farieb',
   'ColorimetricReference' => 'Kolorimetrická referencia',
   'ComponentsConfiguration' => 'Konfigurácia komponentov',
   'CompositeImage' => {
      Description => 'Zložený obrázok',
      PrintConv => {
        'Composite Image Captured While Shooting' => 'Zložený obrázok zachytený počas snímania',
        'General Composite Image' => 'Všeobecný zložený obrázok',
        'Not a Composite Image' => 'Nie je to zložený obrázok',
        'Unknown' => 'Neznáme',
      },
    },
   'CompositeImageCount' => 'Počet zložených obrázkov',
   'CompositeImageExposureTimes' => 'Celková expozícia všetkých zložených obrázkov',
   'CompressedBitsPerPixel' => 'Kompresia bitov na pixel',
   'Compression' => {
      Description => 'Kompresia',
      PrintConv => {
        'JBIG B&W' => 'JBIG čiernobiely',
        'JBIG Color' => 'JBIG Farebný',
        'JPEG (old-style)' => 'JPEG (zastaralý)',
        'Kodak DCR Compressed' => 'Komprimovaný Kodak DCR',
        'Kodak KDC Compressed' => 'Komprimovaný Kodak KDC',
        'Lossy JPEG' => 'Stratový JPEG',
        'Microsoft Document Imaging (MDI) Binary Level Codec' => 'Binárny kodek Microsoft Document Imaging (MDI)',
        'Microsoft Document Imaging (MDI) Progressive Transform Codec' => 'Kodek progresívnej transformácie Microsoft Document Imaging (MDI)',
        'Microsoft Document Imaging (MDI) Vector' => 'Microsoft Document Imaging (MDI) Vektor',
        'Next' => 'Kódovanie NeXT',
        'Nikon NEF Compressed' => 'Komprimovaný Nikon NEF',
        'Packed RAW' => 'Balený RAW',
        'Pentax PEF Compressed' => 'Komprimovaný Pentax PEF',
        'Samsung SRW Compressed' => 'Komprimovaný Samsung SRW',
        'Samsung SRW Compressed 2' => 'Komprimovaný 2 Samsung SRW',
        'Sony ARW Compressed' => 'Komprimovaný Sony ARW',
        'Uncompressed' => 'Bez kompresie',
      },
    },
   'ConfirmedObjectSize' => 'Potvrdená veľkosť predmetu',
   'ConsecutiveBadFaxLines' => 'Počet po sebe idúcich zalomených riadkov',
   'Contact' => 'Kontakt',
   'ContentLocationCode' => 'Kód umiestnenia obsahu',
   'ContentLocationName' => 'Názov umiestnenia obsahu',
   'Contrast' => {
      Description => 'Kontrast',
      PrintConv => {
        'High' => 'Vysoký',
        'Low' => 'Nízky',
        'Normal' => 'Normálny',
      },
    },
   'Converter' => 'Konvertor',
   'Copyright' => 'Autorské právo',
   'CopyrightNotice' => 'Oznámenie o autorských právach',
   'Country-PrimaryLocationCode' => 'Kód lokality krajiny',
   'Country-PrimaryLocationName' => 'Názov lokality krajiny',
   'CreateDate' => 'Dátum digitalizácie',
   'Credit' => 'Kredit',
   'CropBottom' => 'Orezávanie zospodu',
   'CropLeft' => 'Orezávanie vľavo',
   'CropRight' => 'Orezávanie vpravo',
   'CropTop' => 'Orezávanie zhora',
   'CurrentICCProfile' => 'Aktuálny profil ICC',
   'CurrentPreProfileMatrix' => 'Aktuálna predprofilová matrica',
   'CustomRendered' => {
      Description => 'Vlastné vykresľovanie',
      PrintConv => {
        'Custom' => 'Vlastné',
        'HDR (no original saved)' => 'HDR (nie je uložený žiadny originál)',
        'HDR (original saved)' => 'HDR (originál uložený)',
        'Normal' => 'Normálne',
        'Original (for HDR)' => 'Originál (pre HDR)',
        'Panorama' => 'Panoráma',
        'Portrait' => 'Portrét',
        'Portrait HDR' => 'Portrét HDR',
      },
    },
   'DNGAdobeData' => 'DNG - Údaje Adobe',
   'DNGBackwardVersion' => 'Spätná verzia DNG',
   'DNGLensInfo' => 'Informácie o objektíve DNG',
   'DNGPrivateData' => 'DNG - Súkromné ​​údaje',
   'DNGVersion' => 'Verzia DNG',
   'DataCompressionMethod' => 'Spôsob kompresie údajov',
   'DataType' => 'Typ údajov',
   'DateCreated' => 'Dátum vytvorenia',
   'DateSent' => 'Dátum odoslania',
   'DateTimeOriginal' => 'Dátum/Čas zhotovenia',
   'Decode' => 'Dekódovanie',
   'DefaultBlackRender' => {
      Description => 'Predvolené vykresľovanie čiernej',
      PrintConv => {
        'Auto' => 'Automatické',
        'None' => 'Žiadne',
      },
    },
   'DefaultCropOrigin' => 'Predvolená veľkosť orezaného originálu',
   'DefaultCropSize' => 'Predvolená veľkosť orezaného obrázku',
   'DefaultImageColor' => 'Predvolená farba obrázku',
   'DefaultScale' => 'Predvolená stupnica',
   'DefaultUserCrop' => 'Predvolená oblasť orezania',
   'DepthFar' => 'Maximálna hĺbka vody od fotoaparátu',
   'DepthFormat' => {
      Description => 'Formát hĺbky',
      PrintConv => {
        'Inverse' => 'Inverzný',
        'Linear' => 'Lineárny',
        'Unknown' => 'Neznámy',
      },
    },
   'DepthMeasureType' => {
      Description => 'Typ merania hĺbky',
      PrintConv => {
        'Optical Axis' => 'Optická os',
        'Optical Ray' => 'Optický lúč',
        'Unknown' => 'Neznáme',
      },
    },
   'DepthNear' => 'Hĺbka vody od fotoaparátu',
   'DepthUnits' => {
      Description => 'Jednotky hĺbky',
      PrintConv => {
        'Meters' => 'Metre',
        'Unknown' => 'Neznáme',
      },
    },
   'Destination' => 'Destinácia',
   'DeviceSettingDescription' => 'Popis predvolieb fotoaparátu',
   'DigitalCreationDate' => 'Dátum digitalizácie súboru',
   'DigitalCreationTime' => 'Čas digitalizácie súboru',
   'DigitalZoomRatio' => 'Pomer digitálneho zoomu',
   'DistortionCorrParams' => 'Parametre korekcie skreslenia',
   'DistortionCorrection' => {
      Description => 'Korekcia skreslenia',
      PrintConv => {
        'Auto' => 'Automatická',
        'Auto fixed by lens' => 'Automaticky fixovaná objektívom',
        'No correction params available' => 'Nie sú dostupné žiadne parametre korekcie',
        'Off' => 'Vypnutá',
      },
    },
   'DocumentHistory' => 'História dokumentu',
   'DocumentName' => 'Názov dokumentu',
   'DocumentNotes' => 'Pripomienky k dokumentu',
   'DotRange' => 'Rozsah bodov',
   'EditStatus' => 'Stav úprav',
   'EditorialUpdate' => {
      Description => 'Druh aktualizácie',
      PrintConv => {
        'Additional language' => 'Doplnkový jazyk',
      },
    },
   'EndPoints' => 'Koncové body',
   'EnhanceParams' => 'Vylepšené paramentre',
   'EnvelopeNumber' => 'Číslo obálky',
   'EnvelopePriority' => {
      Description => 'Priorita obálky',
      PrintConv => {
        '0 (reserved)' => '0 (rezervované)',
        '1 (most urgent)' => '1 (najnaliehavejšie)',
        '5 (normal urgency)' => '5 (bežná naliehavosť)',
        '8 (least urgent)' => '8 (najmenej naliehavé)',
        '9 (user-defined priority)' => '9 (priorita definovaná užívateľom)',
      },
    },
   'EnvelopeRecordVersion' => 'Verzia záznamu obálky',
   'ExcursionTolerance' => {
      Description => 'Tolerancia exkurzie',
      PrintConv => {
        'Allowed' => 'Povolené',
        'Not Allowed' => 'Nie je povolené',
      },
    },
   'ExifCameraInfo' => 'Informácie Exif o fotoaparáte',
   'ExifImageHeight' => 'Exif - Výška obrázku',
   'ExifImageWidth' => 'Exif - Šírka obrázku',
   'ExifVersion' => 'Verzia Exif',
   'ExpandFilm' => 'Rozšírenie filmu',
   'ExpandFilterLens' => 'Rozšírenie filtra objektívu',
   'ExpandFlashLamp' => 'Rozšírenie bleskovej lampy',
   'ExpandLens' => 'Rozšírenie objektívu',
   'ExpandScanner' => 'Rozšírenie skenera',
   'ExpandSoftware' => 'Softvérové rozšírenie',
   'ExpirationDate' => 'Dátum ukončenia platnosti',
   'ExpirationTime' => 'Čas skončenia platnosti',
   'Exposure' => 'Expozícia',
   'ExposureCompensation' => 'Kompenzácia expozície',
   'ExposureIndex' => 'Index expozície',
   'ExposureMode' => {
      Description => 'Expozičný režim',
      PrintConv => {
        'Auto' => 'Automatický',
        'Auto bracket' => 'Automatické stupňovanie',
        'Manual' => 'Manuálny',
      },
    },
   'ExposureProgram' => {
      Description => 'Expozičný program',
      PrintConv => {
        'Action (High speed)' => 'Akčný (vysoká rýchlosť) ',
        'Aperture-priority AE' => 'AE s prioritou clony',
        'Bulb' => 'Žiarovka',
        'Creative (Slow speed)' => 'Kreatívny (pomalá rýchlosť)',
        'Landscape' => 'Krajina ',
        'Manual' => 'Manuálny',
        'Not Defined' => 'Nedefinovaný',
        'Portrait' => 'Portrét ',
        'Shutter speed priority AE' => 'Priorita rýchlosti uzávierky AE',
      },
    },
   'ExposureTime' => 'Čas expozície',
   'ExtraSamples' => {
      Description => 'Popis voliteľných súčastí',
      PrintConv => {
        'Associated Alpha' => 'Pridružené alfa',
        'Unassociated Alpha' => 'Nepripojený alfa',
        'Unspecified' => 'Nešpecifikované',
      },
    },
   'FNumber' => 'Clonové číslo',
   'FaxProfile' => {
      Description => 'Faxový profil',
      PrintConv => {
        'Extended B&W lossless, F' => 'Rozšírené čiernobiele bezstratové, F',
        'Lossless JBIG B&W, J' => 'Bezstratový JBIG čiernobiely, J',
        'Lossless color and grayscale, L' => 'Bezstratové farby a odtiene sivej, L',
        'Lossy color and grayscale, C' => 'Stratové farby a odtiene šedej, C',
        'Minimal B&W lossless, S' => 'Minimálne čiernobiele bezstratové, S',
        'Mixed raster content, M' => 'Zmiešaný obsah rastra, M',
        'Multi Profiles' => 'Viacnásobné profily',
        'Profile T' => 'Profil T',
        'Unknown' => 'Neznámy',
      },
    },
   'FaxRecvParams' => 'Parametre prijímania faxov',
   'FaxRecvTime' => 'Čas potrebný na prijatie faxu',
   'FaxSubAddress' => 'Podadresa faxu',
   'FileFormat' => {
      Description => 'Formát súboru',
      PrintConv => {
        'Compressed Binary File [.ZIP] (PKWare Inc)' => 'Komprimovaný binárny súbor [.ZIP] (PKWare Inc)',
        'IPTC Unstructured Character Oriented File Format (UCOFF)' => 'Formát neštruktúrovaných znakovo orientovaných súborov IPTC (UCOFF)',
        'IPTC-NAA Digital Newsphoto Parameter Record' => 'Záznam parametrov digitálnych novín IPTC-NAA',
        'IPTC7901 Recommended Message Format' => 'Odporúčaný formát správy IPTC7901',
        'News Industry Text Format (NITF)' => 'Textový formát pre spravodajstvo (NITF)',
        'No ObjectData' => 'Žiadne údaje o objekte',
        'Ritzaus Bureau NITF version (RBNITF DTD)' => 'Verzia Ritzaus Bureau NITF (RBNITF DTD)',
        'Tagged Image File Format (Adobe/Aldus Image data)' => 'Označený formát obrazového súboru (obrazové údaje Adobe/Aldus)',
      },
    },
   'FileSource' => {
      Description => 'Zdroj súboru',
      PrintConv => {
        'Digital Camera' => 'Digitálny fotoaparát',
        'Film Scanner' => 'Filmový skener',
        'Reflection Print Scanner' => 'Odrazový skener',
        'Sigma Digital Camera' => 'Digitálny fotoaparát Sigma',
      },
    },
   'FileVersion' => 'Verzia súboru',
   'FillOrder' => {
      Description => 'Poradie výplne',
      PrintConv => {
        'Normal' => 'Normálne',
        'Reversed' => 'Obrátené',
      },
    },
   'FixtureIdentifier' => 'Identifikátor príslušenstva',
   'Flash' => {
      Description => 'Stav blesku pri snímaní',
      PrintConv => {
        'Auto, Did not fire' => 'Automaticky, nebol odpálený',
        'Auto, Did not fire, Red-eye reduction' => 'Automaticky, nebol odpálený, redukcia červených očí',
        'Auto, Fired' => 'Automaticky, odpálený',
        'Auto, Fired, Red-eye reduction' => 'Zapnutý, odpálený, redukcia červených očí',
        'Auto, Fired, Red-eye reduction, Return detected' => 'Zapnutý, odpálený, redukcia červených očí, návrat bol zistený',
        'Auto, Fired, Red-eye reduction, Return not detected' => 'Zapnutý, odpálený, redukcia červených očí, návrat nebol zistený',
        'Auto, Fired, Return detected' => 'Automaticky, odpálený, návrat bol zistený',
        'Auto, Fired, Return not detected' => 'Automaticky, odpálený, návrat nebol zistený',
        'Fired' => 'Odpálený',
        'Fired, Red-eye reduction' => 'Odpálený, redukcia červených očí',
        'Fired, Red-eye reduction, Return detected' => 'Odpálený, redukcia červených očí, návrat bol zistený',
        'Fired, Red-eye reduction, Return not detected' => 'Odpálený, redukcia červených očí, návrat nebol zistený',
        'Fired, Return detected' => 'Odpálený, návrat bol zistený',
        'Fired, Return not detected' => 'Odpálený, návrat nebol zistený',
        'No Flash' => 'Bez blesku',
        'No flash function' => 'Žiadna funkcia blesku',
        'Off, Did not fire' => 'Vypnutý, nebol odpálený',
        'Off, Did not fire, Return not detected' => 'Vypnutý, nebol odpálený, návrat nebol zistený',
        'Off, No flash function' => 'Výpnutý, žiadna funkcia blesku',
        'Off, Red-eye reduction' => 'Vypnutý, redukcia červených očí',
        'On, Did not fire' => 'Zapnutý, nebol odpálený',
        'On, Fired' => 'Zapnutý, odpálený',
        'On, Red-eye reduction' => 'Zapnutý, redukcia červených očí',
        'On, Red-eye reduction, Return detected' => 'Zapnutý, redukcia červených očí, návrat bol zistený',
        'On, Red-eye reduction, Return not detected' => 'Zapnutý, redukcia červených očí, návrat nebol zistený',
        'On, Return detected' => 'Zapnutý, návrat bol zistený ',
        'On, Return not detected' => 'Zapnutý, návrat nebol zistený ',
      },
    },
   'FlashEnergy' => 'Energia blesku',
   'FlashpixVersion' => 'Verzia Flashpix',
   'FocalLength' => 'Ohnisková vzdialenosť',
   'FocalLengthIn35mmFormat' => 'Ohnisková vzdialenosť vo formáte 35 mm',
   'FocalPlaneResolutionUnit' => {
      Description => 'Jednotka rozlíšenia ohniskovej roviny',
      PrintConv => {
        'None' => 'žiadne',
        'inches' => 'palce',
      },
    },
   'FocalPlaneXResolution' => 'Horizontálne rozlíšenie ohniskovej roviny',
   'FocalPlaneYResolution' => 'Vertikálne rozlíšenie ohniskovej roviny',
   'ForwardMatrix1' => 'Priama matrica 1',
   'ForwardMatrix2' => 'Priama matrica 2',
   'ForwardMatrix3' => 'Priama matrica 3',
   'FovCot' => 'Zobrazovací uhol',
   'FrameRate' => 'Počet snímok za sekundu',
   'FreeByteCounts' => 'Počet voľných bajtov',
   'FreeOffsets' => 'Voľné korekcie',
   'GDALMetadata' => 'GDAL - Metadáta',
   'GDALNoData' => 'GDAL - Priehľadnosť',
   'GPSAltitude' => 'Nadmorská výška',
   'GPSAltitudeRef' => {
      Description => 'Ref. nadmorskej výšky',
      PrintConv => {
        'Above Sea Level' => 'Nad hladinou mora',
        'Below Sea Level' => 'Pod hladinou mora',
      },
    },
   'GPSAreaInformation' => 'Informácie o oblasti',
   'GPSDOP' => 'Stupeň presnosti merania GPS',
   'GPSDateStamp' => 'Dátum a čas GPS',
   'GPSDestBearing' => 'Azimut k cieľovému bodu',
   'GPSDestBearingRef' => {
      Description => 'Ref. azimut k cieľovému bodu',
      PrintConv => {
        'Magnetic North' => 'Magnetický sever',
        'True North' => 'Skutočný sever',
      },
    },
   'GPSDestDistance' => 'Vzdialenosť k cieľu',
   'GPSDestDistanceRef' => {
      Description => 'Ref. vzdialenosť k cieľu',
      PrintConv => {
        'Kilometers' => 'Kilometre',
        'Miles' => 'Míle',
        'Nautical Miles' => 'Námorné míle',
      },
    },
   'GPSDestLatitude' => 'Zemepisná šírka cieľa',
   'GPSDestLatitudeRef' => {
      Description => 'Ref. pre zemepisnú šírku cieľa',
      PrintConv => {
        'North' => 'Severná šírka',
        'South' => 'Južná šírka',
      },
    },
   'GPSDestLongitude' => 'Zemepisná dĺžka cieľa',
   'GPSDestLongitudeRef' => {
      Description => 'Ref. pre zemepisnú dĺžku cieľa',
      PrintConv => {
        'East' => 'Východná dĺžka',
        'West' => 'Západná dĺžka',
      },
    },
   'GPSDifferential' => {
      Description => 'Diferenciálna korekcia',
      PrintConv => {
        'Differential Corrected' => 'Diferenciálna korekcia',
        'No Correction' => 'Žiadna korekcia',
      },
    },
   'GPSHPositioningError' => 'Chyba horizontálneho polohovania',
   'GPSImgDirection' => 'Smer GPS obrázku',
   'GPSImgDirectionRef' => {
      Description => 'Ref. smer obrázku',
      PrintConv => {
        'Magnetic North' => 'Magnetický sever',
        'True North' => 'Skutočný sever',
      },
    },
   'GPSLatitude' => 'Zemepisná šírka',
   'GPSLatitudeRef' => {
      Description => 'Ref. zemepisnej šírky',
      PrintConv => {
        'North' => 'Severná šírka',
        'South' => 'Južná šírka',
      },
    },
   'GPSLongitude' => 'Zemepisná dĺžka',
   'GPSLongitudeRef' => {
      Description => 'Ref. zemepisnej dĺžky',
      PrintConv => {
        'East' => 'Východná dĺžka',
        'West' => 'Západná dĺžka',
      },
    },
   'GPSMapDatum' => 'Údaje geodetického zamerania',
   'GPSMeasureMode' => {
      Description => 'Režim merania GPS',
      PrintConv => {
        '2-Dimensional Measurement' => '2-rozmerné meranie',
        '3-Dimensional Measurement' => '3-rozmerné meranie',
      },
    },
   'GPSProcessingMethod' => 'Metóda spracovania GPS',
   'GPSSatellites' => 'Satelity použité na meranie',
   'GPSSpeed' => 'Rýchlosť pohybu prijímača GPS',
   'GPSSpeedRef' => {
      Description => 'Jednotky na meranie rýchlosti',
      PrintConv => {
        'knots' => 'uzly',
      },
    },
   'GPSStatus' => {
      Description => 'Stav prijímača GPS',
      PrintConv => {
        'Measurement Active' => 'Meranie je aktívne',
        'Measurement Void' => 'Meranie je nesprávne',
      },
    },
   'GPSTimeStamp' => 'Čas GPS (atómové hodiny)',
   'GPSTrack' => 'Smer pohybu prijímača GPS',
   'GPSTrackRef' => {
      Description => 'Ref. na určenie smeru pohybu prijímača',
      PrintConv => {
        'Magnetic North' => 'Magnetický sever',
        'True North' => 'Skutočný sever',
      },
    },
   'GPSVersionID' => 'Identifikátor verzie GPS',
   'GainControl' => {
      Description => 'Ovládanie zosilnenia',
      PrintConv => {
        'High gain down' => 'Vysoké zosilnenie nižšie',
        'High gain up' => 'Vysoké zosilnenie',
        'Low gain down' => 'Nízke zosilnenie nižšie',
        'Low gain up' => 'Nízke zosilnenie',
        'None' => 'Žiadna',
      },
    },
   'Gamma' => 'Gama',
   'GammaCompensatedValue' => 'Hodnota kompenzácie gama',
   'GeoTiffAsciiParams' => 'Geo Tiff - Parametre Ascii',
   'GeoTiffDirectory' => 'Adresár Geo Tiff',
   'GeoTiffDoubleParams' => 'Geo Tiff - Parametre duplikovania',
   'GooglePlusUploadCode' => 'Kód na odosielanie služby Google Plus',
   'GrayResponseCurve' => 'Optická hustota v stupňoch šedej',
   'GrayResponseUnit' => 'Jednotky hustoty v stupňoch šedej',
   'HCUsage' => {
      Description => 'Využitie HCU',
      PrintConv => {
        'Line Art' => 'Umenie línie',
        'Trap' => 'Zachytenie',
      },
    },
   'HalftoneHints' => 'Poltónové rady',
   'HasselbladExif' => 'Exif Hasselblad',
   'HasselbladRawImage' => 'Obrázok Hasselblad Raw',
   'Headline' => 'Titulok',
   'HeightResolution' => 'Rozlíšenie na výšku (PPI)',
   'HighISOMultiplierBlue' => 'Vysoký multiplikátor ISO modrej farby',
   'HighISOMultiplierGreen' => 'Vysoký multiplikátor ISO zelenej farby',
   'HighISOMultiplierRed' => 'Vysoký multiplikátor ISO červenej farby',
   'HostComputer' => 'Hostiteľský počítač',
   'Humidity' => 'Vlhkosť',
   'ICC_Profile' => 'Profil ICC',
   'INGRReserved' => 'INGR Rezervované',
   'IPTCBitsPerSample' => 'IPTC - Bitov na vzorku',
   'IPTCImageHeight' => 'IPTC - Výška obrázku',
   'IPTCImageRotation' => {
      Description => 'IPTC Otočenie obrázku',
      PrintConv => {
        '0' => '0°',
        '180' => '180°',
        '270' => '270°',
        '90' => '90°',
      },
    },
   'IPTCImageWidth' => 'IPTC - Šírka obrázku',
   'IPTCPictureNumber' => 'IPTC - Číslo obrázku',
   'IPTCPixelHeight' => 'IPTC - Výška pixelov',
   'IPTCPixelWidth' => 'IPTC - Šírka pixelov',
   'ISO' => 'Citlivosť ISO',
   'ISOSpeed' => 'Rýchlosť citlivosti ISO',
   'ISOSpeedLatitudeyyy' => 'Hodnota zemepisnej šírky citlivosti ISO YYY',
   'ISOSpeedLatitudezzz' => 'Hodnota zemepisnej šírky citlivosti ISO ZZZ',
   'IT8Header' => 'Hlavička IT8',
   'IlluminantData1' => 'Údaje o osvetlení 1',
   'IlluminantData2' => 'Údaje o osvetlení 2',
   'IlluminantData3' => 'Údaje o osvetlení 3',
   'Image::ExifTool::Composite' => 'Zloženie',
   'Image::ExifTool::DNG::OriginalRaw' => 'Originálny Raw DNG',
   'Image::ExifTool::Exif::Main' => 'Exif',
   'Image::ExifTool::GPS::Main' => 'GPS',
   'Image::ExifTool::IPTC::ApplicationRecord' => 'Záznam aplikácie IPTC',
   'Image::ExifTool::IPTC::EnvelopeRecord' => 'IPTC Záznam obálky',
   'Image::ExifTool::IPTC::NewsPhoto' => 'Novinky IPTC Foto',
   'Image::ExifTool::IPTC::ObjectData' => 'IPTC - Údaje o objekte',
   'Image::ExifTool::IPTC::PostObjectData' => 'IPTC - Predbežné údaje o objekte',
   'Image::ExifTool::IPTC::PreObjectData' => 'IPTC - Údaje pred objektom',
   'ImageByteCount' => 'Veľkosť obrázku (v bajtoch)',
   'ImageColorIndicator' => {
      Description => 'Indikátor farby obrázku',
      PrintConv => {
        'Specified Image Color' => 'Špecifikovaná farba obrázku',
        'Unspecified Image Color' => 'Nešpecifikovaná farba obrázku',
      },
    },
   'ImageColorValue' => 'Hodnota farby obrázku',
   'ImageDataDiscard' => {
      Description => 'Zahodený obrazový údaj',
      PrintConv => {
        'Flexbits Discarded' => 'Vyradené flexibilné bity',
        'Full Resolution' => 'Úplné rozlíšenie',
        'HighPass Frequency Data Discarded' => 'Vyradené údaje s vysokou frekvenciou',
        'Highpass and LowPass Frequency Data Discarded' => 'Vyradené údaje o vysokej a nízkej frekvencii',
      },
    },
   'ImageDepth' => 'Hĺbka obrázku',
   'ImageDescription' => 'Popis obrázku',
   'ImageFullHeight' => 'Celá výška obrázku',
   'ImageFullWidth' => 'Celá šírka obrázku',
   'ImageHeight' => 'Výška obrázku',
   'ImageHistory' => 'História obrázku',
   'ImageID' => 'ID obrázku',
   'ImageLayer' => 'Obrazová vrstva',
   'ImageNumber' => 'Číslo obrázku',
   'ImageOffset' => 'Korekcia obrázku',
   'ImageOrientation' => {
      Description => 'Orientácia obrázku',
      PrintConv => {
        'Landscape' => 'Krajina',
        'Portrait' => 'Portrét',
        'Square' => 'Štvorec',
      },
    },
   'ImageReferencePoints' => 'Referenčné body obrázku',
   'ImageSourceData' => 'Surové obrazové údaje',
   'ImageType' => {
      Description => 'Typ obrázku',
      PrintConv => {
        'Page' => 'Stránka',
        'Preview' => 'Náhľad',
      },
    },
   'ImageUniqueID' => 'Jedinečný identifikátor obrázku',
   'ImageWidth' => 'Šírka obrázku',
   'Indexed' => {
      Description => 'Indexovanie',
      PrintConv => {
        'Indexed' => 'Indexované',
        'Not indexed' => 'Neindexované',
      },
    },
   'InkNames' => 'Názvy atramentov',
   'InkSet' => {
      Description => 'Sada atramentov',
      PrintConv => {
        'Not CMYK' => 'Nie CMYK',
      },
    },
   'InterchangeColorSpace' => 'Zmena farebného priestoru',
   'IntergraphFlagRegisters' => 'Intergraph - Registračná značka',
   'IntergraphMatrix' => 'Intergraph - Matrica',
   'IntergraphPacketData' => 'Intergraph - Údaje o balíku',
   'Interlace' => 'Prelínanie',
   'InteropIndex' => {
      Description => 'Index kompatibility súborov',
      PrintConv => {
        'R03 - DCF option file (Adobe RGB)' => 'R03 - súbor možností DCF (Adobe RGB)',
        'R98 - DCF basic file (sRGB)' => 'R98 - základný súbor DCF (sRGB)',
        'THM - DCF thumbnail file' => 'THM - súbor miniatúry DCF',
      },
    },
   'InteropVersion' => 'Verzia kompatibility súborov',
   'JBIGOptions' => 'Voľby JBIG',
   'JPEGACTables' => 'Tabuľky JPEGAC',
   'JPEGDCTables' => 'Tabuľky JPEGDC',
   'JPEGLosslessPredictors' => 'Bezstratové prediktory JPEG',
   'JPEGPointTransforms' => 'Bodové transformácie JPEG',
   'JPEGProc' => {
      Description => 'Kompresia JPEG v starom štýle',
      PrintConv => {
        'Baseline' => 'Základná línia',
        'Lossless' => 'Bezstratové',
      },
    },
   'JPEGQTables' => 'Tabuľky JPEGQ',
   'JPEGRestartInterval' => 'Interval opätovného spustenia JPEG',
   'JPEGTables' => 'Tabuľky JPEG',
   'JobID' => 'ID úlohy',
   'JpgFromRaw' => 'Súbor Jpg vložený do súboru Raw',
   'JpgFromRawLength' => 'Ťahy v súbore JPG vloženom do súboru Raw',
   'JpgFromRawStart' => 'Posun súboru JPG vloženého do súboru Raw',
   'Keywords' => 'Kľúčové slová',
   'LanguageIdentifier' => 'Identifikátor jazyka',
   'Lens' => 'Objektív',
   'LensInfo' => 'Informácie o objektíve',
   'LensMake' => 'Výrobca objektívu',
   'LensModel' => 'Model objektívu',
   'LensSerialNumber' => 'Sériové číslo objektívu',
   'LightSource' => {
      Description => 'Zdroj svetla',
      PrintConv => {
        'Cloudy' => 'Zamračené',
        'Cool White Fluorescent' => 'Studená biela žiarivka',
        'Day White Fluorescent' => 'Denná biela žiarivka',
        'Daylight' => 'Denné svetlo',
        'Daylight Fluorescent' => 'Denné svetlo žiarivky',
        'Fine Weather' => 'Slnečno',
        'Flash' => 'Blesk',
        'Fluorescent' => 'Žiarivka',
        'ISO Studio Tungsten' => 'Štúdio ISO Tungsten',
        'Other' => 'Iné osvetlenie',
        'Shade' => 'Tieň',
        'Standard Light A' => 'Štandardné svetlo A',
        'Standard Light B' => 'Štandardné svetlo B',
        'Standard Light C' => 'Štandardné svetlo C',
        'Tungsten (Incandescent)' => 'Volfrámové (žiarovky)',
        'Unknown' => 'Neznáme',
        'Warm White Fluorescent' => 'Teplá biela žiarivka',
        'White Fluorescent' => 'Biela žiarivka',
      },
    },
   'LinearResponseLimit' => 'Limit lineárnej odozvy',
   'LinearityLimitBlue' => 'Limit linearity modrej farby',
   'LinearityLimitGreen' => 'Limit linearity zelenej farby',
   'LinearityLimitRed' => 'Limit linearity červenej farby',
   'LinearizationTable' => 'Tabuľka linearizácie',
   'LocalCaption' => 'Miestny titulok',
   'LocalizedCameraModel' => 'Lokalizovaný model fotoaparátu',
   'LookupTable' => 'Tabuľka náhrad',
   'MDColorTable' => 'MD - Tabuľka farieb',
   'MDFileTag' => 'MD - Označenie súboru',
   'MDFileUnits' => 'MD - Dátové jednotky súboru',
   'MDLabName' => 'MD - Tvorca súborov',
   'MDPrepDate' => 'MD - Dátum prípravy',
   'MDPrepTime' => 'MD - Čas prípravy',
   'MDSampleInfo' => 'MD - Informácie o vzorke',
   'MDScalePixel' => 'MD - Mierka pixelov',
   'MSDocumentText' => 'MS - Textový dokument',
   'MSDocumentTextPosition' => 'MS - Pozícia textu v dokumente',
   'MSPropertySetStorage' => 'MS - Vlastnosti pamäťovej skupiny',
   'Make' => 'Výrobca',
   'MakerNoteApple' => 'Poznámka výrobcu Apple',
   'MakerNoteCanon' => 'Poznámka výrobcu Canon',
   'MakerNoteCasio' => 'Poznámka výrobcu Casio',
   'MakerNoteCasio2' => 'Poznámka výrobcu Casio 2',
   'MakerNoteDJI' => 'Poznámka výrobcu DJI',
   'MakerNoteDJIInfo' => 'Poznámka výrobcu DJI Info',
   'MakerNoteFLIR' => 'Poznámka výrobcu FLIR',
   'MakerNoteFujiFilm' => 'Poznámka výrobcu Fuji Film',
   'MakerNoteGE' => 'Poznámka výrobcu GE',
   'MakerNoteGE2' => 'Poznámka výrobcu GE2',
   'MakerNoteHP' => 'Poznámka výrobcu HP',
   'MakerNoteHP2' => 'Poznámka výrobcu HP2',
   'MakerNoteHP4' => 'Poznámka výrobcu HP4',
   'MakerNoteHP6' => 'Poznámka výrobcu HP6',
   'MakerNoteHasselblad' => 'Poznámka výrobcu Hasselblad',
   'MakerNoteISL' => 'Poznámka výrobcu ISL',
   'MakerNoteJVC' => 'Poznámka výrobcu JVC',
   'MakerNoteJVCText' => 'Poznámka výrobcu JVC - Textovo',
   'MakerNoteKodak10' => 'Poznámka výrobcu Kodak 10',
   'MakerNoteKodak11' => 'Poznámka výrobcu Kodak 11',
   'MakerNoteKodak12' => 'Poznámka výrobcu Kodak 12',
   'MakerNoteKodak1a' => 'Poznámka výrobcu Kodak 1a',
   'MakerNoteKodak1b' => 'Poznámka výrobcu Kodak 1b',
   'MakerNoteKodak2' => 'Poznámka výrobcu Kodak 2',
   'MakerNoteKodak3' => 'Poznámka výrobcu Kodak 3',
   'MakerNoteKodak4' => 'Poznámka výrobcu Kodak 4',
   'MakerNoteKodak5' => 'Poznámka výrobcu Kodak 5',
   'MakerNoteKodak6a' => 'Poznámka výrobcu Kodak 6a',
   'MakerNoteKodak6b' => 'Poznámka výrobcu Kodak 6b',
   'MakerNoteKodak7' => 'Poznámka výrobcu Kodak 7',
   'MakerNoteKodak8a' => 'Poznámka výrobcu Kodak 8a',
   'MakerNoteKodak8b' => 'Poznámka výrobcu Kodak 8b',
   'MakerNoteKodak8c' => 'Poznámka výrobcu Kodak 8c',
   'MakerNoteKodak9' => 'Poznámka výrobcu Kodak 9',
   'MakerNoteKodakUnknown' => 'Poznámka výrobcu Kodak - Neznáme',
   'MakerNoteKyocera' => 'Poznámka výrobcu Kyocera',
   'MakerNoteLeica' => 'Poznámka výrobcu Leica',
   'MakerNoteLeica10' => 'Poznámka výrobcu Leica 10',
   'MakerNoteLeica2' => 'Poznámka výrobcu Leica 2',
   'MakerNoteLeica3' => 'Poznámka výrobcu Leica 3',
   'MakerNoteLeica4' => 'Poznámka výrobcu Leica 4',
   'MakerNoteLeica5' => 'Poznámka výrobcu Leica 5',
   'MakerNoteLeica6' => 'Poznámka výrobcu Leica 6',
   'MakerNoteLeica7' => 'Poznámka výrobcu Leica 7',
   'MakerNoteLeica8' => 'Poznámka výrobcu Leica 8',
   'MakerNoteLeica9' => 'Poznámka výrobcu Leica 9',
   'MakerNoteMinolta' => 'Poznámka výrobcu Minolta',
   'MakerNoteMinolta2' => 'Poznámka výrobcu Minolta 2',
   'MakerNoteMinolta3' => 'Poznámka výrobcu Minolta 3',
   'MakerNoteMotorola' => 'Poznámka výrobcu Motorola',
   'MakerNoteNikon' => 'Poznámka výrobcu Nikon',
   'MakerNoteNikon2' => 'Poznámka výrobcu Nikon 2',
   'MakerNoteNikon3' => 'Poznámka výrobcu Nikon 3',
   'MakerNoteNintendo' => 'Poznámka výrobcu Nintendo',
   'MakerNoteOlympus' => 'Poznámka výrobcu Olympus',
   'MakerNoteOlympus2' => 'Poznámka výrobcu Olympus 2',
   'MakerNoteOlympus3' => 'Poznámka výrobcu Olympus 3',
   'MakerNotePanasonic' => 'Poznámka výrobcu Panasonic',
   'MakerNotePanasonic2' => 'Poznámka výrobcu Panasonic 2',
   'MakerNotePanasonic3' => 'Poznámka výrobcu Panasonic 3',
   'MakerNotePentax' => 'Poznámka výrobcu Pentax',
   'MakerNotePentax2' => 'Poznámka výrobcu Pentax 2',
   'MakerNotePentax3' => 'Poznámka výrobcu Pentax 3',
   'MakerNotePentax4' => 'Poznámka výrobcu Pentax 4',
   'MakerNotePentax5' => 'Poznámka výrobcu Pentax 5',
   'MakerNotePentax6' => 'Poznámka výrobcu Pentax 6',
   'MakerNotePhaseOne' => 'Poznámka výrobcu Phase One',
   'MakerNoteReconyx' => 'Poznámka výrobcu Reconyx',
   'MakerNoteReconyx2' => 'Poznámka výrobcu Reconyx 2',
   'MakerNoteReconyx3' => 'Poznámka výrobcu Reconyx 3',
   'MakerNoteRicoh' => 'Poznámka výrobcu Ricoh',
   'MakerNoteRicoh2' => 'Poznámka výrobcu Ricoh 2',
   'MakerNoteRicohPentax' => 'Poznámka výrobcu Ricoh Pentax',
   'MakerNoteRicohText' => 'Poznámka výrobcu Ricoh - Textovo',
   'MakerNoteSafety' => {
      Description => 'Bezpečnostná poznámka výrobcu',
      PrintConv => {
        'Safe' => 'Bezpečný',
        'Unsafe' => 'Nebezpečný',
      },
    },
   'MakerNoteSamsung1a' => 'Poznámka výrobcu Samsung 1a',
   'MakerNoteSamsung1b' => 'Poznámka výrobcu Samsung 1b',
   'MakerNoteSamsung2' => 'Poznámka výrobcu Samsung 2',
   'MakerNoteSanyo' => 'Poznámka výrobcu Sanyo',
   'MakerNoteSanyoC4' => 'Poznámka výrobcu Sanyo C4',
   'MakerNoteSanyoPatch' => 'Poznámka výrobcu Sanyo Sanyo Patch',
   'MakerNoteSigma' => 'Poznámka výrobcu Sigma',
   'MakerNoteSony' => 'Poznámka výrobcu Sony',
   'MakerNoteSony2' => 'Poznámka výrobcu Sony 2',
   'MakerNoteSony3' => 'Poznámka výrobcu Sony 3',
   'MakerNoteSony4' => 'Poznámka výrobcu Sony 4',
   'MakerNoteSony5' => 'Poznámka výrobcu Sony 5',
   'MakerNoteSonyEricsson' => 'Poznámka výrobcu Sony Ericsson',
   'MakerNoteSonySRF' => 'Poznámka výrobcu Sony SRF',
   'MakerNoteUnknown' => 'Poznámky neznámeho výrobcu',
   'MakerNoteUnknownBinary' => 'Poznámky neznámeho výrobcu - Binárne',
   'MakerNoteUnknownText' => 'Poznámky neznámeho výrobcu - Textovo',
   'MaskSubArea' => 'Podoblasť masky',
   'MaskedAreas' => 'Maskované oblasti',
   'MasterDocumentID' => 'Základný identifikátor dokumentu',
   'MatrixWorldToCamera' => 'Matrica zobrazenia medzi svetom a fotoaparátom',
   'MatrixWorldToScreen' => 'Matrica zobrazenia medzi svetom a obrazovkou',
   'Matteing' => 'Zrnitosť',
   'MaxApertureValue' => 'Maximálna hodnota clony',
   'MaxSampleValue' => 'Maximálna hodnota súčasti',
   'MaxSubfileSize' => 'Maximálna veľkosť podsúboru',
   'MaximumDensityRange' => 'Maximálny rozsah hustoty',
   'MaximumObjectSize' => 'Maximálna veľkosť objektu',
   'MeteringMode' => {
      Description => 'Režim merania',
      PrintConv => {
        'Average' => 'Priemerné',
        'Center-weighted average' => 'Stredovo vyvážený priemer',
        'Multi-segment' => 'Viacsegmentovo',
        'Multi-spot' => 'Viacbodovo',
        'Other' => 'Iné',
        'Partial' => 'Čiastočne',
        'Spot' => 'Bodovo',
        'Unknown' => 'Neznáme',
      },
    },
   'MinSampleValue' => 'Minimálna hodnota súčasti',
   'ModeNumber' => 'Číslo režimu',
   'Model' => 'Názov modelu fotoaparátu',
   'ModelTiePoint' => 'Modelový bod väzby',
   'ModelTransform' => 'Model transformácie',
   'ModifyDate' => 'Dátum úpravy',
   'MoireFilter' => 'Moiré filter',
   'MultiProfiles' => {
      Description => 'Viacnásobné profily',
      PrintConv => {
        'JBIG2 Profile M' => 'Profil JBIG2 M',
        'N Layer Profile M' => 'N Profil vrstvy M',
        'Profile C' => 'Profil C',
        'Profile F' => 'Profil F',
        'Profile J' => 'Profil J',
        'Profile L' => 'Profil L',
        'Profile M' => 'Profil M',
        'Profile S' => 'Profil S',
        'Profile T' => 'Profil T',
        'Resolution/Image Width' => 'Rozlíšenie/Šírka obrázku',
        'Shared Data' => 'Zdieľaný dátový',
      },
    },
   'Multishot' => {
      Description => 'Viacnásobné snímanie',
      PrintConv => {
        'Off' => 'Vypnuté',
        'Pixel Shift' => 'Posun pixelov',
      },
    },
   'NewRawImageDigest' => 'Hash obrázku RAW v novom formáte',
   'NewsPhotoVersion' => 'Verzia News Photo',
   'Noise' => 'Šum',
   'NoiseProfile' => 'Profil šumu',
   'NoiseReductionApplied' => 'Použitá redukcia šumu',
   'NoiseReductionParams' => 'Parametre redukciu šumu',
   'NumIndexEntries' => 'Počet záznamov v indexe',
   'NumberofInks' => 'Počet atramentov',
   'OPIProxy' => {
      PrintConv => {
        'Higher resolution image does not exist' => 'Obrázok s vyšším rozlíšením neexistuje',
        'Higher resolution image exists' => 'Obrázok s vyšším rozlíšením existuje',
      },
    },
   'ObjectAttributeReference' => 'Odkaz na atribút objektu',
   'ObjectCycle' => {
      Description => 'Denný cyklus',
      PrintConv => {
        'Both Morning and Evening' => 'Ráno aj večer',
        'Evening' => 'Večer',
        'Morning' => 'Ráno',
      },
    },
   'ObjectName' => 'Názov objektu',
   'ObjectPreviewData' => 'Objekt náhľadu - Údaje',
   'ObjectPreviewFileFormat' => {
      Description => 'Objekt náhľadu - Formát súboru',
      PrintConv => {
        'Compressed Binary File [.ZIP] (PKWare Inc)' => 'Komprimovaný binárny súbor [.ZIP] (PKWare Inc)',
        'IPTC Unstructured Character Oriented File Format (UCOFF)' => 'Formát neštruktúrovaných znakovo orientovaných súborov IPTC (UCOFF)',
        'IPTC-NAA Digital Newsphoto Parameter Record' => 'Záznam parametrov digitálnych novín IPTC-NAA',
        'IPTC7901 Recommended Message Format' => 'Odporúčaný formát správy IPTC7901',
        'News Industry Text Format (NITF)' => 'Textový formát pre spravodajstvo (NITF)',
        'No ObjectData' => 'Žiadne údaje o objekte',
        'Ritzaus Bureau NITF version (RBNITF DTD)' => 'Verzia Ritzaus Bureau NITF (RBNITF DTD)',
        'Tagged Image File Format (Adobe/Aldus Image data)' => 'Označený formát obrazového súboru (obrazové údaje Adobe/Aldus)',
      },
    },
   'ObjectPreviewFileVersion' => 'Objekt náhľadu - Verzia súboru',
   'ObjectSizeAnnounced' => 'Deklarovaná veľkosť objektu',
   'ObjectTypeReference' => 'Odkaz na typ objektu',
   'OceApplicationSelector' => 'Výber aplikácie Oce',
   'OceIDNumber' => 'Identifikačné číslo Oce',
   'OceImageLogic' => 'Logika obrazu Oce',
   'OceScanjobDesc' => 'Popis úlohy skenovania Oce',
   'OffsetSchema' => 'Schéma korekcie',
   'OffsetTime' => 'Korekcia času',
   'OffsetTimeDigitized' => 'Korekcia digitalizovaného času',
   'OffsetTimeOriginal' => 'Korekcia pôvodného času',
   'OldSubfileType' => {
      Description => 'Starý typ podsúboru',
      PrintConv => {
        'Full-resolution image' => 'Obrázok v plnom rozlíšení',
        'Reduced-resolution image' => 'Obrázok so zníženým rozlíšením',
        'Single page of multi-page image' => 'Jedna strana viacstranového obrázku',
      },
    },
   'OpcodeList1' => 'Zoznam kódov operácií 1',
   'OpcodeList2' => 'Zoznam kódov operácií 2',
   'OpcodeList3' => 'Zoznam kódov operácií 3',
   'Opto-ElectricConvFactor' => 'Optoelektrický konverzný faktor',
   'Orientation' => {
      Description => 'Orientácia',
      PrintConv => {
        'Horizontal (normal)' => 'Horizontálna (normálne)',
        'Mirror horizontal' => 'Zrkadlená horizontálne',
        'Mirror horizontal and rotate 270 CW' => 'Zrkadlená horizontálne a otočená o 270° v smere hodinových ručičiek',
        'Mirror horizontal and rotate 90 CW' => 'Zrkadlená horizontálne a otočená o 90° v smere hodinových ručičiek',
        'Mirror vertical' => 'Zrkadlená vertikálne',
        'Rotate 180' => 'Otočená o 180°',
        'Rotate 270 CW' => 'Otočená o 270° v smere hodinových ručičiek',
        'Rotate 90 CW' => 'Otočená o 90° v smere hodinových ručičiek',
      },
    },
   'OriginalBestQualitySize' => 'Pôvodná veľkosť najlepšej kvality',
   'OriginalDefaultCropSize' => 'Pôvodná predvolená veľkosť orezania',
   'OriginalDefaultFinalSize' => 'Plná veľkosť pôvodného obrázku',
   'OriginalFileName' => 'Názov pôvodného súboru',
   'OriginalRawCreator' => 'Autor pôvodného Raw',
   'OriginalRawFileData' => 'Pôvodné údaje súboru Raw',
   'OriginalRawFileDigest' => 'Originálny súbor Raw Digest',
   'OriginalRawFileName' => 'Pôvodný názov súboru Raw',
   'OriginalRawFileType' => 'Pôvodný typ súboru Raw',
   'OriginalRawImage' => 'Pôvodný Raw obrázok',
   'OriginalRawResource' => 'Pôvodný zdroj Raw',
   'OriginalTHMCreator' => 'Pôvodný autor THM',
   'OriginalTHMFileType' => 'Pôvodný typ súboru THM',
   'OriginalTHMImage' => 'Pôvodný obrázok THM',
   'OriginalTHMResource' => 'Pôvodný zdroj THM',
   'OriginalTransmissionReference' => 'Pôvodné prepojenie na odovzdanie',
   'OriginatingProgram' => 'Pôvodný program',
   'OtherImage' => 'Iný obrázok',
   'OtherImageLength' => 'Dĺžka iného obrázku',
   'OtherImageStart' => 'Odsadenie iného obrázku',
   'OwnerID' => 'Identifikátor vlastníka',
   'OwnerName' => 'Meno vlastníka',
   'Padding' => 'Odsadenie',
   'PageName' => 'Názov strany',
   'PageNumber' => 'Číslo strany',
   'PanasonicRawVersion' => 'Verzia Panasonic Raw',
   'PanasonicTitle' => 'Panasonic - Názov',
   'PanasonicTitle2' => 'Panasonic - Názov 2',
   'PhotometricInterpretation' => {
      Description => 'Farebný model',
      PrintConv => {
        'BlackIsZero' => 'Čierna je nula',
        'Color Filter Array' => 'Pole farebných filtrov',
        'Depth Map' => 'Hĺbková mapa',
        'Linear Raw' => 'Surový lineárny',
        'RGB Palette' => 'Paleta RGB',
        'Semantic Mask' => 'Sémantická maska',
        'Sequential Color Filter' => 'Sekvenčný farebný filter',
        'Transparency Mask' => 'Priehľadná maska',
        'WhiteIsZero' => 'Biela je nula',
      },
    },
   'PixelFormat' => {
      Description => 'Formát pixelu',
      PrintConv => {
        '112-bit 6 Channels Alpha' => '112-bitový so 6 alfa kanálmi',
        '112-bit 7 Channels' => '112-bitový so 7 kanálmi',
        '128-bit 7 Channels Alpha' => '128-bitový so 7 alfa kanálmi',
        '128-bit 8 Channels' => '128-bitový s 8 kanálmi',
        '128-bit PRGBA Float' => '128-bitové PRGBA plávajúce',
        '128-bit RGB Float' => '128-bitové RGB plávajúce',
        '128-bit RGBA Fixed Point' => '128-bitové RGBA s pevným bodom',
        '128-bit RGBA Float' => '128-bitové RGBA plávajúce',
        '144-bit 8 Channels Alpha' => '144-bitový s 8 alfa kanálmi',
        '16-bit BGR555' => '16-bitový BGR555',
        '16-bit BGR565' => '16-bitový BGR565',
        '16-bit Gray' => '16-bitový šedý',
        '16-bit Gray Half' => '16-bitový šedý polovičný',
        '24-bit 3 Channels' => '24-bitový s 3 kanálmi',
        '24-bit BGR' => '24-bitový BGR',
        '24-bit RGB' => '24-bitové RGB',
        '32-bit 3 Channels Alpha' => '32-bitový s 3 alfa kanálmi',
        '32-bit 4 Channels' => '32-bitový so 4 kanálmi',
        '32-bit BGR' => '32-bitový BGR',
        '32-bit BGR101010' => '32-bitové BGR101010',
        '32-bit BGRA' => '32-bitové BGRA',
        '32-bit CMYK' => '32-bitový CMYK',
        '32-bit Gray Fixed Point' => '32-bitový šedý s pevným bodom',
        '32-bit Gray Float' => '32-bitový šedý plávajúci',
        '32-bit PBGRA' => '32-bitové PBGRA',
        '32-bit RGBE' => '32-bitový RGBE ',
        '40-bit 4 Channels Alpha' => '40-bitový so 4 alfa kanálmi',
        '40-bit 5 Channels' => '40-bitový s 5 kanálmi',
        '40-bit CMYK Alpha' => '40-bitový alfa CMYK',
        '48-bit 3 Channels' => '48-bitový s 3 kanálmi',
        '48-bit 5 Channels Alpha' => '48-bitový s 5 alfa kanálmi',
        '48-bit 6 Channels' => '48-bitový so 6 kanálmi',
        '48-bit RGB' => '48--bitové RGB',
        '48-bit RGB Fixed Point' => '48-bitové RGB s pevným bodom',
        '48-bit RGB Half' => '48-bitové RGB polovičné',
        '56-bit 6 Channels Alpha' => '56-bitový s 6 alfa kanálmi',
        '56-bit 7 Channels' => '56-bitový so 7 kanálmi',
        '64-bit 3 Channels Alpha' => '64-bitový s 3 alfa kanálmi',
        '64-bit 4 Channels' => '64-bitový so 4 kanálmi',
        '64-bit 7 Channels Alpha' => '64-bitový so 7 alfa kanálmi',
        '64-bit 8 Channels' => '64-bitový s 8 kanálmi',
        '64-bit CMYK' => '64-bitový CMYK',
        '64-bit PRGBA' => '64--bitové PRGBA',
        '64-bit RGBA' => '64--bitové RGBA',
        '64-bit RGBA Fixed Point' => '64-bitové RGBA s pevným bodom',
        '64-bit RGBA Half' => '64-bitové RGBA polovičné',
        '72-bit 8 Channels Alpha' => '72-bitový s 8 alfa kanálmi',
        '8-bit Gray' => '8-bitový sivý',
        '80-bit 4 Channels Alpha' => '80-bitový s 4 alfa kanálmi',
        '80-bit 5 Channels' => '80-bitový s 5 kanálmi',
        '80-bit CMYK Alpha' => '80-bitový alfa CMYK',
        '96-bit 5 Channels Alpha' => '96-bitový s 5 alfa kanálmi',
        '96-bit 6 Channels' => '96-bitový so 6 kanálmi',
        '96-bit RGB Fixed Point' => '96-bitové RGB s pevným bodom',
        'Black & White' => 'Čiernobiely',
      },
    },
   'PixelIntensityRange' => 'Rozsah intenzity pixelov',
   'PixelMagicJBIGOptions' => 'Voľby PixelMagicJBIGO',
   'PixelScale' => 'Pixelová mierka',
   'PlanarConfiguration' => {
      Description => 'Princíp organizácie údajov',
      PrintConv => {
        'Chunky' => 'Hrubá',
        'Planar' => 'Plošná',
      },
    },
   'Predictor' => {
      Description => 'Predikcia',
      PrintConv => {
        'Floating point' => 'Plávajúci bod',
        'Floating point X2' => 'Plávajúci bod X2',
        'Floating point X4' => 'Plávajúci bod X4',
        'Horizontal difference X2' => 'Horizontálne rozlišovanie X2',
        'Horizontal difference X4' => 'Horizontálne rozlišovanie X4',
        'Horizontal differencing' => 'Horizontálne rozlišovanie',
        'None' => 'Žiadna',
      },
    },
   'Prefs' => 'Predvoľby',
   'Pressure' => 'Tlak',
   'PreviewApplicationName' => 'Názov náhľadu aplikácie',
   'PreviewApplicationVersion' => 'Verzia náhľadu aplikácie',
   'PreviewColorSpace' => {
      Description => 'Náhľad farebného priestoru',
      PrintConv => {
        'Gray Gamma 2.2' => 'Sivá gama 2,2',
        'Unknown' => 'Neznámy',
      },
    },
   'PreviewDateTime' => 'Dátum a čas náhľadu',
   'PreviewImage' => 'Náhľad obrázku',
   'PreviewImageLength' => 'Náhľad dĺžky obrázku',
   'PreviewImageStart' => 'Posun náhľadu obrázku',
   'PreviewSettingsDigest' => 'Prehľad nastavení Digest',
   'PreviewSettingsName' => 'Názov nastavenia náhľadu',
   'PrimaryChromaticities' => 'Farebnosť základných farieb',
   'PrintIM' => 'Zhodnosť tlačových obrázkov',
   'ProcessingSoftware' => 'Spracovateľský softvér',
   'ProductID' => 'Identifikátor produktu',
   'ProfileCalibrationSig' => 'Profil kalibračného signálu',
   'ProfileCopyright' => 'Autorské práva k profilu',
   'ProfileEmbedPolicy' => {
      Description => 'Zásady vkladania profilov',
      PrintConv => {
        'Allow Copying' => 'Povoliť kopírovanie',
        'Embed if Used' => 'Vložiť, ak sa používa',
        'Never Embed' => 'Nikdy nevkladať',
        'No Restrictions' => 'Bez obmedzení',
      },
    },
   'ProfileGainTableMap' => 'Profil mapy tabuľky zosilnenia',
   'ProfileHueSatMapData1' => 'Profil - Mapa údajov a odtieňoch a sýtosti 1',
   'ProfileHueSatMapData2' => 'Profil - Mapa údajov a odtieňoch a sýtosti 2',
   'ProfileHueSatMapData3' => 'Údaje o profile odtieňa 3',
   'ProfileHueSatMapDims' => 'Profil - Mapa odtieňov',
   'ProfileHueSatMapEncoding' => {
      Description => 'Kódovanie mapy profilov Hue Sat',
      PrintConv => {
        'Linear' => 'Lineárne',
      },
    },
   'ProfileLookTableData' => 'Profil - Údaje v tabuľke vzhľadu',
   'ProfileLookTableDims' => 'Profil - Rozdelenie odtieňov',
   'ProfileLookTableEncoding' => {
      Description => 'Kódovanie vzhľadovej tabuľky profilu',
      PrintConv => {
        'Linear' => 'Lineárne',
      },
    },
   'ProfileName' => 'Názov profilu',
   'ProfileToneCurve' => 'Profil - Tónová krivka',
   'ProfileType' => {
      Description => 'Typ profilu',
      PrintConv => {
        'Group 3 FAX' => 'Skupina 3 FAX',
        'Unspecified' => 'Nešpecifikovaný',
      },
    },
   'ProgramVersion' => 'Verzia programu',
   'Province-State' => 'Krajina - Štát',
   'QuantizationMethod' => {
      Description => 'Kvantizačná metóda',
      PrintConv => {
        'AP Domestic Analogue' => 'Domáce analógové AP',
        'Color Space Specific' => 'Špecifický farebný priestor',
        'Compression Method Specific' => 'Špecifická metóda kompresie',
        'Gamma Compensated' => 'Kompenzácia gama',
        'Linear Density' => 'Lineárna hustota',
        'Linear Dot Percent' => 'Lineárne bodové percento',
        'Linear Reflectance/Transmittance' => 'Lineárna odrazivosť/priepustnosť',
      },
    },
   'RGBTables' => 'Tabuľky RGB',
   'RasterPadding' => {
      Description => 'Výplň rastra',
      PrintConv => {
        'Byte' => 'Bajt',
        'Long Sector' => 'Dlhý sektor',
        'Long Word' => 'Dlhé slovo',
        'Sector' => 'Sektor',
        'Word' => 'Slovo',
      },
    },
   'RasterizedCaption' => 'Rastrovaný titulok',
   'Rating' => 'Hodnotenie',
   'RatingPercent' => 'Hodnotenie v percentách',
   'RawDataOffset' => 'Korekcia údajov Raw',
   'RawDataUniqueID' => 'Jedinečný identifikátor údajov Raw',
   'RawFile' => 'Súbor Raw',
   'RawFormat' => 'Formát Raw',
   'RawImageDigest' => 'Súbor Raw Digest',
   'RawImageSegmentation' => 'Segmentácia obrázku Raw',
   'RawToPreviewGain' => 'Faktor zväčšenia medzi súborom Raw a náhľadom',
   'RecommendedExposureIndex' => 'Odporúčaný index expozície',
   'RedBalance' => 'Rovnováha červenej farby',
   'ReductionMatrix1' => 'Redukčná matrica 1',
   'ReductionMatrix2' => 'Redukčná matrica 2',
   'ReductionMatrix3' => 'Redukčná matrica 3',
   'ReelName' => 'Názov nosiča',
   'ReferenceBlackWhite' => 'Počiatočné hodnoty čiernej a bielej',
   'ReferenceDate' => 'Referenčný dátum',
   'ReferenceNumber' => 'Referenčné číslo',
   'ReferenceService' => 'Referenčná služba',
   'RegionXformTackPoint' => 'Bod návratu regiónu Xform',
   'RelatedImageFileFormat' => 'Formát prepojeného obrazového súboru',
   'RelatedImageHeight' => 'Výška súvisiaceho obrázku',
   'RelatedImageWidth' => 'Šírka súvisiaceho obrázku',
   'RelatedSoundFile' => 'Súvisiaci zvukový súbor',
   'ReleaseDate' => 'Dátum vydania',
   'ReleaseTime' => 'Čas vydania',
   'ResolutionUnit' => {
      Description => 'Jednotka rozlíšenia',
      PrintConv => {
        'None' => 'žiadne',
        'cm' => 'centimetre',
        'inches' => 'palce',
      },
    },
   'RowInterleaveFactor' => 'Faktor prekladania riadkov',
   'RowsPerStrip' => 'Riadkov na pruh',
   'SEMInfo' => 'Informácie o SEM',
   'SMaxSampleValue' => 'S max. hodnota vzorky',
   'SMinSampleValue' => 'S min. hodnota vzorky',
   'SRawType' => 'Typ SRaw',
   'SampleFormat' => {
      Description => 'Formát súčasti',
      PrintConv => {
        'Complex float' => 'Komplexné číslo s pohyblivou čiarkou',
        'Complex int' => 'Komplexné celé číslo',
        'Float' => 'Pohyblivá desatinná čiarka',
        'Signed' => 'Celé číslo so znamienkom dvojkovej sústavy',
        'Undefined' => 'Nedefinovaný',
        'Unsigned' => 'Celé číslo bez znamienka',
      },
    },
   'SampleStructure' => 'Štruktúra súčastí',
   'SamplesPerPixel' => 'Počet vzoriek na pixel',
   'SamsungRawByteOrder' => 'Samsung RAW - Poradie bajtov',
   'SamsungRawPointersLength' => 'Samsung RAW - Ukazovateľ dĺžky',
   'SamsungRawPointersOffset' => 'Samsung RAW - Posun ukazovateľa',
   'SamsungRawUnknown' => 'Samsung RAW - Neznámy',
   'Saturation' => {
      Description => 'Sýtosť',
      PrintConv => {
        'High' => 'Vysoká',
        'Low' => 'Nízka',
        'Normal' => 'Normálna',
      },
    },
   'ScanningDirection' => {
      Description => 'Smer snímania',
      PrintConv => {
        'Bottom-Top, L-R' => 'Dole-hore, L-R',
        'Bottom-Top, R-L' => 'Dole-hore, R-L',
        'L-R, Bottom-Top' => 'L-R, odspodu nahor',
        'L-R, Top-Bottom' => 'L-R, zhora nadol',
        'R-L, Bottom-Top' => 'R-L, odspodu nahor',
        'R-L, Top-Bottom' => 'R-L, zhora nadol',
        'Top-Bottom, L-R' => 'Hore-dole, L-R',
        'Top-Bottom, R-L' => 'Hore-dole, R-L',
      },
    },
   'SceneCaptureType' => {
      Description => 'Typ zachytenia scény',
      PrintConv => {
        'Landscape' => 'Na šírku',
        'Night' => 'Noc',
        'Other' => 'Iný',
        'Portrait' => 'Na výšku',
        'Standard' => 'Štandardný',
      },
    },
   'SceneType' => {
      Description => 'Typ scény',
      PrintConv => {
        'Directly photographed' => 'Priamo odfotografované',
      },
    },
   'SecurityClassification' => {
      Description => 'Bezpečnostná klasifikácia',
      PrintConv => {
        'Confidential' => 'Dôverné',
        'Restricted' => 'Vyhradené',
        'Secret' => 'Tajné',
        'Top Secret' => 'Prísne tajné',
        'Unclassified' => 'Neklasifikované',
      },
    },
   'SelfTimerMode' => 'Režim samospúšte',
   'SemanticInstanceID' => 'Identifikátor sémantickej inštancie',
   'SemanticName' => 'Sémantický názov',
   'SensingMethod' => {
      Description => 'Spôsob snímania',
      PrintConv => {
        'Color sequential area' => 'Farebná sekvenčná oblasť',
        'Color sequential linear' => 'Farebný sekvenčný lineárny',
        'Monochrome area' => 'Jednofarebná oblasť',
        'Monochrome linear' => 'Monochromatické lineárne',
        'Not defined' => 'Nie je definované',
        'One-chip color area' => 'Jednočipová farebná oblasť',
        'Three-chip color area' => 'Trojčipová farebná oblasť',
        'Trilinear' => 'Trilineárna',
        'Two-chip color area' => 'Dvojčipová farebná oblasť',
      },
    },
   'SensitivityType' => {
      Description => 'Typ citlivosti',
      PrintConv => {
        'ISO Speed' => 'Rýchlosť ISO',
        'Recommended Exposure Index' => 'Odporúčaný index expozície',
        'Recommended Exposure Index and ISO Speed' => 'Odporúčaný index expozície a rýchlosť ISO',
        'Standard Output Sensitivity' => 'Štandardná výstupná citlivosť',
        'Standard Output Sensitivity and ISO Speed' => 'Štandardná výstupná citlivosť a citlivosť ISO ',
        'Standard Output Sensitivity and Recommended Exposure Index' => 'Štandardná výstupná citlivosť a odporúčaný index expozície',
        'Standard Output Sensitivity, Recommended Exposure Index and ISO Speed' => 'Štandardná výstupná citlivosť, odporúčaný expozičný index a citlivosť ISO',
        'Unknown' => 'Neznámy',
      },
    },
   'SensorBottomBorder' => 'Spodný okraj snímača',
   'SensorHeight' => 'Výška snímača',
   'SensorLeftBorder' => 'Ľavý okraj snímača',
   'SensorRightBorder' => 'Pravý okraj snímača',
   'SensorTopBorder' => 'Horný okraj snímača',
   'SensorWidth' => 'Šírka snímača',
   'SerialNumber' => 'Sériové číslo',
   'ServiceIdentifier' => 'Identifikátor služby',
   'ShadowScale' => 'Tieňová stupnica',
   'Shadows' => 'Tiene',
   'SharedData' => 'Zdieľané údaje',
   'Sharpness' => {
      Description => 'Ostrosť',
      PrintConv => {
        'Hard' => 'Tvrdá',
        'Normal' => 'Normálna',
        'Soft' => 'Mäkká',
      },
    },
   'ShortDocumentID' => 'Stručný identifikátor dokumentu',
   'ShutterSpeedValue' => 'Hodnota rýchlosti uzávierky',
   'SimilarityIndex' => 'Index podobnosti',
   'Site' => 'Miesto',
   'SizeMode' => {
      Description => 'Režim veľkosti',
      PrintConv => {
        'Size Known' => 'Známa veľkosť',
        'Size Not Known' => 'Veľkosť neznáma',
      },
    },
   'Smoothness' => 'Hladkosť',
   'Software' => 'Softvér',
   'SonyCropSize' => 'Veľkosť orezu Sony',
   'SonyCropTopLeft' => 'Orez Sony vľavo hore',
   'SonyRawFileType' => {
      Description => 'Typ súboru Sony Raw',
      PrintConv => {
        'Sony Compressed RAW' => 'Sony komprimovaný RAW',
        'Sony Lossless Compressed RAW' => 'Sony bezstratový komprimovaný RAW',
        'Sony Lossless Compressed RAW 2' => 'Sony bezstratový komprimovaný RAW 2',
        'Sony Uncompressed 12-bit RAW' => 'Sony nekomprimovaný 12-bitový RAW',
        'Sony Uncompressed 14-bit RAW' => 'Sony nekomprimovaný 14-bitový RAW',
      },
    },
   'SonyToneCurve' => 'Tónová krivka Sony',
   'Source' => 'Zdroj',
   'SpatialFrequencyResponse' => 'Priestorový frekvenčný rozsah',
   'SpecialInstructions' => 'Špeciálne pokyny',
   'SpectralSensitivity' => 'Spektrálna citlivosť',
   'StandardOutputSensitivity' => 'Štandardná výstupná citlivosť',
   'StoNits' => 'Intenzita osvetlenia (konverzný faktor cd/m2)',
   'StripByteCounts' => 'Počet bajtov na pruh',
   'StripOffsets' => 'Korekcia pásov',
   'StripRowCounts' => 'Počet riadkov v páse',
   'Sub-location' => 'Poloha v meste',
   'SubFile' => 'Podsúbor',
   'SubSecTime' => 'Čas úprav v milisekundách',
   'SubSecTimeDigitized' => 'Čas vytvorenia v milisekundách',
   'SubSecTimeOriginal' => 'Čas snímania v milisekundách',
   'SubTileBlockSize' => 'Veľkosť bloku pod dlaždicou',
   'SubfileType' => {
      Description => 'Typ podsúboru',
      PrintConv => {
        'Alternate reduced-resolution image' => 'Alternatívny obrázok so zníženým rozlíšením',
        'Depth map' => 'Hĺbková mapa',
        'Depth map of reduced-resolution image' => 'Hĺbková mapa obrázku so zníženým rozlíšením',
        'Enhanced image data' => 'Vylepšené údaje obrázku',
        'Full-resolution image' => 'Obrázok v plnom rozlíšení',
        'Reduced-resolution image' => 'Obrázok so zníženým rozlíšením',
        'Semantic Mask' => 'Sémantická maska',
        'Single page of multi-page image' => 'Jedna strana viacstranového obrázku',
        'Single page of multi-page reduced-resolution image' => 'Jedna strana viacstranového obrázku so zníženým rozlíšením',
        'Transparency mask' => 'Maska priehľadnosti',
        'Transparency mask of multi-page image' => 'Maska priehľadnosti viacstránkového obrázku',
        'Transparency mask of reduced-resolution image' => 'Maska priehľadnosti obrázok so zníženým rozlíšením',
        'Transparency mask of reduced-resolution multi-page image' => 'Maska priehľadnosti viacstránkového obrázku so zníženým rozlíšením',
        'invalid' => 'neplatný',
      },
    },
   'SubjectArea' => 'Oblasť objektu',
   'SubjectDistance' => 'Vzdialenosť k zaostrenému objektu',
   'SubjectDistanceRange' => {
      Description => 'Vzdialenosť od snímaného objektu',
      PrintConv => {
        'Close' => 'Blízky',
        'Distant' => 'Vzdialený',
        'Macro' => 'Makro',
        'Unknown' => 'Neznámy',
      },
    },
   'SubjectLocation' => 'Umiestnenie predmetu',
   'SubjectReference' => 'Odkaz na tému',
   'SupplementalCategories' => 'Doplnkové kategórie',
   'SupplementalType' => {
      Description => 'Typ prídavku',
      PrintConv => {
        'Main Image' => 'Hlavný obrázok',
        'Rasterized Caption' => 'Rastrovaný titulok',
        'Reduced Resolution Image' => 'Obrázok so zníženým rozlíšením',
      },
    },
   'T4Options' => {
      Description => 'Voľby T4',
      PrintConv => {
        '2-Dimensional encoding' => 'Dvojrozmerné kódovanie',
        'Fill bits added' => 'Pridanie bitov výplne',
        'Uncompressed' => 'Nekomprimované',
      },
    },
   'T6Options' => {
      Description => 'Voľby T6',
      PrintConv => {
        'Uncompressed' => 'Nekomprimované',
      },
    },
   'T82Options' => 'Voľby T82',
   'T88Options' => 'Voľby T88',
   'TIFF-EPStandardID' => 'Štandardné ID TIFF-EP',
   'TIFF_FXExtensions' => {
      Description => 'Rozšírenia TIFF FX',
      PrintConv => {
        'B&W JBIG2' => 'ČB JBIG2',
        'JBIG2 Profile M' => 'Profil JBIG2 M',
        'N Layer Profile M' => 'N profil vrstvy M',
        'Resolution/Image Width' => 'Rozlíšenie/šírka obrazu',
        'Shared Data' => 'Zdieľaný dátový',
      },
    },
   'TStop' => 'Prenosový index objektívu (T-stop)',
   'TargetPrinter' => 'Cieľová tlačiareň',
   'TextureFormat' => 'Formát textúry',
   'Thresholding' => {
      Description => 'Prahová úprava',
      PrintConv => {
        'No dithering or halftoning' => 'Žiadne rozostrenie alebo polotónovanie',
        'Ordered dither or halftone' => 'Usporiadaný rozklad alebo poltón',
        'Randomized dither' => 'Náhodný rozklad',
      },
    },
   'ThumbnailImage' => 'Miniatúra obrázku',
   'ThumbnailLength' => 'Dĺžka miniatúry',
   'ThumbnailOffset' => 'Posun miniatúry',
   'TileByteCounts' => 'Počet bajtov v dlaždici',
   'TileDepth' => 'Hĺbka dlaždice',
   'TileLength' => 'Dĺžka dlaždice',
   'TileOffsets' => 'Posunutie dlaždíc',
   'TileWidth' => 'Šírka dlaždice',
   'TimeCodes' => 'Časové kódy',
   'TimeCreated' => 'Čas vytvorenia',
   'TimeSent' => 'Čas odoslania',
   'TimeZoneOffset' => 'Posun časového pásma',
   'TransferFunction' => 'Prenosová funkcia',
   'TransferRange' => 'Rozsah prenosovej funkcie',
   'Transformation' => {
      Description => 'Transformácia',
      PrintConv => {
        'Horizontal (normal)' => 'Horizontálne (normálne)',
        'Mirror horizontal' => 'Zrkadlené horizontálne ',
        'Mirror horizontal and rotate 270 CW' => 'Zrkadlené horizontálne a otočené 270° doprava',
        'Mirror horizontal and rotate 90 CW' => 'Zrkadlené horizontálne a otočené 90° doprava',
        'Mirror vertical' => 'Zrkadlené vertikálne',
        'Rotate 180' => 'Otočené o 180°',
        'Rotate 270 CW' => 'Otočené o 270° doprava',
        'Rotate 90 CW' => 'Otočené o 90° doprava',
      },
    },
   'TransparencyIndicator' => 'Ukazovateľ priehľadnosti',
   'TrapIndicator' => 'Indikátor zachytenia',
   'UIC1Tag' => 'Tag UIC1',
   'UIC2Tag' => 'Tag UIC2',
   'UIC3Tag' => 'Tag UIC3',
   'UIC4Tag' => 'Tag UIC4',
   'USPTOMiscellaneous' => 'Rôzne USPTO',
   'USPTOOriginalContentType' => {
      Description => 'USPTO - Typ pôvodného obsahu',
      PrintConv => {
        'Color' => 'Farba',
        'Grayscale' => 'Odtiene šedej',
        'Text or Drawing' => 'Text alebo kresba',
      },
    },
   'Uncompressed' => {
      Description => 'Nekomprimovaný',
      PrintConv => {
        'No' => 'Nie',
        'Yes' => 'Áno',
      },
    },
   'UniqueCameraModel' => 'Jedinečný model fotoaparátu',
   'UniqueDocumentID' => 'Jedinečný identifikátor dokumentu',
   'UniqueObjectName' => 'Jedinečný názov objektu',
   'Urgency' => {
      Description => 'Priorita spracovania',
      PrintConv => {
        '0 (reserved)' => '0 (rezervované)',
        '1 (most urgent)' => '1 (najnaliehavejšie)',
        '5 (normal urgency)' => '5 (bežná naliehavosť)',
        '8 (least urgent)' => '8 (najmenej naliehavé)',
        '9 (user-defined priority)' => '9 (priorita definovaná užívateľom)',
      },
    },
   'UserComment' => 'Komentár užívateľa',
   'VersionYear' => 'Rok verzie',
   'VignettingCorrParams' => 'Parametre korekcie vinetácie',
   'VignettingCorrection' => {
      Description => 'Korekcia vinetácie',
      PrintConv => {
        'Auto' => 'Automatická',
        'Auto (ILCE-1)' => 'Automatická (ILCE-1)',
        'No correction params available' => 'Nie sú dostupné žiadne parametre korekcie',
        'Off' => 'Vypnutá',
      },
    },
   'WBBlueLevel' => 'Vyváženie bielej farby - Úroveň modrej',
   'WBGreenLevel' => 'Vyváženie bielej farby - Úroveň zelenej',
   'WBRedLevel' => 'Vyváženie bielej farby - Úroveň červenej',
   'WB_GRGBLevels' => 'Úrovne vyváženia bielej GRGB',
   'WangAnnotation' => 'Wangová anotácia',
   'WangTag1' => 'Wangová značka 1',
   'WangTag3' => 'Wangová značka 3',
   'WangTag4' => 'Wangová značka 4',
   'WarpQuadrilateral' => 'Deformácia štvoruholníka',
   'WaterDepth' => 'Hĺbka vody',
   'WhiteBalance' => {
      Description => 'Vyváženie bielej',
      PrintConv => {
        'Auto' => 'Automatické',
        'Manual' => 'Manuálne',
      },
    },
   'WhiteLevel' => 'Úroveň bielej',
   'WhitePoint' => 'Farba bieleho bodu',
   'WidthResolution' => 'Rozlíšenie na šírku (PPI)',
   'WrapModes' => 'Režimy balenia',
   'Writer-Editor' => 'Spisovateľ-Redaktor',
   'XClipPathUnits' => 'Orezanie obrysových jednotiek X',
   'XPAuthor' => 'XP - Autor',
   'XPComment' => 'XP - Komentár',
   'XPKeywords' => 'XP - Kľúčová slová',
   'XPSubject' => 'XP - Téma',
   'XPTitle' => 'XP - Názov',
   'XPosition' => 'Pozícia obrázku X',
   'XResolution' => 'Horizontálne rozlíšenie',
   'YCbCrCoefficients' => 'Konverzné faktory Y Cb Cr',
   'YCbCrPositioning' => {
      Description => 'Umiestnenie Y Cb Cr',
      PrintConv => {
        'Centered' => 'Vycentrované',
        'Co-sited' => 'Spoločné umiestnenie',
      },
    },
   'YCbCrSubSampling' => 'Faktor čiastkového vzorkovania Y Cb Cr',
   'YClipPathUnits' => 'Orezanie obrysových jednotiek Y',
   'YPosition' => 'Pozícia obrázku Y',
   'YResolution' => 'Vertikálne rozlíšenie',
);

1;  # end

__END__

=head1 NAME

Image::ExifTool::Lang::sk.pm - ExifTool Slovak language translations

=head1 DESCRIPTION

This file is used by Image::ExifTool to generate localized tag descriptions
and values.

=head1 AUTHOR

Copyright 2003-2025, Phil Harvey (philharvey66 at gmail.com)

This library is free software; you can redistribute it and/or modify it
under the same terms as Perl itself.

=head1 ACKNOWLEDGEMENTS

Thanks to Peter Bagin for providing this translation.

=head1 SEE ALSO

L<Image::ExifTool(3pm)|Image::ExifTool>,
L<Image::ExifTool::TagInfoXML(3pm)|Image::ExifTool::TagInfoXML>

=cut
