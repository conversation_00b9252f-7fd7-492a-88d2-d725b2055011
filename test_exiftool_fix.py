#!/usr/bin/env python3
"""
Test script to verify ExifTool fix
"""

import os
import sys
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from services.metadata_service import MetadataService

def test_exiftool():
    print("Testing ExifTool fix...")
    
    # Create metadata service instance
    service = MetadataService()
    
    # Test ExifTool path detection
    exiftool_path = service._get_exiftool_path()
    print(f"ExifTool path: {exiftool_path}")
    
    if exiftool_path:
        print("✓ ExifTool found and validated")
        
        # Check if alternative command is being used
        if hasattr(service, '_exiftool_alt_cmd') and service._exiftool_alt_cmd:
            print(f"✓ Using alternative command: {service._exiftool_alt_cmd}")
        else:
            print("✓ Using direct executable")
            
        return True
    else:
        print("✗ ExifTool not found or validation failed")
        return False

if __name__ == "__main__":
    success = test_exiftool()
    sys.exit(0 if success else 1)
