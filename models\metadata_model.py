"""
Metadata model for handling file metadata information
"""

class MetadataModel:
    def __init__(self):
        self.file_path = ""
        self.file_type = ""
        self.metadata = {}
        self.supported_formats = {
            'image': ['.jpg', '.jpeg', '.png', '.tiff', '.tif', '.bmp', '.gif'],
            'video': ['.mp4', '.avi', '.mov', '.mkv', '.wmv', '.flv', '.webm'],
            'audio': ['.mp3', '.flac', '.ogg', '.m4a', '.wav']
        }
    
    def set_file_path(self, path):
        self.file_path = path
        self.file_type = self._detect_file_type(path)
    
    def _detect_file_type(self, path):
        extension = path.lower().split('.')[-1] if '.' in path else ''
        extension = '.' + extension
        
        for file_type, extensions in self.supported_formats.items():
            if extension in extensions:
                return file_type
        return 'unknown'
    
    def set_metadata(self, metadata_dict):
        self.metadata = metadata_dict.copy()
    
    def get_metadata(self):
        return self.metadata.copy()
    
    def update_metadata(self, key, value):
        self.metadata[key] = value
    
    def remove_metadata(self, key):
        if key in self.metadata:
            del self.metadata[key]
    
    def is_supported_format(self):
        return self.file_type != 'unknown'
    
    def get_file_info(self):
        return {
            'path': self.file_path,
            'type': self.file_type,
            'supported': self.is_supported_format()
        }
