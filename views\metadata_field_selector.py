"""
Metadata field selector dialog for choosing metadata fields from templates
"""

import tkinter as tk
from tkinter import ttk
from services.metadata_template_service import MetadataTemplateService

class MetadataFieldSelector:
    def __init__(self, parent, theme_service, file_type="unknown"):
        self.parent = parent
        self.theme_service = theme_service
        self.file_type = file_type
        self.template_service = MetadataTemplateService()
        self.selected_fields = []
        self.result = None
        
        self.create_dialog()
    
    def create_dialog(self):
        # Create dialog window
        self.dialog = tk.Toplevel(self.parent)
        self.dialog.title("Add Metadata Field")
        self.dialog.geometry("500x600")
        self.dialog.resizable(True, True)
        self.dialog.transient(self.parent)
        self.dialog.grab_set()
        
        # Center dialog
        self.center_dialog()
        
        # Main frame
        self.main_frame = tk.Frame(self.dialog)
        self.main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Title
        title_label = tk.Label(
            self.main_frame,
            text="Select Metadata Fields to Add",
            font=("Arial", 14, "bold")
        )
        title_label.pack(pady=(0, 10))
        
        # File type info
        type_label = tk.Label(
            self.main_frame,
            text=f"File Type: {self.file_type.title()}",
            font=("Arial", 10)
        )
        type_label.pack(pady=(0, 10))
        
        # Search frame
        search_frame = tk.Frame(self.main_frame)
        search_frame.pack(fill=tk.X, pady=(0, 10))
        
        tk.Label(search_frame, text="Search:", font=("Arial", 10)).pack(side=tk.LEFT)
        
        self.search_var = tk.StringVar()
        self.search_entry = tk.Entry(
            search_frame, 
            textvariable=self.search_var,
            font=("Arial", 10)
        )
        self.search_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(5, 0))
        self.search_var.trace('w', self.on_search_changed)
        
        # Selected fields frame (create before tabs)
        selected_frame = tk.LabelFrame(self.main_frame, text="Selected Fields", font=("Arial", 10, "bold"))
        selected_frame.pack(fill=tk.X, pady=(0, 10))

        self.selected_listbox = tk.Listbox(selected_frame, height=4, font=("Arial", 9))
        self.selected_listbox.pack(fill=tk.X, padx=5, pady=5)
        self.selected_listbox.bind('<Double-Button-1>', self.remove_selected_field)

        # Tab frame for categories
        self.notebook = ttk.Notebook(self.main_frame)
        self.notebook.pack(fill=tk.BOTH, expand=True, pady=(0, 10))

        # Create popular tab first
        self.create_popular_tab()

        # Create tabs for different categories
        self.create_category_tabs()
        
        # Buttons frame
        button_frame = tk.Frame(self.main_frame)
        button_frame.pack(fill=tk.X)
        
        self.cancel_button = tk.Button(
            button_frame,
            text="Cancel",
            command=self.cancel,
            font=("Arial", 10),
            width=10
        )
        self.cancel_button.pack(side=tk.RIGHT, padx=(5, 0))
        
        self.ok_button = tk.Button(
            button_frame,
            text="Add Fields",
            command=self.ok,
            font=("Arial", 10),
            width=12
        )
        self.ok_button.pack(side=tk.RIGHT)
        
        self.clear_button = tk.Button(
            button_frame,
            text="Clear All",
            command=self.clear_selected,
            font=("Arial", 10),
            width=10
        )
        self.clear_button.pack(side=tk.LEFT)
        
        # Select popular tab by default
        self.notebook.select(0)

        # Apply theme
        self.apply_theme()

        # Focus on search entry
        self.search_entry.focus()
    
    def create_category_tabs(self):
        templates = self.template_service.get_templates_for_file_type(self.file_type)
        
        self.category_listboxes = {}
        
        for category, fields in templates.items():
            # Create tab frame
            tab_frame = tk.Frame(self.notebook)
            self.notebook.add(tab_frame, text=category)
            
            # Create listbox for this category
            listbox_frame = tk.Frame(tab_frame)
            listbox_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
            
            listbox = tk.Listbox(listbox_frame, font=("Arial", 9))
            scrollbar = ttk.Scrollbar(listbox_frame, orient=tk.VERTICAL, command=listbox.yview)
            listbox.configure(yscrollcommand=scrollbar.set)
            
            listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
            scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
            
            # Populate listbox
            for field_name in sorted(fields.keys()):
                listbox.insert(tk.END, field_name)
            
            # Bind events
            listbox.bind('<Double-Button-1>', lambda e, lb=listbox: self.add_field_from_listbox(lb))
            listbox.bind('<Return>', lambda e, lb=listbox: self.add_field_from_listbox(lb))
            
            self.category_listboxes[category] = listbox
    
    def create_popular_tab(self):
        # Popular fields tab
        popular_frame = tk.Frame(self.notebook)
        self.notebook.add(popular_frame, text="⭐ Popular")
        
        listbox_frame = tk.Frame(popular_frame)
        listbox_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        self.popular_listbox = tk.Listbox(listbox_frame, font=("Arial", 9))
        scrollbar = ttk.Scrollbar(listbox_frame, orient=tk.VERTICAL, command=self.popular_listbox.yview)
        self.popular_listbox.configure(yscrollcommand=scrollbar.set)
        
        self.popular_listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # Populate with popular fields
        popular_fields = self.template_service.get_popular_fields_for_file_type(self.file_type)
        for field in popular_fields:
            self.popular_listbox.insert(tk.END, field)
        
        # Bind events
        self.popular_listbox.bind('<Double-Button-1>', lambda e: self.add_field_from_listbox(self.popular_listbox))
        self.popular_listbox.bind('<Return>', lambda e: self.add_field_from_listbox(self.popular_listbox))
    
    def on_search_changed(self, *args):
        search_term = self.search_var.get()
        suggestions = self.template_service.get_field_suggestions(self.file_type, search_term)
        
        # Update all listboxes to show only matching fields
        for category, listbox in self.category_listboxes.items():
            listbox.delete(0, tk.END)
            templates = self.template_service.get_templates_for_file_type(self.file_type)
            
            if category in templates:
                for field_name in sorted(templates[category].keys()):
                    if not search_term or search_term.lower() in field_name.lower():
                        listbox.insert(tk.END, field_name)
        
        # Update popular listbox
        self.popular_listbox.delete(0, tk.END)
        popular_fields = self.template_service.get_popular_fields_for_file_type(self.file_type)
        for field in popular_fields:
            if not search_term or search_term.lower() in field.lower():
                self.popular_listbox.insert(tk.END, field)
    
    def add_field_from_listbox(self, listbox):
        selection = listbox.curselection()
        if selection:
            field_name = listbox.get(selection[0])
            if field_name not in self.selected_fields:
                self.selected_fields.append(field_name)
                self.update_selected_display()
    
    def remove_selected_field(self, event):
        selection = self.selected_listbox.curselection()
        if selection:
            field_name = self.selected_listbox.get(selection[0])
            if field_name in self.selected_fields:
                self.selected_fields.remove(field_name)
                self.update_selected_display()
    
    def update_selected_display(self):
        self.selected_listbox.delete(0, tk.END)
        for field in self.selected_fields:
            self.selected_listbox.insert(tk.END, field)
    
    def clear_selected(self):
        self.selected_fields.clear()
        self.update_selected_display()
    
    def ok(self):
        self.result = self.selected_fields.copy()
        self.dialog.destroy()
    
    def cancel(self):
        self.result = None
        self.dialog.destroy()
    
    def center_dialog(self):
        self.dialog.update_idletasks()
        width = self.dialog.winfo_width()
        height = self.dialog.winfo_height()
        x = self.parent.winfo_x() + (self.parent.winfo_width() // 2) - (width // 2)
        y = self.parent.winfo_y() + (self.parent.winfo_height() // 2) - (height // 2)
        self.dialog.geometry(f"{width}x{height}+{x}+{y}")
    
    def apply_theme(self):
        # Apply theme to dialog components
        self.theme_service.apply_theme_to_widget(self.dialog, 'frame')
        self.theme_service.apply_theme_to_widget(self.main_frame, 'frame')
        self.theme_service.apply_theme_to_widget(self.search_entry, 'entry')
        self.theme_service.apply_theme_to_widget(self.selected_listbox, 'listbox')
        self.theme_service.apply_theme_to_widget(self.popular_listbox, 'listbox')

        # Apply theme to category listboxes
        for listbox in self.category_listboxes.values():
            self.theme_service.apply_theme_to_widget(listbox, 'listbox')
        
        # Apply theme to buttons
        for button in [self.ok_button, self.cancel_button, self.clear_button]:
            self.theme_service.apply_theme_to_widget(button, 'button_secondary')
    
    def show(self):
        self.dialog.wait_window()
        return self.result
