# Panduan Penggunaan Gema

## Instalasi dan Setup

### 1. Persiapan Environment
```bash
# Pastikan Python 3.7+ terinstall
python --version

# Clone atau download aplikasi
# Masuk ke direktori aplikasi
cd Gema
```

### 2. Install Dependencies
```bash
# Otomatis (Windows)
run_gema.bat

# Manual
pip install Pillow mutagen exifread
```

### 3. Menjalankan Aplikasi
```bash
# Langsung
python main.py

# Atau test terlebih dahulu
python test_app.py
```

## Interface Aplikasi

### Header
- **Judul**: Menampilkan nama aplikasi "Gema"
- **Toggle Theme**: Tombol 🌙/☀️ untuk beralih antara tema terang dan gelap

### Tab Navigation
1. **📁 File Browser**: Browse dan pilih file media
2. **✏️ Metadata Editor**: Edit metadata file yang dipilih
3. **📦 Batch Editor**: Edit metadata multiple files (coming soon)

## File Browser Tab

### Fitur:
- **Path Navigation**: Input path manual atau browse folder
- **Navigation Buttons**:
  - ⬆️ Up: Naik ke direktori parent
  - 🏠 Home: Kembali ke home directory
  - 🔄 Refresh: Refresh direktori saat ini
- **File List**: Menampilkan file yang didukung dengan informasi size dan type
- **Thumbnail Preview**: Preview gambar dan info file saat dipilih
- **Auto-save Path**: Direktori terakhir tersimpan otomatis
- **Recent Files**: File yang dibuka tersimpan dalam history
- **Double-click**: Buka file di Metadata Editor

### Supported Formats:
- **Images**: JPG, JPEG, PNG, TIFF, TIF, BMP, GIF
- **Videos**: MP4, AVI, MOV, MKV, WMV, FLV, WEBM
- **Audio**: MP3, FLAC, OGG, M4A, WAV

## Metadata Editor Tab

### Fitur:
- **File Info**: Menampilkan nama, type, dan size file
- **Action Buttons**:
  - 📂 Load File: Load file manual
  - 📖 Read Metadata: Baca metadata dari file
  - 💾 Save Metadata: Simpan metadata ke file
  - 🗑️ Clear All: Hapus semua field metadata
  - ⚡ Quick Add: Tambah field populer untuk tipe file
  - ➕ Add Field: Pilih field dari template yang tersedia

### Workflow:
1. Load file (otomatis dari File Browser atau manual)
2. Read metadata existing (otomatis)
3. Edit atau tambah field metadata
4. Save metadata ke file

### Field Metadata:
- **Key**: Nama field metadata (dengan tooltip deskripsi)
- **Value**: Nilai metadata (dengan validasi real-time)
- **Delete**: Tombol ❌ untuk hapus field

### Template Field System:
- **Quick Add**: Menambah field populer sesuai tipe file
- **Add Field Dialog**:
  - Tab "⭐ Popular": Field yang sering digunakan
  - Tab kategori: Field berdasarkan kategori (Basic Info, Technical, dll)
  - Search: Cari field berdasarkan nama
  - Multiple selection: Pilih beberapa field sekaligus
- **Field Validation**: Visual feedback untuk nilai yang tidak valid
- **Tooltips**: Hover pada field key untuk melihat deskripsi

## Tema dan Kustomisasi

### Tema Terang (Default)
- Primary: #d1c2ed (ungu muda)
- Background: #ffffff (putih)
- Text: #2d1b3d (gelap)

### Tema Gelap
- Primary: #8a6dc1 (ungu gelap)
- Background: #1a1625 (gelap)
- Text: #e6ddf4 (terang)

### Toggle Theme:
Klik tombol 🌙/☀️ di header untuk beralih tema

## Tips Penggunaan

### 1. Navigasi Cepat
- Gunakan keyboard shortcuts:
  - Enter: Buka file/folder di File Browser
  - Double-click: Aksi cepat

### 2. Metadata Editing
- Field kosong tidak akan disimpan
- Gunakan nama field yang standar untuk kompatibilitas
- Backup file penting sebelum edit metadata

### 3. Format Support
- EXIF data untuk gambar (JPG, TIFF)
- ID3 tags untuk audio (MP3)
- Container metadata untuk video (MP4, MKV)

## Troubleshooting

### Error: Module not found
```bash
pip install Pillow mutagen exifread
```

### Error: Permission denied
- Pastikan file tidak sedang dibuka di aplikasi lain
- Run sebagai administrator jika perlu

### Error: Unsupported format
- Cek format file di daftar supported formats
- Beberapa format mungkin read-only

### Interface tidak responsif
- Restart aplikasi
- Cek file size (file besar mungkin butuh waktu loading)

## Keyboard Shortcuts

- **Ctrl+O**: Load file (di Metadata Editor)
- **Ctrl+S**: Save metadata (di Metadata Editor)
- **Ctrl+R**: Refresh (di File Browser)
- **F5**: Refresh current view
- **Escape**: Clear selection

## Advanced Usage

### Custom Metadata Fields
Anda bisa menambah field metadata custom:
- Artist, Album, Title untuk audio
- Camera, GPS, DateTime untuk gambar
- Duration, Codec, Resolution untuk video

### Batch Operations
Fitur batch editing akan tersedia di update mendatang untuk:
- Edit metadata multiple files sekaligus
- Apply template metadata
- Bulk rename berdasarkan metadata

## Support

Untuk bantuan lebih lanjut:
1. Baca dokumentasi di README.md
2. Cek error messages di console
3. Test dengan file sample kecil terlebih dahulu
