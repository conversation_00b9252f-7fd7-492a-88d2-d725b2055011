"""
Theme service for managing application themes
"""

import tkinter as tk
from config.themes import ThemeConfig

class ThemeService:
    def __init__(self):
        self.current_theme = 'light'
        self.theme_data = ThemeConfig.get_theme(self.current_theme)
        self.observers = []
    
    def add_observer(self, callback):
        self.observers.append(callback)
    
    def remove_observer(self, callback):
        if callback in self.observers:
            self.observers.remove(callback)
    
    def notify_observers(self):
        for callback in self.observers:
            callback(self.theme_data)
    
    def toggle_theme(self):
        self.current_theme = 'dark' if self.current_theme == 'light' else 'light'
        self.theme_data = ThemeConfig.get_theme(self.current_theme)
        self.notify_observers()
    
    def set_theme(self, theme_name):
        if theme_name in ['light', 'dark']:
            self.current_theme = theme_name
            self.theme_data = ThemeConfig.get_theme(self.current_theme)
            self.notify_observers()
    
    def get_current_theme(self):
        return self.current_theme
    
    def get_theme_data(self):
        return self.theme_data
    
    def apply_theme_to_widget(self, widget, widget_type='default'):
        theme = self.theme_data
        
        if widget_type == 'button':
            widget.configure(
                bg=theme['primary'],
                fg=theme['text_primary'],
                activebackground=theme['hover'],
                activeforeground=theme['text_primary'],
                relief='flat',
                borderwidth=1,
                highlightthickness=0
            )
        elif widget_type == 'button_secondary':
            widget.configure(
                bg=theme['surface'],
                fg=theme['text_secondary'],
                activebackground=theme['hover'],
                activeforeground=theme['text_primary'],
                relief='flat',
                borderwidth=1,
                highlightthickness=0
            )
        elif widget_type == 'entry':
            widget.configure(
                bg=theme['surface'],
                fg=theme['text_primary'],
                insertbackground=theme['text_primary'],
                relief='flat',
                borderwidth=1,
                highlightthickness=1,
                highlightcolor=theme['primary']
            )
        elif widget_type == 'text':
            widget.configure(
                bg=theme['surface'],
                fg=theme['text_primary'],
                insertbackground=theme['text_primary'],
                relief='flat',
                borderwidth=1,
                highlightthickness=1,
                highlightcolor=theme['primary']
            )
        elif widget_type == 'listbox':
            widget.configure(
                bg=theme['surface'],
                fg=theme['text_primary'],
                selectbackground=theme['primary'],
                selectforeground=theme['text_primary'],
                relief='flat',
                borderwidth=1,
                highlightthickness=1,
                highlightcolor=theme['primary']
            )
        elif widget_type == 'treeview':
            # For ttk.Treeview, we need to use ttk.Style
            try:
                import tkinter.ttk as ttk
                style = ttk.Style()

                # Configure treeview style
                style.configure("Themed.Treeview",
                    background=theme['surface'],
                    foreground=theme['text_primary'],
                    fieldbackground=theme['surface'],
                    borderwidth=1,
                    relief='flat'
                )

                style.configure("Themed.Treeview.Heading",
                    background=theme['primary'],
                    foreground=theme['text_primary'],
                    borderwidth=1,
                    relief='flat'
                )

                # Apply the style to the widget
                widget.configure(style="Themed.Treeview")
            except:
                # Fallback if ttk styling fails
                pass
        elif widget_type == 'frame':
            widget.configure(
                bg=theme['background'],
                relief='flat',
                borderwidth=0
            )
        elif widget_type == 'label':
            widget.configure(
                bg=theme['background'],
                fg=theme['text_primary']
            )
        elif widget_type == 'label_secondary':
            widget.configure(
                bg=theme['background'],
                fg=theme['text_secondary']
            )
        else:
            widget.configure(bg=theme['background'])
